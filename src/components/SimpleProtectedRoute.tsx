import React from 'react';
import { Navigate, useParams } from 'react-router-dom';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { School } from 'lucide-react';

interface SimpleProtectedRouteProps {
  children: React.ReactNode;
}

export const SimpleProtectedRoute: React.FC<SimpleProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useSimpleAuth();
  const { schoolSlug } = useParams<{ schoolSlug: string }>();

  console.log('🔒 SimpleProtectedRoute state:', { user: !!user, loading, schoolSlug });

  // TEMPORARY: Bypass authentication for testing
  const bypassAuth = false; // Disabled since we have mock auth

  if (bypassAuth) {
    console.log('🔒 SimpleProtectedRoute: BYPASSING AUTH FOR TESTING');
    return <>{children}</>;
  }

  if (loading) {
    console.log('🔒 SimpleProtectedRoute: Still loading...');
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <School className="h-16 w-16 text-blue-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Loading authentication...</p>
          <p className="text-sm text-gray-400 mt-2">User: {user ? 'Found' : 'Not found'}</p>
          <button
            onClick={() => window.location.href = `/${schoolSlug}/dashboard`}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
          >
            Skip to Dashboard (Debug)
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    console.log('🔒 SimpleProtectedRoute: No user, redirecting to login');
    return <Navigate to={`/${schoolSlug}/login`} replace />;
  }

  console.log('🔒 SimpleProtectedRoute: User authenticated, rendering children');
  return <>{children}</>;
};
