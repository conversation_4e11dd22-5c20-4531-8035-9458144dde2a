import React from 'react';
import { Link, useLocation, useParams, useNavigate } from 'react-router-dom';
import { useCleanAuth } from '@/contexts/CleanAuthContext';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Users,
  GraduationCap,
  Heart,
  BookOpen,
  Building2,
  ClipboardList,
  Calendar,
  FileText,
  Award,
  BarChart3,
  DollarSign,
  MessageSquare,
  Bell,
  Settings,
  UserCog,
  Shield,
  LogOut
} from 'lucide-react';

interface SimpleLayoutProps {
  children: React.ReactNode;
}

// Role-based navigation items
const allNavigationItems = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    page: "dashboard",
    roles: ["admin", "teacher", "parent", "student"]
  },
  {
    name: "Students",
    href: "/students",
    icon: Users,
    page: "students",
    roles: ["admin"]
  },
  {
    name: "My Students",
    href: "/students",
    icon: Users,
    page: "students",
    roles: ["teacher"]
  },
  {
    name: "Teachers",
    href: "/teachers",
    icon: GraduationCap,
    page: "teachers",
    roles: ["admin"]
  },
  {
    name: "Parents",
    href: "/parents",
    icon: Heart,
    page: "parents",
    roles: ["admin"]
  },
  {
    name: "Classes",
    href: "/classes",
    icon: BookOpen,
    page: "classes",
    roles: ["admin", "parent"]
  },
  {
    name: "My Classes",
    href: "/classes",
    icon: BookOpen,
    page: "classes",
    roles: ["teacher"]
  },
  {
    name: "Subjects",
    href: "/subjects",
    icon: BookOpen,
    page: "subjects",
    roles: ["admin", "teacher"]
  },
  {
    name: "Departments",
    href: "/departments",
    icon: Building2,
    page: "departments",
    roles: ["admin"]
  },
  {
    name: "Attendance",
    href: "/attendance",
    icon: ClipboardList,
    page: "attendance",
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "Timetable",
    href: "/timetable",
    icon: Calendar,
    page: "timetable",
    roles: ["admin"]
  },
  {
    name: "My Timetable",
    href: "/teacher-timetable",
    icon: Calendar,
    page: "teacher-timetable",
    roles: ["teacher"]
  },
  {
    name: "My Timetable",
    href: "/student-timetable",
    icon: Calendar,
    page: "student-timetable",
    roles: ["student"]
  },
  {
    name: "Assignments",
    href: "/assignments",
    icon: FileText,
    page: "assignments",
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "My Assignments",
    href: "/student-assignments",
    icon: FileText,
    page: "student-assignments",
    roles: ["student"]
  },
  {
    name: "Examinations",
    href: "/examinations",
    icon: Award,
    page: "examinations",
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "My Examinations",
    href: "/student-examinations",
    icon: Award,
    page: "student-examinations",
    roles: ["student"]
  },
  {
    name: "Grades",
    href: "/grades",
    icon: BarChart3,
    page: "grades",
    roles: ["admin", "teacher"]
  },
  {
    name: "Results",
    href: "/results",
    icon: BarChart3,
    page: "results",
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "My Results",
    href: "/student-results",
    icon: BarChart3,
    page: "student-results",
    roles: ["student"]
  },
  {
    name: "Fees",
    href: "/fees",
    icon: DollarSign,
    page: "fees",
    roles: ["admin", "parent", "student"]
  },
  {
    name: "Communications",
    href: "/communications",
    icon: MessageSquare,
    page: "communications",
    roles: ["admin", "teacher", "parent", "student"]
  },
  {
    name: "Reports",
    href: "/reports",
    icon: FileText,
    page: "reports",
    roles: ["admin", "teacher"]
  },
  {
    name: "Notifications",
    href: "/notifications",
    icon: Bell,
    page: "notifications",
    roles: ["admin"]
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    page: "settings",
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "User Management",
    href: "/user-management",
    icon: UserCog,
    page: "user-management",
    roles: ["admin"]
  },
];

export const SimpleLayout: React.FC<SimpleLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { user, profile, signOut } = useCleanAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate(`/${schoolSlug}/login`);
  };

  const getInitials = (email: string) => {
    return email.charAt(0).toUpperCase();
  };

  // Filter navigation items based on user role
  const getFilteredNavigation = () => {
    if (!user || !profile) return [];

    // Get user role from profile (from database)
    const userRole = profile.role;

    return allNavigationItems.filter(item => {
      // Show items that include the user's role
      const hasAccess = item.roles.includes(userRole);
      return hasAccess;
    });
  };

  const navigation = getFilteredNavigation();

  return (
    <div className="min-h-screen">
      {/* Desktop Sidebar - Fixed position, hidden on mobile */}
      <div className="hidden md:block">
        <div className="fixed left-0 top-0 h-screen w-64 flex flex-col bg-white border-r border-gray-200">
          {/* Header with custom green background and logo */}
          <div className="flex h-16 items-center px-6 text-white" style={{ backgroundColor: '#007100' }}>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <GraduationCap className="h-8 w-8 text-white" />
                <span className="ml-2 text-xl font-bold text-white">
                  School Management
                </span>
              </div>
              {user && profile && (
                <div className="flex items-center gap-1 bg-white/20 px-2 py-1 rounded-full">
                  <Shield className="h-3 w-3" />
                  <span className="text-xs font-medium capitalize">
                    {profile.role}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Navigation - Scrollable content */}
          <nav className="flex-1 space-y-1 px-3 py-4 overflow-y-auto">
            {navigation.map((item) => {
              const fullHref = `/${schoolSlug}${item.href}`;
              const isActive = location.pathname === fullHref;
              return (
                <Link
                  key={`${item.name}-${item.href}`}
                  to={fullHref}
                  className={cn(
                    "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    isActive
                      ? "bg-green-50 border-r-2"
                      : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                  )}
                  style={isActive ? { color: '#007100', borderRightColor: '#007100' } : {}}
                >
                  <item.icon
                    className={cn(
                      "mr-3 h-5 w-5 flex-shrink-0",
                      isActive ? "" : "text-gray-400 group-hover:text-gray-500"
                    )}
                    style={isActive ? { color: '#007100' } : {}}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User section at bottom */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-700">
                    {getInitials(user?.email || '')}
                  </span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-700 truncate">
                    {user?.email?.split('@')[0]}
                  </p>
                  <p className="text-xs text-gray-500 capitalize">
                    {profile?.role || 'Student'}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-gray-500 hover:text-gray-700"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area with left margin for sidebar on desktop */}
      <div className="md:ml-64 flex flex-col min-h-screen">
        {/* Header for mobile */}
        <div className="md:hidden bg-white shadow-sm border-b">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <GraduationCap className="h-6 w-6 text-green-700" />
                <span className="ml-2 text-lg font-bold text-gray-900">
                  {schoolSlug}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-gray-500"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <main className="flex-1 bg-gray-50">
          {children}
        </main>
      </div>
    </div>
  );
};
