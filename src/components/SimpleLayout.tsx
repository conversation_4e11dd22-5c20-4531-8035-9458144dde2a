import React from 'react';
import { Link, useLocation, useParams, useNavigate } from 'react-router-dom';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Users,
  GraduationCap,
  Heart,
  BookOpen,
  Building2,
  ClipboardList,
  Calendar,
  FileText,
  Award,
  BarChart3,
  DollarSign,
  MessageSquare,
  Bell,
  Settings,
  UserCog,
  Shield,
  LogOut
} from 'lucide-react';

interface SimpleLayoutProps {
  children: React.ReactNode;
}

// Navigation items
const navigationItems = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    name: "Students",
    href: "/students",
    icon: Users,
  },
  {
    name: "Teachers",
    href: "/teachers",
    icon: GraduationCap,
  },
  {
    name: "Parents",
    href: "/parents",
    icon: Heart,
  },
  {
    name: "Classes",
    href: "/classes",
    icon: BookOpen,
  },
  {
    name: "Subjects",
    href: "/subjects",
    icon: BookOpen,
  },
  {
    name: "Departments",
    href: "/departments",
    icon: Building2,
  },
  {
    name: "Attendance",
    href: "/attendance",
    icon: ClipboardList,
  },
  {
    name: "Timetable",
    href: "/timetable",
    icon: Calendar,
  },
  {
    name: "Assignments",
    href: "/assignments",
    icon: FileText,
  },
  {
    name: "Examinations",
    href: "/examinations",
    icon: Award,
  },
  {
    name: "Grades",
    href: "/grades",
    icon: BarChart3,
  },
  {
    name: "Results",
    href: "/results",
    icon: BarChart3,
  },
  {
    name: "Fees",
    href: "/fees",
    icon: DollarSign,
  },
  {
    name: "Communications",
    href: "/communications",
    icon: MessageSquare,
  },
  {
    name: "Reports",
    href: "/reports",
    icon: FileText,
  },
  {
    name: "Notifications",
    href: "/notifications",
    icon: Bell,
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
  },
  {
    name: "User Management",
    href: "/user-management",
    icon: UserCog,
  },
];

export const SimpleLayout: React.FC<SimpleLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { user, signOut } = useSimpleAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate(`/${schoolSlug}/login`);
  };

  const getInitials = (email: string) => {
    return email.charAt(0).toUpperCase();
  };

  return (
    <div className="min-h-screen">
      {/* Desktop Sidebar - Fixed position, hidden on mobile */}
      <div className="hidden md:block">
        <div className="fixed left-0 top-0 h-screen w-64 flex flex-col bg-white border-r border-gray-200">
          {/* Header with custom green background and logo */}
          <div className="flex h-16 items-center px-6 text-white" style={{ backgroundColor: '#007100' }}>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <GraduationCap className="h-8 w-8 text-white" />
                <span className="ml-2 text-xl font-bold text-white">
                  School Management
                </span>
              </div>
              {user && (
                <div className="flex items-center gap-1 bg-white/20 px-2 py-1 rounded-full">
                  <Shield className="h-3 w-3" />
                  <span className="text-xs font-medium">
                    {getInitials(user.email || '')}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Navigation - Scrollable content */}
          <nav className="flex-1 space-y-1 px-3 py-4 overflow-y-auto">
            {navigationItems.map((item) => {
              const fullHref = `/${schoolSlug}${item.href}`;
              const isActive = location.pathname === fullHref;
              return (
                <Link
                  key={item.name}
                  to={fullHref}
                  className={cn(
                    "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    isActive
                      ? "bg-green-50 border-r-2"
                      : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                  )}
                  style={isActive ? { color: '#007100', borderRightColor: '#007100' } : {}}
                >
                  <item.icon
                    className={cn(
                      "mr-3 h-5 w-5 flex-shrink-0",
                      isActive ? "" : "text-gray-400 group-hover:text-gray-500"
                    )}
                    style={isActive ? { color: '#007100' } : {}}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User section at bottom */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-700">
                    {getInitials(user?.email || '')}
                  </span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-700 truncate">
                    {user?.email?.split('@')[0]}
                  </p>
                  <p className="text-xs text-gray-500">Administrator</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-gray-500 hover:text-gray-700"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area with left margin for sidebar on desktop */}
      <div className="md:ml-64 flex flex-col min-h-screen">
        {/* Header for mobile */}
        <div className="md:hidden bg-white shadow-sm border-b">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <GraduationCap className="h-6 w-6 text-green-700" />
                <span className="ml-2 text-lg font-bold text-gray-900">
                  {schoolSlug}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-gray-500"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <main className="flex-1 bg-gray-50">
          {children}
        </main>
      </div>
    </div>
  );
};
