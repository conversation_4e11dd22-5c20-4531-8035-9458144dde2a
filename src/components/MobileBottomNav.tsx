import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Home,
  User,
  Users,
  Book,
  BookOpen,
  Building,
  ClipboardList,
  GraduationCap,
  FileText,
  Calendar,
  Award,
  BarChart3,
  MoreHorizontal,
  DollarSign
} from 'lucide-react';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

interface NavItem {
  title: string;
  url: string;
  icon: any;
}

const navItems: NavItem[] = [
  { title: "Home", url: "/dashboard", icon: Home },
  { title: "Students", url: "/students", icon: User },
  { title: "Teachers", url: "/teachers", icon: Users },
  { title: "Classes", url: "/classes", icon: Book },
  { title: "Subjects", url: "/subjects", icon: BookOpen },
  { title: "Departments", url: "/departments", icon: Building },
  { title: "Attendance", url: "/attendance", icon: ClipboardList },
  { title: "Grades", url: "/grades", icon: GraduationCap },
  { title: "Assignments", url: "/assignments", icon: FileText },
  { title: "Timetable", url: "/timetable", icon: Calendar },
  { title: "Results", url: "/results", icon: Award },
  { title: "Reports", url: "/reports", icon: BarChart3 },
  { title: "Fees", url: "/fees", icon: DollarSign },
];

export const MobileBottomNav: React.FC = () => {
  const location = useLocation();
  const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);

  // Limit to 4 items for mobile (since we have hamburger menu)
  const filteredItems = navItems.slice(0, 4);

  // Get remaining items for "More" menu
  const moreItems = navItems.slice(4);

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
      <nav className="flex justify-around items-center py-2 px-1">
        {filteredItems.map((item) => {
          const isActive = location.pathname === item.url;
          const Icon = item.icon;

          return (
            <NavLink
              key={item.title}
              to={item.url}
              className={`flex flex-col items-center justify-center py-2 px-2 rounded-lg transition-colors min-w-0 flex-1 ${
                isActive
                  ? 'text-primary bg-primary/10'
                  : 'text-gray-600 hover:text-primary hover:bg-gray-50'
              }`}
            >
              <Icon className="h-5 w-5 mb-1" />
              <span className="text-xs font-medium truncate">{item.title}</span>
            </NavLink>
          );
        })}

        {/* More Menu Button */}
        {moreItems.length > 0 && (
          <Sheet open={isMoreMenuOpen} onOpenChange={setIsMoreMenuOpen}>
            <SheetTrigger asChild>
              <button className="flex flex-col items-center justify-center py-2 px-2 rounded-lg transition-colors min-w-0 flex-1 text-gray-600 hover:text-primary hover:bg-gray-50">
                <MoreHorizontal className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">More</span>
              </button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[60vh]">
              <SheetHeader>
                <SheetTitle>More Options</SheetTitle>
              </SheetHeader>
              <div className="grid grid-cols-3 gap-4 mt-6">
                {moreItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = location.pathname === item.url;

                  return (
                    <NavLink
                      key={item.title}
                      to={item.url}
                      onClick={() => setIsMoreMenuOpen(false)}
                      className={`flex flex-col items-center justify-center p-4 rounded-lg transition-colors ${
                        isActive
                          ? 'text-primary bg-primary/10'
                          : 'text-gray-600 hover:text-primary hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="h-6 w-6 mb-2" />
                      <span className="text-sm font-medium text-center">{item.title}</span>
                    </NavLink>
                  );
                })}
              </div>
            </SheetContent>
          </Sheet>
        )}
      </nav>
    </div>
  );
};
