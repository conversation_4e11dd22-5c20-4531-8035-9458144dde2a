import { Link, useLocation, useParams } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useSimpleAuth } from "@/contexts/SimpleAuthContext";
import { useDataAccess } from "@/hooks/useDataAccess";
import {
  LayoutDashboard,
  Users,
  GraduationCap,
  BookOpen,
  Building2,
  ClipboardList,
  Calendar,
  DollarSign,
  MessageSquare,
  FileText,
  Bell,
  Settings,
  UserCog,
  BarChart3,
  Award,
  Shield,
  Heart
} from "lucide-react";

// Complete navigation items with role-based access
const allNavigationItems = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    page: "dashboard",
    roles: ["admin", "teacher", "parent", "student"]
  },
  {
    name: "My Students",
    href: "/students",
    icon: Users,
    page: "students",
    roles: ["teacher"]
  },
  {
    name: "Students",
    href: "/students",
    icon: Users,
    page: "students",
    roles: ["admin"]
  },
  {
    name: "Teachers",
    href: "/teachers",
    icon: GraduationCap,
    page: "teachers",
    roles: ["admin"]
  },
  {
    name: "Parents",
    href: "/parents",
    icon: Heart,
    page: "parents",
    roles: ["admin"]
  },
  {
    name: "My Classes",
    href: "/classes",
    icon: BookOpen,
    page: "classes",
    roles: ["teacher"]
  },
  {
    name: "Classes",
    href: "/classes",
    icon: BookOpen,
    page: "classes",
    roles: ["admin", "parent"]
  },
  {
    name: "Subjects",
    href: "/subjects",
    icon: BookOpen,
    page: "subjects",
    roles: ["admin", "parent"]
  },
  {
    name: "Departments",
    href: "/departments",
    icon: Building2,
    page: "departments",
    roles: ["admin"]
  },
  {
    name: "Attendance",
    href: "/attendance",
    icon: ClipboardList,
    page: "attendance",
    roles: ["admin", "teacher", "parent"]
  },
  // Admin/Teacher Timetable
  {
    name: "Timetable",
    href: "/timetable",
    icon: Calendar,
    page: "timetable",
    roles: ["admin", "parent"]
  },
  // Teacher Timetable
  {
    name: "My Timetables",
    href: "teacher-timetable",
    icon: Calendar,
    page: "teacher-timetable",
    roles: ["teacher"]
  },
  // Student Timetable
  {
    name: "My Timetable",
    href: "student-timetable",
    icon: Calendar,
    page: "student-timetable",
    roles: ["student"]
  },
  // Admin/Teacher Assignments
  {
    name: "Assignments",
    href: "/assignments",
    icon: FileText,
    page: "assignments",
    roles: ["admin", "teacher", "parent"]
  },
  // Student Assignments
  {
    name: "My Assignments",
    href: "student-assignments",
    icon: FileText,
    page: "student-assignments",
    roles: ["student"]
  },
  // Admin/Teacher Examinations
  {
    name: "Examinations",
    href: "/examinations",
    icon: Award,
    page: "examinations",
    roles: ["admin", "teacher", "parent"]
  },
  // Student Examinations
  {
    name: "My Examinations",
    href: "student-examinations",
    icon: Award,
    page: "student-examinations",
    roles: ["student"]
  },
  {
    name: "Grades",
    href: "/grades",
    icon: BarChart3,
    page: "grades",
    roles: ["admin", "teacher", "parent"]
  },
  // Admin/Teacher Results
  {
    name: "Results",
    href: "/results",
    icon: BarChart3,
    page: "results",
    roles: ["admin", "teacher", "parent"]
  },
  // Student Results
  {
    name: "My Results",
    href: "student-results",
    icon: BarChart3,
    page: "student-results",
    roles: ["student"]
  },
  {
    name: "Fees",
    href: "/fees",
    icon: DollarSign,
    page: "fees",
    roles: ["admin", "parent", "student"]
  },
  {
    name: "Communications",
    href: "/communications",
    icon: MessageSquare,
    page: "communications",
    roles: ["admin", "teacher", "parent", "student"]
  },
  {
    name: "Reports",
    href: "/reports",
    icon: FileText,
    page: "reports",
    roles: ["admin"]
  },
  {
    name: "Notifications",
    href: "/notifications",
    icon: Bell,
    page: "notifications",
    roles: ["admin"]
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    page: "settings",
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "User Management",
    href: "/user-management",
    icon: UserCog,
    page: "user-management",
    roles: ["admin"]
  },
  {
    name: "Role Demo",
    href: "/role-demo",
    icon: Shield,
    page: "role-demo",
    roles: ["admin", "teacher", "parent"]
  },
];

export const Sidebar = () => {
  const location = useLocation();
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { user } = useSimpleAuth();
  // Simplified - assume admin access for now
  const getPageAccess = () => ({ canView: true });

  const getInitials = (email: string) => {
    return email ? email.charAt(0).toUpperCase() : 'U';
  };

  // Simplified - just return the item name
  const getRoleSpecificLabel = (item: any) => {
    return item.name;
  };

  // Filter navigation items based on user role
  const getFilteredNavigation = () => {
    if (!user) return [];

    // Get user role from auth metadata
    const userRole = user?.user_metadata?.role || 'admin';

    console.log('Sidebar - User Role:', userRole);
    console.log('Sidebar - Filtering navigation for role:', userRole);

    return allNavigationItems.filter(item => {
      // Show items that include the user's role
      const hasAccess = item.roles.includes(userRole);
      console.log(`Item "${item.name}" - Roles: [${item.roles.join(', ')}] - Access: ${hasAccess}`);
      return hasAccess;
    }).map(item => ({
      ...item,
      displayName: getRoleSpecificLabel(item)
    }));
  };

  const navigation = getFilteredNavigation();

  return (
    <div className="fixed left-0 top-0 h-screen w-64 flex flex-col bg-white border-r border-gray-200">
      {/* Header with custom green background and logo */}
      <div className="flex h-16 items-center px-6 text-white" style={{ backgroundColor: '#007100' }}>
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <GraduationCap className="h-8 w-8 text-white" />
            <span className="ml-2 text-xl font-bold text-white">
              School Management
            </span>
          </div>
          {user && (
            <div className="flex items-center gap-1 bg-white/20 px-2 py-1 rounded-full">
              <Shield className="h-3 w-3" />
              <span className="text-xs font-medium capitalize">
                {user?.user_metadata?.role || 'admin'}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Navigation - Scrollable content */}
      <nav className="flex-1 space-y-1 px-3 py-4 overflow-y-auto">
        {navigation.map((item) => {
          const fullHref = `/${schoolSlug}/${item.href.replace(/^\//, '')}`;
          const isActive = location.pathname === fullHref;
          return (
            <Link
              key={item.name}
              to={fullHref}
              className={cn(
                "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                isActive
                  ? "bg-green-50 border-r-2"
                  : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              )}
              style={isActive ? { color: '#007100', borderRightColor: '#007100' } : {}}
            >
              <item.icon
                className={cn(
                  "mr-3 h-5 w-5 flex-shrink-0",
                  isActive ? "" : "text-gray-400 group-hover:text-gray-500"
                )}
                style={isActive ? { color: '#007100' } : {}}
              />
              {item.displayName || item.name}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="flex-shrink-0 border-t border-gray-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 rounded-full flex items-center justify-center" style={{ backgroundColor: '#007100' }}>
              <span className="text-sm font-medium text-white">
                {getInitials(user?.email || '')}
              </span>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700">
              {user?.email?.split('@')[0] || 'User'}
            </p>
            <p className="text-xs text-gray-500">{user?.email}</p>
          </div>
        </div>
      </div>
    </div>
  );
};
