import React from 'react';
import { Navigate, useParams } from 'react-router-dom';
import { useMultiTenantAuth } from '@/contexts/MultiTenantAuthContext';
import { UserRole } from '@/types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requiredRoles?: UserRole[];
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredRoles
}) => {
  const { user, profile, isAuthenticated, hasRole, hasAnyRole } = useMultiTenantAuth();
  const { schoolSlug } = useParams<{ schoolSlug: string }>();

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to={`/${schoolSlug}/login`} replace />;
  }

  // If specific role is required, check if user has it
  if (requiredRole && !hasRole(requiredRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  // If multiple roles are allowed, check if user has any of them
  if (requiredRoles && !hasAnyRole(requiredRoles)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};
