import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Subject } from '@/types';
import { departmentsService, getCurrentSchoolId } from '@/services/supabaseService';

interface SubjectFormProps {
  subject?: Subject;
  onSubmit: (data: Omit<Subject, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
}

export const SubjectForm: React.FC<SubjectFormProps> = ({ subject, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: subject?.name || '',
    department: subject?.department || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [departments, setDepartments] = useState<Array<{id: string, name: string}>>([]);
  const [loadingDepartments, setLoadingDepartments] = useState(true);

  // Fetch departments from database
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const schoolId = await getCurrentSchoolId();
        if (schoolId) {
          const departmentsData = await departmentsService.getAll(schoolId);
          setDepartments(departmentsData || []);
        }
      } catch (error) {
        console.error('Error fetching departments:', error);
        // Fallback to default departments if database fetch fails
        setDepartments([
          { id: 'science', name: 'Science' },
          { id: 'arts', name: 'Arts' },
          { id: 'social-studies', name: 'Social Studies' },
          { id: 'technology', name: 'Technology' },
          { id: 'mathematics', name: 'Mathematics' },
          { id: 'languages', name: 'Languages' },
          { id: 'physical-education', name: 'Physical Education' },
          { id: 'fine-arts', name: 'Fine Arts' },
          { id: 'business-studies', name: 'Business Studies' },
          { id: 'vocational-studies', name: 'Vocational Studies' }
        ]);
      } finally {
        setLoadingDepartments(false);
      }
    };

    fetchDepartments();
  }, []);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Subject name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };



  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Subject Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="e.g., Mathematics, English, Science"
            className={errors.name ? 'border-red-500' : ''}
          />
          {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="department">Department (Optional)</Label>
          <select
            id="department"
            value={formData.department}
            onChange={(e) => handleInputChange('department', e.target.value)}
            disabled={loadingDepartments}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
          >
            <option value="">
              {loadingDepartments ? 'Loading departments...' : 'Select Department (Optional)'}
            </option>
            {departments.map(dept => (
              <option key={dept.id} value={dept.name}>{dept.name}</option>
            ))}
          </select>
          {departments.length === 0 && !loadingDepartments && (
            <p className="text-xs text-gray-500">
              No departments found. You can create departments in the Departments page.
            </p>
          )}
        </div>
      </div>

      <div className="flex gap-3 pt-4">
        <Button type="submit" className="flex-1">
          {subject ? 'Update Subject' : 'Add Subject'}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
      </div>
    </form>
  );
};
