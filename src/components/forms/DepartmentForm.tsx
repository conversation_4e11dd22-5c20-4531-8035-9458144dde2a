import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Department } from '@/types';
import { teachersService, getCurrentSchoolId } from '@/services/supabaseService';

interface DepartmentFormProps {
  department?: Department;
  onSubmit: (data: Omit<Department, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
}

export const DepartmentForm: React.FC<DepartmentFormProps> = ({ department, onSubmit, onCancel }) => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const [formData, setFormData] = useState({
    name: department?.name || '',
    description: department?.description || '',
    headOfDepartmentId: department?.headOfDepartmentId || '',
    isActive: department?.isActive ?? true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [teachers, setTeachers] = useState<any[]>([]);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load school ID and teachers
  useEffect(() => {
    const initializeData = async () => {
      if (!schoolSlug) return;

      try {
        const currentSchoolId = await getCurrentSchoolId(schoolSlug);
        if (currentSchoolId) {
          setSchoolId(currentSchoolId);

          // Load teachers for head of department selection
          const teachersData = await teachersService.getAll(currentSchoolId);
          setTeachers(teachersData);
        }
      } catch (error) {
        console.error('Error loading teachers:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, [schoolSlug]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Department name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };



  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Department Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="e.g., Computer Science"
            className={errors.name ? 'border-red-500' : ''}
          />
          {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
        </div>

        {/* Department code will be auto-generated from name */}
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Describe what this department covers..."
          rows={3}
          className={errors.description ? 'border-red-500' : ''}
        />
        {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
      </div>

      {/* Head of Department */}
      <div className="space-y-2">
        <Label htmlFor="headOfDepartmentId">Head of Department (Optional)</Label>
        <Select onValueChange={(value) => handleInputChange('headOfDepartmentId', value === 'none' ? '' : value)} value={formData.headOfDepartmentId || 'none'}>
          <SelectTrigger>
            <SelectValue placeholder={isLoading ? "Loading teachers..." : "Select head of department"} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No head assigned</SelectItem>
            {teachers.map(teacher => (
              <SelectItem key={teacher.id} value={teacher.id}>
                {teacher.profile?.first_name} {teacher.profile?.last_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-500">
          Select a teacher to lead this department (optional)
        </p>
      </div>

      {/* Teachers and Subjects will be managed through separate assignment pages */}

      <div className="flex items-center space-x-2">
        <Switch
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => handleInputChange('isActive', checked)}
        />
        <Label htmlFor="isActive">Active Department</Label>
        <p className="text-xs text-gray-500">Inactive departments won't be available for new assignments</p>
      </div>

      <div className="flex gap-3 pt-4">
        <Button type="submit" className="flex-1">
          {department ? 'Update Department' : 'Add Department'}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
      </div>
    </form>
  );
};
