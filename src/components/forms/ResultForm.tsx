import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Result } from '@/types';

const resultSchema = z.object({
  studentId: z.string().min(1, 'Student is required'),
  examId: z.string().min(1, 'Exam is required'),
  subject: z.string().min(1, 'Subject is required'),
  marksObtained: z.number().min(0, 'Marks must be 0 or greater'),
  totalMarks: z.number().min(1, 'Total marks must be greater than 0'),
  grade: z.string().optional(),
  remarks: z.string().optional(),
  examDate: z.string().min(1, 'Exam date is required'),
  percentage: z.number().optional(),
});

type ResultFormData = z.infer<typeof resultSchema>;

interface ResultFormProps {
  result?: Result;
  onSubmit: (data: ResultFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  students?: any[];
  examinations?: any[];
  subjects?: any[];
  classes?: any[];
}

export const ResultForm: React.FC<ResultFormProps> = ({
  result,
  onSubmit,
  onCancel,
  isLoading = false,
  students = [],
  examinations = [],
  subjects = [],
  classes = []
}) => {
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [filteredStudents, setFilteredStudents] = useState<any[]>(students);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<ResultFormData>({
    resolver: zodResolver(resultSchema),
    defaultValues: result ? {
      studentId: result.studentId,
      examId: result.examId,
      subject: result.subject,
      marksObtained: result.marksObtained,
      totalMarks: result.totalMarks,
      grade: result.grade || '',
      remarks: result.remarks || '',
      examDate: result.examDate,
      percentage: result.percentage,
    } : {
      studentId: '',
      examId: '',
      subject: '',
      marksObtained: 0,
      totalMarks: 100,
      grade: '',
      remarks: '',
      examDate: new Date().toISOString().split('T')[0],
      percentage: 0,
    }
  });

  const marksObtained = watch('marksObtained');
  const totalMarks = watch('totalMarks');

  // Auto-calculate grade and percentage
  React.useEffect(() => {
    if (marksObtained !== undefined && totalMarks > 0) {
      const percentage = (marksObtained / totalMarks) * 100;
      let grade = '';
      
      if (percentage >= 90) grade = 'A+';
      else if (percentage >= 80) grade = 'A';
      else if (percentage >= 70) grade = 'B+';
      else if (percentage >= 60) grade = 'B';
      else if (percentage >= 50) grade = 'C+';
      else if (percentage >= 40) grade = 'C';
      else if (percentage >= 33) grade = 'D';
      else grade = 'F';
      
      setValue('grade', grade);
    }
  }, [marksObtained, totalMarks, setValue]);

  // Filter students by selected class
  useEffect(() => {
    console.log('📊 ResultForm - All students:', students.length);
    console.log('📊 ResultForm - Selected class:', selectedClass);
    console.log('📊 ResultForm - Students data:', students.map(s => ({ id: s.id, name: `${s.first_name} ${s.last_name}`, class_id: s.class_id })));

    if (selectedClass) {
      const studentsInClass = students.filter(student => student.class_id === selectedClass);
      setFilteredStudents(studentsInClass);
      console.log('📊 Filtered students for class:', selectedClass, studentsInClass.length);
      console.log('📊 Filtered students:', studentsInClass.map(s => ({ id: s.id, name: `${s.first_name} ${s.last_name}`, class_id: s.class_id })));
    } else {
      setFilteredStudents(students);
    }
  }, [selectedClass, students]);

  // Handle class selection
  const handleClassChange = (classId: string) => {
    console.log('📊 Class selected:', classId);
    console.log('📊 Available classes:', classes.map(c => ({ id: c.id, name: c.name, section: c.section })));
    setSelectedClass(classId);
    // Clear student selection when class changes
    setValue('studentId', '');
  };

  const getPercentage = () => {
    if (marksObtained !== undefined && totalMarks > 0) {
      return ((marksObtained / totalMarks) * 100).toFixed(2);
    }
    return '0.00';
  };

  const handleFormSubmit = (data: ResultFormData) => {
    // Calculate percentage before submitting
    const percentage = data.totalMarks > 0 ? (data.marksObtained / data.totalMarks) * 100 : 0;
    const formDataWithPercentage = {
      ...data,
      percentage,
      classId: selectedClass // Add the selected class ID
    };
    console.log('📊 ResultForm submitting data:', formDataWithPercentage);
    onSubmit(formDataWithPercentage);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium mb-4">Result Information</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="classId">Class *</Label>
              <Select onValueChange={handleClassChange} value={selectedClass}>
                <SelectTrigger>
                  <SelectValue placeholder="Select class first" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name} {cls.section}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="studentId">Student *</Label>
              <Select
                onValueChange={(value) => setValue('studentId', value)}
                value={watch('studentId')}
                disabled={!selectedClass}
              >
                <SelectTrigger>
                  <SelectValue placeholder={selectedClass ? "Select student" : "Select class first"} />
                </SelectTrigger>
                <SelectContent>
                  {filteredStudents.map(student => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.first_name} {student.last_name} ({student.student_id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.studentId && (
                <p className="text-sm text-red-600">{errors.studentId.message}</p>
              )}
              {selectedClass && filteredStudents.length === 0 && (
                <p className="text-sm text-gray-500">No students found in this class</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="examId">Exam *</Label>
              <Select onValueChange={(value) => setValue('examId', value)} value={watch('examId')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select exam" />
                </SelectTrigger>
                <SelectContent>
                  {examinations.map(exam => (
                    <SelectItem key={exam.id} value={exam.id}>
                      {exam.name} ({exam.exam_type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.examId && (
                <p className="text-sm text-red-600">{errors.examId.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="subject">Subject *</Label>
              <Select onValueChange={(value) => setValue('subject', value)} value={watch('subject')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map(subject => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name} ({subject.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.subject && (
                <p className="text-sm text-red-600">{errors.subject.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="examDate">Exam Date *</Label>
              <Input
                id="examDate"
                type="date"
                {...register('examDate')}
              />
              {errors.examDate && (
                <p className="text-sm text-red-600">{errors.examDate.message}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Marks Information */}
      <div>
        <h3 className="text-lg font-medium mb-4">Marks & Grading</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="marksObtained">Marks Obtained *</Label>
              <Input
                id="marksObtained"
                type="number"
                step="0.01"
                {...register('marksObtained', { valueAsNumber: true })}
                placeholder="85"
              />
              {errors.marksObtained && (
                <p className="text-sm text-red-600">{errors.marksObtained.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="totalMarks">Total Marks *</Label>
              <Input
                id="totalMarks"
                type="number"
                {...register('totalMarks', { valueAsNumber: true })}
                placeholder="100"
              />
              {errors.totalMarks && (
                <p className="text-sm text-red-600">{errors.totalMarks.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="grade">Grade</Label>
              <Input
                id="grade"
                {...register('grade')}
                placeholder="Auto-calculated"
                readOnly
                className="bg-gray-50"
              />
            </div>
          </div>

          {/* Percentage Display */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-900">Percentage:</span>
              <span className="text-lg font-bold text-blue-900">{getPercentage()}%</span>
            </div>
            <div className="flex items-center justify-between mt-1">
              <span className="text-sm font-medium text-blue-900">Grade:</span>
              <span className="text-lg font-bold text-blue-900">{watch('grade')}</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="remarks">Remarks</Label>
            <Textarea
              id="remarks"
              {...register('remarks')}
              placeholder="Additional comments about the performance..."
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* Grade Scale Reference */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Grade Scale Reference:</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600">
          <div>A+: 90-100%</div>
          <div>A: 80-89%</div>
          <div>B+: 70-79%</div>
          <div>B: 60-69%</div>
          <div>C+: 50-59%</div>
          <div>C: 40-49%</div>
          <div>D: 33-39%</div>
          <div>F: Below 33%</div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex gap-4 pt-4">
        <Button type="submit" disabled={isLoading} className="flex-1">
          {isLoading ? 'Saving...' : result ? 'Update Result' : 'Add Result'}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
      </div>
    </form>
  );
};
