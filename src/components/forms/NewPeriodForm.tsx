import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Clock, BookOpen, User, MapPin, Calendar } from 'lucide-react';

// Form validation schema
const periodFormSchema = z.object({
  day_of_week: z.string().min(1, 'Day is required'),
  period_number: z.string().optional(),
  start_time: z.string().min(1, 'Start time is required'),
  end_time: z.string().min(1, 'End time is required'),
  subject_id: z.string().min(1, 'Subject is required'),
  teacher_id: z.string().min(1, 'Teacher is required'),
  class_id: z.string().min(1, 'Class is required'),
  room_number: z.string().optional(),
});

type PeriodFormData = z.infer<typeof periodFormSchema>;

interface PeriodFormProps {
  period?: any;
  onSubmit: (data: PeriodFormData) => void | Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  subjects?: any[];
  teachers?: any[];
  classes?: any[];
  selectedClass?: string;
}

// Constants
const DAYS_OF_WEEK = [
  { value: '1', label: 'Monday' },
  { value: '2', label: 'Tuesday' },
  { value: '3', label: 'Wednesday' },
  { value: '4', label: 'Thursday' },
  { value: '5', label: 'Friday' },
  { value: '6', label: 'Saturday' },
];

// Removed PERIOD_NUMBERS - now using manual input

// Removed TIME_SLOTS - now using time picker input

export const PeriodForm: React.FC<PeriodFormProps> = ({
  period,
  onSubmit,
  onCancel,
  isLoading = false,
  subjects = [],
  teachers = [],
  classes = [],
  selectedClass = ''
}) => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<PeriodFormData>({
    resolver: zodResolver(periodFormSchema),
    defaultValues: {
      day_of_week: period?.day_of_week?.toString() || '',
      period_number: period?.period_number?.toString() || '',
      start_time: period?.start_time || '',
      end_time: period?.end_time || '',
      subject_id: period?.subject_id || '',
      teacher_id: period?.teacher_id || '',
      class_id: period?.class_id || selectedClass || '',
      room_number: period?.room_number || '',
    }
  });

  const handleFormSubmit = async (data: PeriodFormData) => {
    try {
      // Process the data before submission
      const processedData = {
        ...data,
        // Convert period_number to string if provided, otherwise use empty string
        period_number: data.period_number?.trim() || '',
        // Clean up room_number
        room_number: data.room_number?.trim() || '',
      };

      console.log('📅 Submitting period data:', processedData);
      await onSubmit(processedData);
    } catch (error) {
      console.error('Error submitting period form:', error);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {period ? 'Edit Period' : 'Add New Period'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Schedule Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="day_of_week" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Day of Week
              </Label>
              <Select onValueChange={(value) => setValue('day_of_week', value)} value={watch('day_of_week')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  {DAYS_OF_WEEK.map(day => (
                    <SelectItem key={day.value} value={day.value}>
                      {day.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.day_of_week && (
                <p className="text-sm text-red-600">{errors.day_of_week.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="period_number" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Period Number <span className="text-sm text-gray-500">(Optional)</span>
              </Label>
              <Input
                {...register('period_number')}
                placeholder="Enter period number (e.g., 1, 2, 3...)"
                type="text"
              />
              {errors.period_number && (
                <p className="text-sm text-red-600">{errors.period_number.message}</p>
              )}
            </div>
          </div>

          {/* Time Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_time" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Start Time
              </Label>
              <Input
                {...register('start_time')}
                type="time"
                className="w-full"
                placeholder="Enter start time"
              />
              {errors.start_time && (
                <p className="text-sm text-red-600">{errors.start_time.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_time" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                End Time
              </Label>
              <Input
                {...register('end_time')}
                type="time"
                className="w-full"
                placeholder="Enter end time"
              />
              {errors.end_time && (
                <p className="text-sm text-red-600">{errors.end_time.message}</p>
              )}
            </div>
          </div>

          {/* Academic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="subject_id" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Subject
              </Label>
              <Select onValueChange={(value) => setValue('subject_id', value)} value={watch('subject_id')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map(subject => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name} ({subject.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.subject_id && (
                <p className="text-sm text-red-600">{errors.subject_id.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="teacher_id" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Teacher
              </Label>
              <Select onValueChange={(value) => setValue('teacher_id', value)} value={watch('teacher_id')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select teacher" />
                </SelectTrigger>
                <SelectContent>
                  {teachers.map(teacher => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.first_name} {teacher.last_name} ({teacher.employee_id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.teacher_id && (
                <p className="text-sm text-red-600">{errors.teacher_id.message}</p>
              )}
            </div>
          </div>

          {/* Class and Room Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="class_id">Class</Label>
              <Select onValueChange={(value) => setValue('class_id', value)} value={watch('class_id')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name} {cls.section}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.class_id && (
                <p className="text-sm text-red-600">{errors.class_id.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="room_number" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Room <span className="text-sm text-gray-500">(Optional)</span>
              </Label>
              <Input
                {...register('room_number')}
                placeholder="Enter room number or name (e.g., Room 101, Lab 1, Library)"
              />
              {errors.room_number && (
                <p className="text-sm text-red-600">{errors.room_number.message}</p>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {period ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                period ? 'Update Period' : 'Create Period'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
