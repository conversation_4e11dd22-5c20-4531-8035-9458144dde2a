import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { Assignment } from '@/types';

const assignmentSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  subjectId: z.string().min(1, 'Subject is required'),
  classId: z.string().min(1, 'Class is required'),
  dueDate: z.string().min(1, 'Due date is required'),
  totalMarks: z.number().min(1, 'Total marks must be at least 1'),
  instructions: z.string().optional(),
});

type AssignmentFormData = z.infer<typeof assignmentSchema>;

interface AssignmentFormProps {
  assignment?: Assignment;
  subjects?: any[];
  classes?: any[];
  onSubmit: (data: AssignmentFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export const AssignmentForm: React.FC<AssignmentFormProps> = ({
  assignment,
  subjects = [],
  classes = [],
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<AssignmentFormData>({
    resolver: zodResolver(assignmentSchema),
    defaultValues: assignment ? {
      title: assignment.title,
      description: assignment.description,
      subject: assignment.subject,
      class: assignment.class,
      dueDate: assignment.dueDate,
      maxScore: assignment.maxScore,
      instructions: assignment.instructions || '',
      attachments: assignment.attachments || [],
    } : {
      title: '',
      description: '',
      subject: '',
      class: '',
      dueDate: '',
      maxScore: 100,
      instructions: '',
      attachments: [],
    }
  });

  const [dueDate, setDueDate] = React.useState<Date | undefined>(
    assignment?.dueDate ? new Date(assignment.dueDate) : undefined
  );

  const selectedSubject = watch('subject');
  const selectedClass = watch('class');

  const handleDateSelect = (date: Date | undefined) => {
    setDueDate(date);
    if (date) {
      setValue('dueDate', date.toISOString().split('T')[0]);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium mb-4">Assignment Details</h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Assignment Title *</Label>
            <Input
              id="title"
              {...register('title')}
              placeholder="e.g., Chapter 5 Problem Set"
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Provide a detailed description of the assignment..."
              rows={4}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          {/* Class Selection - Primary Field */}
          <div className="space-y-2 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <Label htmlFor="classId" className="text-blue-800 font-medium">Target Class *</Label>
            <Select onValueChange={(value) => {
              setValue('classId', value);
              setSelectedClass(value);
            }} value={selectedClass}>
              <SelectTrigger className="border-blue-300">
                <SelectValue placeholder="Select class - All students in this class will receive this assignment" />
              </SelectTrigger>
              <SelectContent>
                {classes.map(cls => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name} {cls.section}
                    <span className="text-sm text-gray-500 ml-2">
                      ({cls.max_students || 'N/A'} students)
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.classId && (
              <p className="text-sm text-red-600">{errors.classId.message}</p>
            )}
            {selectedClass && (
              <p className="text-sm text-blue-600 mt-2">
                📚 This assignment will be visible to all students in the selected class
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="subjectId">Subject *</Label>
            <Select onValueChange={(value) => setValue('subjectId', value)} value={selectedSubject}>
              <SelectTrigger>
                <SelectValue placeholder="Select subject" />
              </SelectTrigger>
              <SelectContent>
                {subjects.map(subject => (
                  <SelectItem key={subject.id} value={subject.id}>{subject.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.subjectId && (
              <p className="text-sm text-red-600">{errors.subjectId.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Due Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? format(dueDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dueDate}
                    onSelect={handleDateSelect}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.dueDate && (
                <p className="text-sm text-red-600">{errors.dueDate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="totalMarks">Total Marks *</Label>
              <Input
                id="totalMarks"
                type="number"
                {...register('totalMarks', { valueAsNumber: true })}
                placeholder="100"
              />
              {errors.totalMarks && (
                <p className="text-sm text-red-600">{errors.totalMarks.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="instructions">Instructions</Label>
            <Textarea
              id="instructions"
              {...register('instructions')}
              placeholder="Additional instructions for students..."
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* Submission Settings */}
      <div>
        <h3 className="text-lg font-medium mb-4">Submission Settings</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="allowLateSubmission"
              className="rounded border-gray-300"
            />
            <Label htmlFor="allowLateSubmission">Allow late submissions</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="requireFile"
              className="rounded border-gray-300"
            />
            <Label htmlFor="requireFile">Require file upload</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="autoGrade"
              className="rounded border-gray-300"
            />
            <Label htmlFor="autoGrade">Enable auto-grading (for multiple choice)</Label>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex gap-4 pt-4">
        <Button type="submit" disabled={isLoading} className="flex-1">
          {isLoading ? 'Saving...' : assignment ? 'Update Assignment' : 'Create Assignment'}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
      </div>
    </form>
  );
};
