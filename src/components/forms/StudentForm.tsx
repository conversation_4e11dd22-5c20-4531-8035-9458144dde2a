import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Student } from '@/types';
import { classesService, getCurrentSchoolId } from '@/services/supabaseService';

const studentSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid parent email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  studentId: z.string().min(1, 'Student ID is required'),
  classId: z.string().min(1, 'Class is required'),
  parentContact: z.string().optional(),
  admissionDate: z.string().min(1, 'Admission date is required'),
  emergencyContact: z.string().optional(),
  medicalInfo: z.string().optional(),
  gender: z.string().min(1, 'Gender is required'),
});

type StudentFormData = z.infer<typeof studentSchema>;

interface StudentFormProps {
  student?: Student;
  onSubmit: (data: StudentFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}



export const StudentForm: React.FC<StudentFormProps> = ({
  student,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const [classes, setClasses] = useState<any[]>([]);
  const [loadingClasses, setLoadingClasses] = useState(true);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  // Load school ID and classes
  useEffect(() => {
    const initializeData = async () => {
      if (!schoolSlug) return;

      try {
        const currentSchoolId = await getCurrentSchoolId(schoolSlug);
        if (currentSchoolId) {
          setSchoolId(currentSchoolId);

          // Load classes for this school
          const classesData = await classesService.getAll(currentSchoolId);
          setClasses(classesData);
        }
      } catch (error) {
        console.error('Error loading classes:', error);
      } finally {
        setLoadingClasses(false);
      }
    };

    initializeData();
  }, [schoolSlug]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<StudentFormData>({
    resolver: zodResolver(studentSchema),
    defaultValues: student ? {
      firstName: student.firstName,
      lastName: student.lastName,
      email: student.email,
      phone: student.phone || '',
      address: student.address || '',
      dateOfBirth: student.dateOfBirth || '',
      studentId: student.studentId,
      classId: student.classId || '',
      parentContact: student.parentContact || '',
      admissionDate: student.admissionDate,
      emergencyContact: student.emergencyContact || '',
      medicalInfo: student.medicalInfo || '',
      gender: student.gender || '',
    } : {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      address: '',
      dateOfBirth: '',
      studentId: '',
      classId: '',
      parentContact: '',
      admissionDate: '',
      emergencyContact: '',
      medicalInfo: '',
      gender: '',
    }
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Personal Information */}
      <div>
        <h3 className="text-base sm:text-lg font-medium mb-4">Personal Information</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name *</Label>
            <Input
              id="firstName"
              {...register('firstName')}
              placeholder="Enter first name"
            />
            {errors.firstName && (
              <p className="text-sm text-red-600">{errors.firstName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name *</Label>
            <Input
              id="lastName"
              {...register('lastName')}
              placeholder="Enter last name"
            />
            {errors.lastName && (
              <p className="text-sm text-red-600">{errors.lastName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Parent's Email Address *</Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              placeholder="Enter parent's email (used for login)"
            />
            <p className="text-xs text-gray-500">This email will be used for parent login access</p>
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number *</Label>
            <Input
              id="phone"
              type="tel"
              {...register('phone')}
              placeholder="Enter phone number"
            />
            {errors.phone && (
              <p className="text-sm text-red-600">{errors.phone.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="dateOfBirth">Date of Birth *</Label>
            <Input
              id="dateOfBirth"
              type="date"
              {...register('dateOfBirth')}
            />
            {errors.dateOfBirth && (
              <p className="text-sm text-red-600">{errors.dateOfBirth.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="gender">Gender *</Label>
            <Select onValueChange={(value) => setValue('gender', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Male</SelectItem>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            {errors.gender && (
              <p className="text-sm text-red-600">{errors.gender.message}</p>
            )}
          </div>


        </div>

        <div className="space-y-2 mt-4">
          <Label htmlFor="address">Address *</Label>
          <Textarea
            id="address"
            {...register('address')}
            placeholder="Enter full address"
            rows={3}
          />
          {errors.address && (
            <p className="text-sm text-red-600">{errors.address.message}</p>
          )}
        </div>
      </div>

      {/* Academic Information */}
      <div>
        <h3 className="text-base sm:text-lg font-medium mb-4">Academic Information</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="studentId">Student ID *</Label>
            <Input
              id="studentId"
              {...register('studentId')}
              placeholder="Enter student ID"
            />
            <p className="text-xs text-gray-500">This ID will be used as password for student portal login</p>
            {errors.studentId && (
              <p className="text-sm text-red-600">{errors.studentId.message}</p>
            )}
          </div>



          <div className="space-y-2">
            <Label htmlFor="classId">Class *</Label>
            <Select onValueChange={(value) => setValue('classId', value)}>
              <SelectTrigger>
                <SelectValue placeholder={loadingClasses ? "Loading classes..." : "Select class"} />
              </SelectTrigger>
              <SelectContent>
                {classes.map(cls => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name} (Grade {cls.grade_level})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.classId && (
              <p className="text-sm text-red-600">{errors.classId.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="admissionDate">Admission Date *</Label>
            <Input
              id="admissionDate"
              type="date"
              {...register('admissionDate')}
            />
            {errors.admissionDate && (
              <p className="text-sm text-red-600">{errors.admissionDate.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="parentContact">Parent Contact</Label>
            <Input
              id="parentContact"
              {...register('parentContact')}
              placeholder="Enter parent contact number"
            />
            {errors.parentContact && (
              <p className="text-sm text-red-600">{errors.parentContact.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Emergency Information */}
      <div>
        <h3 className="text-base sm:text-lg font-medium mb-4">Emergency Information</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="emergencyContact">Emergency Contact (Optional)</Label>
            <Input
              id="emergencyContact"
              type="tel"
              {...register('emergencyContact')}
              placeholder="Enter emergency contact number"
            />
            {errors.emergencyContact && (
              <p className="text-sm text-red-600">{errors.emergencyContact.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="medicalInfo">Medical Information (Optional)</Label>
            <Textarea
              id="medicalInfo"
              {...register('medicalInfo')}
              placeholder="Enter any medical conditions, allergies, etc."
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
        <Button type="submit" disabled={isLoading} className="flex-1 h-11">
          {isLoading ? 'Saving...' : student ? 'Update Student' : 'Add Student'}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} className="flex-1 h-11">
          Cancel
        </Button>
      </div>
    </form>
  );
};
