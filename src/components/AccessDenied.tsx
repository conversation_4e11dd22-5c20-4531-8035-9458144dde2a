import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { 
  Shield, 
  Lock, 
  AlertTriangle, 
  Home, 
  ArrowLeft,
  User,
  Users,
  GraduationCap,
  Heart
} from "lucide-react";

interface AccessDeniedProps {
  title?: string;
  description?: string;
  requiredRole?: string;
  requiredPermission?: string;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  variant?: 'full' | 'card' | 'inline';
}

const getRoleIcon = (role: string) => {
  switch (role) {
    case 'admin':
      return <Shield className="h-8 w-8 text-red-500" />;
    case 'teacher':
      return <GraduationCap className="h-8 w-8 text-blue-500" />;
    case 'student':
      return <User className="h-8 w-8 text-green-500" />;
    case 'parent':
      return <Heart className="h-8 w-8 text-purple-500" />;
    default:
      return <Users className="h-8 w-8 text-gray-500" />;
  }
};

const getRoleLabel = (role: string) => {
  switch (role) {
    case 'admin':
      return 'Administrator';
    case 'teacher':
      return 'Teacher';
    case 'student':
      return 'Student';
    case 'parent':
      return 'Parent';
    default:
      return 'User';
  }
};

export const AccessDenied: React.FC<AccessDeniedProps> = ({
  title = "Access Denied",
  description,
  requiredRole,
  requiredPermission,
  showBackButton = true,
  showHomeButton = true,
  variant = 'full'
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const getDefaultDescription = () => {
    if (requiredRole) {
      return `This page requires ${getRoleLabel(requiredRole)} access. You are currently logged in as ${getRoleLabel(user?.role || 'unknown')}.`;
    }
    if (requiredPermission) {
      return `You don't have the required permission (${requiredPermission}) to access this page.`;
    }
    return "You don't have permission to access this page.";
  };

  const finalDescription = description || getDefaultDescription();

  const content = (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        {requiredRole ? (
          getRoleIcon(requiredRole)
        ) : (
          <Lock className="h-12 w-12 text-red-500" />
        )}
      </div>
      
      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
        <p className="text-gray-600 max-w-md mx-auto">{finalDescription}</p>
      </div>

      {user && (
        <div className="bg-gray-50 rounded-lg p-4 max-w-sm mx-auto">
          <div className="flex items-center gap-3">
            {getRoleIcon(user.role)}
            <div className="text-left">
              <p className="font-medium text-gray-900">{user.firstName} {user.lastName}</p>
              <p className="text-sm text-gray-600">{getRoleLabel(user.role)} Account</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {showBackButton && (
          <Button
            variant="outline"
            onClick={() => navigate(-1)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Go Back
          </Button>
        )}
        {showHomeButton && (
          <Button
            onClick={() => navigate('/dashboard')}
            className="flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Go to Dashboard
          </Button>
        )}
      </div>
    </div>
  );

  if (variant === 'full') {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          {content}
        </div>
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <div className="flex justify-center mb-4">
            {requiredRole ? (
              getRoleIcon(requiredRole)
            ) : (
              <Lock className="h-8 w-8 text-red-500" />
            )}
          </div>
          <CardTitle className="text-center">{title}</CardTitle>
          <CardDescription className="text-center">{finalDescription}</CardDescription>
        </CardHeader>
        <CardContent>
          {user && (
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <div className="flex items-center gap-3">
                {getRoleIcon(user.role)}
                <div className="text-left">
                  <p className="font-medium text-gray-900">{user.firstName} {user.lastName}</p>
                  <p className="text-sm text-gray-600">{getRoleLabel(user.role)} Account</p>
                </div>
              </div>
            </div>
          )}
          
          <div className="flex flex-col sm:flex-row gap-3">
            {showBackButton && (
              <Button
                variant="outline"
                onClick={() => navigate(-1)}
                className="flex items-center gap-2 flex-1"
              >
                <ArrowLeft className="h-4 w-4" />
                Go Back
              </Button>
            )}
            {showHomeButton && (
              <Button
                onClick={() => navigate('/dashboard')}
                className="flex items-center gap-2 flex-1"
              >
                <Home className="h-4 w-4" />
                Dashboard
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Inline variant
  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-start gap-3">
        <AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="font-medium text-yellow-800">{title}</h3>
          <p className="text-sm text-yellow-700 mt-1">{finalDescription}</p>
          {(showBackButton || showHomeButton) && (
            <div className="flex gap-2 mt-3">
              {showBackButton && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(-1)}
                  className="text-yellow-800 border-yellow-300 hover:bg-yellow-100"
                >
                  <ArrowLeft className="h-3 w-3 mr-1" />
                  Back
                </Button>
              )}
              {showHomeButton && (
                <Button
                  size="sm"
                  onClick={() => navigate('/dashboard')}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  <Home className="h-3 w-3 mr-1" />
                  Dashboard
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Higher-order component for conditional access control
export const withAccessControl = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string,
  requiredPermission?: string
) => {
  return (props: P) => {
    const { user, hasRole, hasPermission } = useAuth();
    
    if (!user) {
      return <AccessDenied title="Authentication Required" description="Please log in to access this page." />;
    }
    
    if (requiredRole && !hasRole(requiredRole as any)) {
      return <AccessDenied requiredRole={requiredRole} />;
    }
    
    if (requiredPermission && !hasPermission(requiredPermission)) {
      return <AccessDenied requiredPermission={requiredPermission} />;
    }
    
    return <Component {...props} />;
  };
};
