import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Menu,
  X,
  Home,
  User,
  Users,
  Book,
  BookOpen,
  Building,
  ClipboardList,
  GraduationCap,
  FileText,
  Calendar,
  Award,
  BarChart3,
  DollarSign,
  MessageCircle,
  Settings,
  Bell
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { She<PERSON>, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

interface MenuItem {
  title: string;
  url: string;
  icon: any;
}

const menuItems: MenuItem[] = [
  { title: "Dashboard", url: "/dashboard", icon: Home },
  { title: "Students", url: "/students", icon: User },
  { title: "Teachers", url: "/teachers", icon: Users },
  { title: "Classes", url: "/classes", icon: Book },
  { title: "Subjects", url: "/subjects", icon: BookOpen },
  { title: "Departments", url: "/departments", icon: Building },
  { title: "Attendance", url: "/attendance", icon: ClipboardList },
  { title: "Grades", url: "/grades", icon: GraduationCap },
  { title: "Assignments", url: "/assignments", icon: FileText },
  { title: "Examinations", url: "/examinations", icon: BookOpen },
  { title: "Timetable", url: "/timetable", icon: Calendar },
  { title: "Results", url: "/results", icon: Award },
  { title: "Reports", url: "/reports", icon: BarChart3 },
  { title: "Fees", url: "/fees", icon: DollarSign },
  { title: "Communications", url: "/communications", icon: MessageCircle },
  { title: "Notifications", url: "/notifications", icon: Bell },
  { title: "Settings", url: "/settings", icon: Settings },
];

export const MobileNavMenu: React.FC = () => {
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="md:hidden">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="text-white hover:bg-white/10">
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 p-0 bg-[#006400]">
          <div className="flex flex-col h-full">
            {/* Header */}
            <SheetHeader className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">SMS</span>
                  </div>
                  <div>
                    <SheetTitle className="text-white text-left">
                      School Management
                    </SheetTitle>
                    <p className="text-white/80 text-sm">Admin Portal</p>
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => setIsOpen(false)}
                  className="text-white hover:bg-white/10"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </SheetHeader>

            {/* User Info */}
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-medium bg-red-500">
                  AU
                </div>
                <div className="flex-1">
                  <p className="text-white font-medium">Admin User</p>
                  <p className="text-white/70 text-sm"><EMAIL></p>
                </div>
              </div>
            </div>

            {/* Navigation Menu */}
            <div className="flex-1 overflow-y-auto p-4">
              <nav className="space-y-2">
                {menuItems.map((item) => {
                  const isActive = location.pathname === item.url;
                  const Icon = item.icon;

                  return (
                    <NavLink
                      key={item.title}
                      to={item.url}
                      onClick={() => setIsOpen(false)}
                      className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
                        isActive
                          ? 'bg-white/20 text-white'
                          : 'text-white/80 hover:bg-white/10 hover:text-white'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="font-medium">{item.title}</span>
                    </NavLink>
                  );
                })}
              </nav>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-white/20">
              <div className="text-center">
                <p className="text-white/60 text-xs">
                  School Management System v1.0
                </p>
                <p className="text-white/60 text-xs mt-1">
                  © 2024 All rights reserved
                </p>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};
