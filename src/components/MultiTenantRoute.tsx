
import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { useSchool } from '@/contexts/SchoolContext';
import { useMultiTenantAuth } from '@/contexts/MultiTenantAuthContext';
import { School } from 'lucide-react';

interface MultiTenantRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

export const MultiTenantRoute: React.FC<MultiTenantRouteProps> = ({ 
  children, 
  requireAuth = true 
}) => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { currentSchool, loadSchoolBySlug } = useSchool();
  const { isAuthenticated, loading: authLoading } = useMultiTenantAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  console.log('🛣️ MultiTenantRoute state:', {
    schoolSlug,
    currentSchool: currentSchool?.slug,
    isAuthenticated,
    authLoading,
    loading,
    error,
    requireAuth
  });

  useEffect(() => {
    const initializeSchool = async () => {
      if (!schoolSlug) {
        setError('No school specified');
        setLoading(false);
        return;
      }

      if (currentSchool?.slug === schoolSlug) {
        setLoading(false);
        return;
      }

      try {
        const school = await loadSchoolBySlug(schoolSlug);
        if (!school) {
          setError('School not found');
        }
      } catch (err) {
        setError('Failed to load school');
      } finally {
        setLoading(false);
      }
    };

    initializeSchool();
  }, [schoolSlug, currentSchool, loadSchoolBySlug]);

  if (loading || authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
        <div className="text-center">
          <School className="h-16 w-16 text-blue-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (error || !currentSchool) {
    return <Navigate to="/" replace />;
  }

  if (requireAuth && !isAuthenticated) {
    return <Navigate to={`/${schoolSlug}/login`} replace />;
  }

  return <>{children}</>;
};
