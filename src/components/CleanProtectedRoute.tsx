import React from 'react';
import { Navigate, useParams } from 'react-router-dom';
import { useCleanAuth } from '@/contexts/CleanAuthContext';
import { School } from 'lucide-react';

interface CleanProtectedRouteProps {
  children: React.ReactNode;
}

export const CleanProtectedRoute: React.FC<CleanProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useCleanAuth();
  const { schoolSlug } = useParams<{ schoolSlug: string }>();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <School className="h-16 w-16 text-blue-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to={`/${schoolSlug}/login`} replace />;
  }

  return <>{children}</>;
};
