import React from 'react';
import { Link, useLocation, useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/NewAuthContext';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Users,
  GraduationCap,
  Heart,
  BookOpen,
  Building2,
  ClipboardList,
  Calendar,
  FileText,
  Award,
  BarChart3,
  DollarSign,
  MessageSquare,
  Bell,
  Settings,
  UserCog,
  Shield,
  LogOut
} from 'lucide-react';

interface NewLayoutProps {
  children: React.ReactNode;
}

// Navigation items with role-based access
const navigationItems = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    roles: ["admin", "teacher", "parent", "student"]
  },
  {
    name: "Students",
    href: "/students",
    icon: Users,
    roles: ["admin"]
  },
  {
    name: "My Students",
    href: "/students",
    icon: Users,
    roles: ["teacher"]
  },
  {
    name: "Teachers",
    href: "/teachers",
    icon: GraduationCap,
    roles: ["admin"]
  },
  {
    name: "Parents",
    href: "/parents",
    icon: Heart,
    roles: ["admin"]
  },
  {
    name: "Classes",
    href: "/classes",
    icon: BookOpen,
    roles: ["admin", "parent"]
  },
  {
    name: "My Classes",
    href: "/classes",
    icon: BookOpen,
    roles: ["teacher"]
  },
  {
    name: "Subjects",
    href: "/subjects",
    icon: BookOpen,
    roles: ["admin", "parent"]
  },
  {
    name: "Departments",
    href: "/departments",
    icon: Building2,
    roles: ["admin"]
  },
  {
    name: "Attendance",
    href: "/attendance",
    icon: ClipboardList,
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "Timetable",
    href: "/timetable",
    icon: Calendar,
    roles: ["admin", "parent"]
  },
  {
    name: "My Timetable",
    href: "/student-timetable",
    icon: Calendar,
    roles: ["student"]
  },
  {
    name: "My Timetables",
    href: "/teacher-timetable",
    icon: Calendar,
    roles: ["teacher"]
  },
  {
    name: "Assignments",
    href: "/assignments",
    icon: FileText,
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "My Assignments",
    href: "/student-assignments",
    icon: FileText,
    roles: ["student"]
  },
  {
    name: "Examinations",
    href: "/examinations",
    icon: Award,
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "My Examinations",
    href: "/student-examinations",
    icon: Award,
    roles: ["student"]
  },
  {
    name: "Grades",
    href: "/grades",
    icon: BarChart3,
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "Results",
    href: "/results",
    icon: BarChart3,
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "My Results",
    href: "/student-results",
    icon: BarChart3,
    roles: ["student"]
  },
  {
    name: "Fees",
    href: "/fees",
    icon: DollarSign,
    roles: ["admin", "parent", "student"]
  },
  {
    name: "Communications",
    href: "/communications",
    icon: MessageSquare,
    roles: ["admin", "teacher", "parent", "student"]
  },
  {
    name: "Reports",
    href: "/reports",
    icon: FileText,
    roles: ["admin"]
  },
  {
    name: "Notifications",
    href: "/notifications",
    icon: Bell,
    roles: ["admin"]
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    roles: ["admin", "teacher", "parent"]
  },
  {
    name: "User Management",
    href: "/user-management",
    icon: UserCog,
    roles: ["admin"]
  }
];

export const NewLayout: React.FC<NewLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { user, profile, signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate(`/${schoolSlug}/login`);
  };

  const getInitials = (email: string) => {
    return email.charAt(0).toUpperCase();
  };

  // Filter navigation items based on user role
  const getFilteredNavigation = () => {
    if (!profile) return [];

    return navigationItems.filter(item => 
      item.roles.includes(profile.role)
    );
  };

  const navigation = getFilteredNavigation();

  return (
    <div className="min-h-screen">
      {/* Desktop Sidebar */}
      <div className="hidden md:block">
        <div className="fixed left-0 top-0 h-screen w-64 flex flex-col bg-white border-r border-gray-200">
          {/* Header */}
          <div className="flex h-16 items-center px-6 text-white" style={{ backgroundColor: '#007100' }}>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <GraduationCap className="h-8 w-8 text-white" />
                <span className="ml-2 text-xl font-bold text-white">
                  School Management
                </span>
              </div>
              {profile && (
                <div className="flex items-center gap-1 bg-white/20 px-2 py-1 rounded-full">
                  <Shield className="h-3 w-3" />
                  <span className="text-xs font-medium">{profile.role}</span>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {navigation.map((item) => {
              const isActive = location.pathname === `/${schoolSlug}${item.href}`;
              const Icon = item.icon;
              
              return (
                <Link
                  key={item.name}
                  to={`/${schoolSlug}${item.href}`}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    isActive
                      ? "bg-green-100 text-green-700"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  )}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User info and sign out */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-green-700 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {user?.email ? getInitials(user.email) : 'U'}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">
                    {profile?.first_name} {profile?.last_name}
                  </p>
                  <p className="text-xs text-gray-500">{profile?.role}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-gray-500"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="md:ml-64 flex flex-col min-h-screen">
        {/* Mobile header */}
        <div className="md:hidden bg-white shadow-sm border-b">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <GraduationCap className="h-6 w-6 text-green-700" />
                <span className="ml-2 text-lg font-bold text-gray-900">
                  {schoolSlug}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-gray-500"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <main className="flex-1 bg-gray-50">
          {children}
        </main>
      </div>
    </div>
  );
};
