import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/NewAuthContext';
import { Shield, ArrowLeft, Home } from 'lucide-react';

export const Unauthorized: React.FC = () => {
  const { user, signOut } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 rounded-full bg-red-100">
              <Shield className="h-8 w-8 text-red-500" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-red-700">Access Denied</CardTitle>
          <p className="text-gray-600">You don't have permission to access this page</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">Current User:</p>
            <p className="font-medium">{user?.firstName} {user?.lastName}</p>
            <p className="text-sm text-gray-500">Role: {user?.role}</p>
          </div>
          
          <div className="space-y-2">
            <Button asChild className="w-full" variant="outline">
              <Link to="/dashboard">
                <Home className="h-4 w-4 mr-2" />
                Go to Dashboard
              </Link>
            </Button>
            
            <Button asChild className="w-full" variant="outline">
              <Link to="/role-demo">
                <Shield className="h-4 w-4 mr-2" />
                View Role Permissions
              </Link>
            </Button>
            
            <Button
              onClick={signOut}
              className="w-full"
              variant="destructive"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
          
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">Need Access?</h4>
            <p className="text-sm text-yellow-700">
              Contact your system administrator to request additional permissions for your role.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
