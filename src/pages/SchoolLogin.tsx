
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useMultiTenantAuth } from '@/contexts/MultiTenantAuthContext';
import { useSchool } from '@/contexts/SchoolContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  LogIn,
  School,
  Eye,
  EyeOff,
  CheckCircle,
  Shield,
  User,
  Users,
  GraduationCap,
  Heart,
  Info,
  Copy,
  ArrowLeft
} from 'lucide-react';
import { PublicHeader } from '@/components/PublicHeader';
import { PublicFooter } from '@/components/PublicFooter';

export const SchoolLogin: React.FC = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const [searchParams] = useSearchParams();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [selectedRole, setSelectedRole] = useState<'admin' | 'teacher' | 'student'>('admin');
  const [loading, setLoading] = useState(false);
  const [schoolLoading, setSchoolLoading] = useState(true);
  const [schoolLoadError, setSchoolLoadError] = useState(false);
  const { login, isAuthenticated } = useMultiTenantAuth();
  const { currentSchool, loadSchoolBySlug } = useSchool();
  const navigate = useNavigate();
  const { toast } = useToast();

  console.log('SchoolLogin component rendered for school:', schoolSlug);
  console.log('Authentication state:', { isAuthenticated, currentSchool: currentSchool?.slug });

  const isRegistered = searchParams.get('registered') === 'true';
  const adminRegistered = searchParams.get('admin_registered') === 'true';





  useEffect(() => {
    console.log('SchoolLogin useEffect triggered:', { schoolSlug, isAuthenticated, currentSchool: currentSchool?.slug });

    if (!schoolSlug) {
      console.log('No schoolSlug, redirecting to home');
      navigate('/');
      return;
    }

    if (isAuthenticated && currentSchool) {
      console.log('User is authenticated, redirecting to dashboard');
      navigate(`/${schoolSlug}/dashboard`);
      return;
    }

    console.log('No redirect needed, staying on login page');

    // Load school data with timeout
    const loadSchool = async () => {
      try {
        setSchoolLoading(true);
        setSchoolLoadError(false);

        const school = await loadSchoolBySlug(schoolSlug);

        if (!school) {
          console.error('School not found for slug:', schoolSlug);
          setSchoolLoadError(true);
          toast({
            title: "School Not Found",
            description: "The school you're looking for doesn't exist or is inactive.",
            variant: "destructive",
          });
          navigate('/');
        } else {
          console.log('School loaded successfully:', school);
          setSchoolLoadError(false);
        }
      } catch (error) {
        console.error('Error loading school:', error);
        setSchoolLoadError(true);
        toast({
          title: "Error Loading School",
          description: "There was an error loading the school. Please try again.",
          variant: "destructive",
        });
      } finally {
        setSchoolLoading(false);
      }
    };

    loadSchool();
  }, [schoolSlug, isAuthenticated, currentSchool, navigate, loadSchoolBySlug]);

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('🎯 handleSubmit called!');
    e.preventDefault();
    console.log('🛑 preventDefault called');

    if (!schoolSlug) {
      console.log('❌ No schoolSlug, returning early');
      return;
    }

    console.log('🚀 Form submitted, starting simplified login...');
    setLoading(true);

    try {
      // Simplified login logic - bypass complex authentication for now
      console.log('📝 Login attempt:', { email, role: selectedRole, schoolSlug });

      // Simulate authentication delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For now, allow any email/password combination to login
      if (email && password) {
        console.log('✅ Simplified login successful');

        toast({
          title: "Login Successful",
          description: `Welcome to ${currentSchool?.name}!`,
        });

        console.log('🧭 Navigating to dashboard...');
        navigate(`/${schoolSlug}/dashboard`);
      } else {
        console.log('❌ Missing email or password');
        toast({
          title: "Login Failed",
          description: "Please enter both email and password.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('💥 Login error:', error);
      toast({
        title: "Error",
        description: "An error occurred during login. Please try again.",
        variant: "destructive",
      });
    } finally {
      console.log('🏁 Setting loading to false');
      setLoading(false);
    }
  };

  if (schoolLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
        <div className="text-center">
          <School className="h-16 w-16 text-blue-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Loading school...</p>
        </div>
      </div>
    );
  }

  if (schoolLoadError || !currentSchool) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
        <div className="text-center">
          <School className="h-16 w-16 text-red-600 mx-auto mb-4" />
          <p className="text-red-600 font-medium">School not found</p>
          <p className="text-gray-600 text-sm mt-2">The school "{schoolSlug}" doesn't exist or is inactive.</p>
          <Button
            onClick={() => navigate('/')}
            className="mt-4"
            variant="outline"
          >
            Go Home
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <PublicHeader />

      {/* COMPONENT RENDER TEST */}
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        backgroundColor: 'purple',
        color: 'white',
        padding: '10px',
        zIndex: 9999,
        border: '3px solid yellow'
      }}>
        🟣 SCHOOLLOGIN COMPONENT IS RENDERING! 🟣
      </div>

      <div className="flex items-center justify-center py-12 px-4">
        <div className="w-full max-w-4xl">
          {/* Navigation Link */}
          <div className="text-center mb-4">
            <Button
              variant="ghost"
              onClick={() => navigate(`/${schoolSlug}`)}
              className="text-blue-600 hover:text-blue-700"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Main Portal
            </Button>
          </div>

          <div className="text-center mb-8">
            <School className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {currentSchool.name}
            </h1>
            <p className="text-gray-600">Sign in to access your dashboard</p>
          </div>

          {isRegistered && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                <p className="font-medium">Registration Successful!</p>
              </div>
              <p className="text-green-700 text-sm mt-1">
                Your school has been registered successfully. Please check your email for login credentials.
              </p>
            </div>
          )}

          {adminRegistered && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                <p className="font-medium">Admin Account Created!</p>
              </div>
              <p className="text-green-700 text-sm mt-1">
                Your administrator account has been created successfully. Please check your email to verify your account, then sign in below.
              </p>
            </div>
          )}

          <div className="max-w-md mx-auto">
            {/* Login Form */}
            <Card className="shadow-lg mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LogIn className="h-5 w-5" />
                  Login to Your Account
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="role">Login As</Label>
                    <Tabs value={selectedRole} onValueChange={(value) => setSelectedRole(value as 'admin' | 'teacher' | 'student')} className="w-full">
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="admin" className="flex items-center gap-2">
                          <Shield className="h-4 w-4" />
                          Admin
                        </TabsTrigger>
                        <TabsTrigger value="teacher" className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Teacher
                        </TabsTrigger>
                        <TabsTrigger value="student" className="flex items-center gap-2">
                          <GraduationCap className="h-4 w-4" />
                          Student
                        </TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter your password"
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    disabled={loading}
                  >
                    {loading ? "Signing in..." : "Sign In"}
                  </Button>
                </form>

                {/* Signup Links */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-700 mb-3">Don't have an account?</p>
                    <div className="flex flex-col gap-3">
                      <Button
                        variant="outline"
                        size="default"
                        onClick={() => {
                          console.log('Student signup clicked');
                          navigate(`/${schoolSlug}/signup/student`);
                        }}
                        className="flex items-center justify-center gap-2 w-full bg-blue-50 hover:bg-blue-100 border-blue-200"
                      >
                        <GraduationCap className="h-5 w-5" />
                        Student Registration
                      </Button>
                      <Button
                        variant="outline"
                        size="default"
                        onClick={() => {
                          console.log('Teacher signup clicked');
                          navigate(`/${schoolSlug}/signup/teacher`);
                        }}
                        className="flex items-center justify-center gap-2 w-full bg-green-50 hover:bg-green-100 border-green-200"
                      >
                        <Users className="h-5 w-5" />
                        Teacher Registration
                      </Button>
                    </div>
                  </div>

                  <div className="text-center pt-3 mt-3 border-t border-gray-200">
                    <p className="text-xs text-gray-500">
                      Admin access? Contact your school administrator
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* TEST SIGNUP SECTION - SHOULD BE VERY VISIBLE */}
            <div style={{
              backgroundColor: 'red',
              padding: '20px',
              margin: '20px 0',
              border: '5px solid black',
              textAlign: 'center'
            }}>
              <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '20px' }}>
                🚨 SIGNUP SECTION TEST 🚨
              </h2>

              <button
                onClick={() => {
                  alert('Student signup clicked!');
                  navigate(`/${schoolSlug}/signup/student`);
                }}
                style={{
                  backgroundColor: 'blue',
                  color: 'white',
                  padding: '15px 30px',
                  fontSize: '18px',
                  border: 'none',
                  margin: '10px',
                  cursor: 'pointer',
                  borderRadius: '5px'
                }}
              >
                👨‍🎓 STUDENT SIGNUP
              </button>

              <button
                onClick={() => {
                  alert('Teacher signup clicked!');
                  navigate(`/${schoolSlug}/signup/teacher`);
                }}
                style={{
                  backgroundColor: 'green',
                  color: 'white',
                  padding: '15px 30px',
                  fontSize: '18px',
                  border: 'none',
                  margin: '10px',
                  cursor: 'pointer',
                  borderRadius: '5px'
                }}
              >
                👨‍🏫 TEACHER SIGNUP
              </button>

              <p style={{ color: 'white', marginTop: '10px' }}>
                If you can see this red box, the component is rendering!
              </p>
            </div>

          </div>
        </div>
      </div>

      <PublicFooter />
    </div>
  );
};
