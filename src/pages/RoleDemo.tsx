import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { UserSwitcher } from '@/components/UserSwitcher';
import { useAuth } from '@/contexts/AuthContext';
import { useDataAccess } from '@/hooks/useDataAccess';
import { Shield, Eye, Plus, Edit, Trash2, Download, Settings } from 'lucide-react';

const pages = [
  'students', 'teachers', 'parents', 'classes', 'subjects', 'departments',
  'assignments', 'examinations', 'results', 'grades', 'attendance', 
  'timetable', 'fees', 'communications', 'notifications', 'reports', 'settings'
];

const actionIcons = {
  canView: Eye,
  canCreate: Plus,
  canEdit: Edit,
  canDelete: Trash2,
  canExport: Download,
  canManage: Settings
};

export const RoleDemo: React.FC = () => {
  const { user } = useAuth();
  const { getPageAccess } = useDataAccess();

  const getPermissionColor = (hasPermission: boolean) => {
    return hasPermission 
      ? 'bg-green-100 text-green-800 border-green-200' 
      : 'bg-red-100 text-red-800 border-red-200';
  };

  const getViewModeColor = (viewMode: string) => {
    switch (viewMode) {
      case 'full':
        return 'bg-blue-100 text-blue-800';
      case 'limited':
        return 'bg-yellow-100 text-yellow-800';
      case 'own':
        return 'bg-purple-100 text-purple-800';
      case 'none':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Role-Based Access Control Demo</h1>
          <p className="text-gray-600 mt-1">
            Test different user roles and their permissions across the system
          </p>
        </div>
      </div>

      {/* User Switcher */}
      <UserSwitcher />

      {/* Current User Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Current User: {user?.firstName} {user?.lastName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700">Role</label>
              <Badge className={`mt-1 ${
                user?.role === 'admin' ? 'bg-red-100 text-red-800' :
                user?.role === 'teacher' ? 'bg-blue-100 text-blue-800' :
                user?.role === 'student' ? 'bg-green-100 text-green-800' :
                'bg-purple-100 text-purple-800'
              }`}>
                {user?.role?.charAt(0).toUpperCase()}{user?.role?.slice(1)}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Email</label>
              <p className="text-sm text-gray-900">{user?.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Username</label>
              <p className="text-sm text-gray-900">{user?.username}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Permissions Matrix</CardTitle>
          <p className="text-sm text-gray-600">
            Shows what actions the current user can perform on each page
          </p>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-3 font-medium text-gray-700">Page</th>
                  <th className="text-center py-2 px-2 font-medium text-gray-700">View</th>
                  <th className="text-center py-2 px-2 font-medium text-gray-700">Create</th>
                  <th className="text-center py-2 px-2 font-medium text-gray-700">Edit</th>
                  <th className="text-center py-2 px-2 font-medium text-gray-700">Delete</th>
                  <th className="text-center py-2 px-2 font-medium text-gray-700">Export</th>
                  <th className="text-center py-2 px-2 font-medium text-gray-700">Manage</th>
                  <th className="text-center py-2 px-3 font-medium text-gray-700">View Mode</th>
                </tr>
              </thead>
              <tbody>
                {pages.map((page) => {
                  const access = getPageAccess(page);
                  return (
                    <tr key={page} className="border-b hover:bg-gray-50">
                      <td className="py-2 px-3 font-medium capitalize">{page}</td>
                      <td className="text-center py-2 px-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPermissionColor(access.canView)}`}
                        >
                          {access.canView ? '✓' : '✗'}
                        </Badge>
                      </td>
                      <td className="text-center py-2 px-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPermissionColor(access.canCreate)}`}
                        >
                          {access.canCreate ? '✓' : '✗'}
                        </Badge>
                      </td>
                      <td className="text-center py-2 px-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPermissionColor(access.canEdit)}`}
                        >
                          {access.canEdit ? '✓' : '✗'}
                        </Badge>
                      </td>
                      <td className="text-center py-2 px-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPermissionColor(access.canDelete)}`}
                        >
                          {access.canDelete ? '✓' : '✗'}
                        </Badge>
                      </td>
                      <td className="text-center py-2 px-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPermissionColor(access.canExport)}`}
                        >
                          {access.canExport ? '✓' : '✗'}
                        </Badge>
                      </td>
                      <td className="text-center py-2 px-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPermissionColor(access.canManage)}`}
                        >
                          {access.canManage ? '✓' : '✗'}
                        </Badge>
                      </td>
                      <td className="text-center py-2 px-3">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getViewModeColor(access.viewMode)}`}
                        >
                          {access.viewMode}
                        </Badge>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle>Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Permissions</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-green-100 text-green-800">✓</Badge>
                  <span>Allowed</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-red-100 text-red-800">✗</Badge>
                  <span>Not Allowed</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">View Modes</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">full</Badge>
                  <span>Access to all data</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-yellow-100 text-yellow-800">limited</Badge>
                  <span>Access to assigned/related data only</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">own</Badge>
                  <span>Access to own data only</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-gray-100 text-gray-800">none</Badge>
                  <span>No access</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sidebar Navigation Info */}
      <Card>
        <CardHeader>
          <CardTitle>Role-Based Sidebar Navigation</CardTitle>
          <p className="text-sm text-gray-600">
            The sidebar automatically adapts to show only accessible pages with role-specific labels
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 text-red-700">Admin Navigation</h4>
              <div className="space-y-1 text-sm text-gray-700">
                <p>• All pages visible (15+ navigation items)</p>
                <p>• Teachers, Departments, Reports, Notifications</p>
                <p>• User Management & System Settings</p>
                <p>• Standard labels (Students, Teachers, etc.)</p>
                <p>• Full system access indicator</p>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3 text-blue-700">Teacher Navigation</h4>
              <div className="space-y-1 text-sm text-gray-700">
                <p>• Academic pages only (no Teachers, Departments)</p>
                <p>• "My Classes", "My Students", "My Timetable"</p>
                <p>• Focus on classroom management</p>
                <p>• No Reports, Notifications, or Fees</p>
                <p>• Teacher role indicator in header</p>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3 text-purple-700">Parent Navigation</h4>
              <div className="space-y-1 text-sm text-gray-700">
                <p>• Children-focused academic pages</p>
                <p>• "Children's Results", "Children's Fees", "Children's Subjects"</p>
                <p>• No Teachers, Departments, Reports access</p>
                <p>• Can manage children's fee payments</p>
                <p>• Parent role indicator in header</p>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h5 className="font-medium text-green-800 mb-2">Dynamic Features:</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-700">
              <div>
                <p><strong>Smart Filtering:</strong> Only shows pages the user can access</p>
                <p><strong>Role Indicators:</strong> Visual role badges in sidebar header</p>
              </div>
              <div>
                <p><strong>Contextual Labels:</strong> Navigation text adapts to user role</p>
                <p><strong>Permission-Based:</strong> Uses actual permission system</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
