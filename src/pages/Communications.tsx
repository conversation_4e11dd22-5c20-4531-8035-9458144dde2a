import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/components/ui/use-toast";
import {
  Search,
  Plus,
  Send,
  MessageCircle,
  Mail,
  Phone,
  Users,
  User,
  Bell,
  Eye,
  Reply,
  Forward,
  Archive,
  MoreVertical
} from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";
import { MetricCard } from "@/components/MetricCard";
import { useIsMobile } from "@/hooks/use-mobile";
import { useDataAccess } from "@/hooks/useDataAccess.tsx";
import { CommunicationService } from "@/services";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

// Mock communication data
const mockMessages = [
  {
    id: '1',
    from: 'Ms. Sarah Davis',
    fromRole: 'teacher',
    to: 'Alice Johnson (Parent)',
    toRole: 'parent',
    subject: 'Alice\'s Academic Progress',
    message: 'Alice has shown excellent improvement in Mathematics this term. She scored 92% in the recent test.',
    timestamp: '2024-12-10T14:30:00Z',
    isRead: false,
    priority: 'normal',
    type: 'message'
  },
  {
    id: '2',
    from: 'Admin Office',
    fromRole: 'admin',
    to: 'All Parents',
    toRole: 'parent',
    subject: 'Parent-Teacher Meeting Reminder',
    message: 'This is a reminder about the upcoming parent-teacher meeting scheduled for December 22, 2024.',
    timestamp: '2024-12-08T10:00:00Z',
    isRead: true,
    priority: 'high',
    type: 'announcement'
  },
  {
    id: '3',
    from: 'Bob Johnson (Parent)',
    fromRole: 'parent',
    to: 'Mr. Johnson',
    toRole: 'teacher',
    subject: 'Question about Homework',
    message: 'Could you please clarify the requirements for the mathematics assignment due next week?',
    timestamp: '2024-12-09T16:45:00Z',
    isRead: true,
    priority: 'normal',
    type: 'message'
  }
];

const mockAnnouncements = [
  {
    id: '1',
    title: 'Winter Break Schedule',
    content: 'School will be closed from December 23, 2024 to January 2, 2025. Classes will resume on January 3, 2025.',
    author: 'Admin Office',
    timestamp: '2024-12-05T09:00:00Z',
    priority: 'high',
    recipients: 'All'
  },
  {
    id: '2',
    title: 'Science Fair Registration',
    content: 'Registration for the annual science fair is now open. Deadline for submission is December 20, 2024.',
    author: 'Ms. Garcia',
    timestamp: '2024-12-03T11:30:00Z',
    priority: 'normal',
    recipients: 'Students'
  }
];

export const Communications: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [messages, setMessages] = useState(mockMessages);
  const [announcements, setAnnouncements] = useState(mockAnnouncements);
  const [isComposeOpen, setIsComposeOpen] = useState(false);
  const [isAnnouncementOpen, setIsAnnouncementOpen] = useState(false);
  const isMobile = useIsMobile();

  // Form states for compose message
  const [composeForm, setComposeForm] = useState({
    to: '',
    priority: 'normal',
    subject: '',
    message: ''
  });

  // Form states for announcement
  const [announcementForm, setAnnouncementForm] = useState({
    recipients: '',
    priority: 'normal',
    title: '',
    content: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">High</Badge>;
      case 'normal':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Normal</Badge>;
      case 'low':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Low</Badge>;
      default:
        return <Badge variant="secondary">Normal</Badge>;
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-500';
      case 'teacher': return 'bg-blue-500';
      case 'student': return 'bg-green-500';
      case 'parent': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = (message.subject?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (message.from?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (message.message?.toLowerCase() || '').includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || message.type === selectedType;

    return matchesSearch && matchesType;
  });

  const unreadCount = messages.filter(m => !m.isRead).length;
  const totalMessages = messages.length;
  const highPriorityCount = messages.filter(m => m.priority === 'high').length;

  const markAsRead = (messageId: string) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId ? { ...msg, isRead: true } : msg
    ));
  };

  const handleComposeSubmit = async () => {
    if (!composeForm.to || !composeForm.subject || !composeForm.message) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newMessage = {
        id: (messages.length + 1).toString(),
        from: user?.firstName + ' ' + user?.lastName || 'You',
        fromRole: user?.role || 'user',
        to: composeForm.to,
        toRole: 'recipient',
        subject: composeForm.subject,
        message: composeForm.message,
        timestamp: new Date().toISOString(),
        isRead: false,
        priority: composeForm.priority,
        type: 'message' as const
      };

      setMessages(prev => [newMessage, ...prev]);
      setComposeForm({ to: '', priority: 'normal', subject: '', message: '' });
      setIsComposeOpen(false);

      toast({
        title: "Message Sent!",
        description: "Your message has been sent successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAnnouncementSubmit = async () => {
    if (!announcementForm.recipients || !announcementForm.title || !announcementForm.content) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newAnnouncement = {
        id: (announcements.length + 1).toString(),
        title: announcementForm.title,
        content: announcementForm.content,
        author: user?.firstName + ' ' + user?.lastName || 'You',
        timestamp: new Date().toISOString(),
        priority: announcementForm.priority,
        recipients: announcementForm.recipients
      };

      setAnnouncements(prev => [newAnnouncement, ...prev]);
      setAnnouncementForm({ recipients: '', priority: 'normal', title: '', content: '' });
      setIsAnnouncementOpen(false);

      toast({
        title: "Announcement Published!",
        description: "Your announcement has been published successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to publish announcement. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetComposeForm = () => {
    setComposeForm({ to: '', priority: 'normal', subject: '', message: '' });
  };

  const resetAnnouncementForm = () => {
    setAnnouncementForm({ recipients: '', priority: 'normal', title: '', content: '' });
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    toast({
      title: "Filters Cleared",
      description: "All filters have been reset.",
    });
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Communications</h1>
          <p className="text-gray-600 mt-1 text-xs sm:text-sm md:text-base">
            Manage messages, announcements, and school communications
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isComposeOpen} onOpenChange={setIsComposeOpen}>
            <DialogTrigger asChild>
              <Button className="text-xs md:text-sm px-3 py-2 h-9">
                <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Compose Message</span>
                <span className="sm:hidden">Compose</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl sm:max-w-[95vw] sm:mx-4 max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="text-lg sm:text-xl">Compose New Message</DialogTitle>
                <DialogDescription className="text-sm">
                  Send a message to students, parents, or teachers
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>To *</Label>
                    <Select value={composeForm.to} onValueChange={(value) => setComposeForm(prev => ({ ...prev, to: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select recipient" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all-parents">All Parents</SelectItem>
                        <SelectItem value="all-students">All Students</SelectItem>
                        <SelectItem value="all-teachers">All Teachers</SelectItem>
                        <SelectItem value="john-smith">John Smith (Parent)</SelectItem>
                        <SelectItem value="sarah-davis">Sarah Davis (Teacher)</SelectItem>
                        <SelectItem value="alice-johnson">Alice Johnson (Student)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Priority</Label>
                    <Select value={composeForm.priority} onValueChange={(value) => setComposeForm(prev => ({ ...prev, priority: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Subject *</Label>
                  <Input
                    placeholder="Enter message subject"
                    value={composeForm.subject}
                    onChange={(e) => setComposeForm(prev => ({ ...prev, subject: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Message *</Label>
                  <Textarea
                    placeholder="Type your message here..."
                    rows={6}
                    value={composeForm.message}
                    onChange={(e) => setComposeForm(prev => ({ ...prev, message: e.target.value }))}
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    className="flex-1"
                    onClick={handleComposeSubmit}
                    disabled={isSubmitting}
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsComposeOpen(false);
                      resetComposeForm();
                    }}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          {(user?.role === 'admin' || user?.role === 'teacher') && (
            <Dialog open={isAnnouncementOpen} onOpenChange={setIsAnnouncementOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="text-xs md:text-sm px-3 py-2 h-9">
                  <Bell className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                  <span className="hidden sm:inline">New Announcement</span>
                  <span className="sm:hidden">Announce</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl sm:max-w-[95vw] sm:mx-4 max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Create Announcement</DialogTitle>
                  <DialogDescription>
                    Create a new announcement for the school community
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Recipients *</Label>
                      <Select value={announcementForm.recipients} onValueChange={(value) => setAnnouncementForm(prev => ({ ...prev, recipients: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select recipients" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Everyone">Everyone</SelectItem>
                          <SelectItem value="All Parents">All Parents</SelectItem>
                          <SelectItem value="All Students">All Students</SelectItem>
                          <SelectItem value="All Teachers">All Teachers</SelectItem>
                          <SelectItem value="Grade 8">Grade 8 Students</SelectItem>
                          <SelectItem value="Grade 9">Grade 9 Students</SelectItem>
                          <SelectItem value="Grade 10">Grade 10 Students</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Priority</Label>
                      <Select value={announcementForm.priority} onValueChange={(value) => setAnnouncementForm(prev => ({ ...prev, priority: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Title *</Label>
                    <Input
                      placeholder="Announcement title"
                      value={announcementForm.title}
                      onChange={(e) => setAnnouncementForm(prev => ({ ...prev, title: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Content *</Label>
                    <Textarea
                      placeholder="Announcement content..."
                      rows={6}
                      value={announcementForm.content}
                      onChange={(e) => setAnnouncementForm(prev => ({ ...prev, content: e.target.value }))}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button
                      className="flex-1"
                      onClick={handleAnnouncementSubmit}
                      disabled={isSubmitting}
                    >
                      <Bell className="h-4 w-4 mr-2" />
                      {isSubmitting ? 'Publishing...' : 'Publish Announcement'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAnnouncementOpen(false);
                        resetAnnouncementForm();
                      }}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {/* Communication Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
        <MetricCard
          title="Total Messages"
          value={totalMessages.toString()}
          icon={<MessageCircle className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Unread Messages"
          value={unreadCount.toString()}
          icon={<Mail className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-red-500"
        />
        <MetricCard
          title="High Priority"
          value={highPriorityCount.toString()}
          icon={<Bell className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-yellow-500"
        />
        <MetricCard
          title="Announcements"
          value={announcements.length.toString()}
          icon={<Users className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-green-500"
        />
      </div>

      <Tabs defaultValue="messages" className="space-y-4">
        <TabsList>
          <TabsTrigger value="messages">Messages</TabsTrigger>
          <TabsTrigger value="announcements">Announcements</TabsTrigger>
          <TabsTrigger value="sent">Sent</TabsTrigger>
        </TabsList>

        <TabsContent value="messages" className="space-y-4">
          {/* Message Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Message Filters</CardTitle>
              <CardDescription>
                Filter messages by type or search for specific content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Message Type</Label>
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="message">Personal Messages</SelectItem>
                      <SelectItem value="announcement">Announcements</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search messages..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>&nbsp;</Label>
                  <Button className="w-full" variant="outline" onClick={clearFilters}>
                    Clear Filters
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Messages List */}
          <Card>
            <CardHeader>
              <CardTitle>Inbox</CardTitle>
              <CardDescription>
                Your messages and communications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredMessages.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
                    <p className="text-gray-600 mb-4">
                      {searchTerm || selectedType !== 'all'
                        ? 'Try adjusting your search terms or filters.'
                        : 'No messages to display yet.'}
                    </p>
                    {!searchTerm && selectedType === 'all' && (
                      <Button onClick={() => setIsComposeOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Send Your First Message
                      </Button>
                    )}
                  </div>
                ) : (
                  filteredMessages.map((message) => (
                  <Card key={message.id} className={`${!message.isRead ? 'bg-blue-50 border-blue-200' : ''}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className={`${getRoleColor(message.fromRole)} text-white`}>
                            {getInitials(message.from)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <p className="font-semibold">{message.from}</p>
                              <Badge variant="outline" className="text-xs capitalize">
                                {message.fromRole}
                              </Badge>
                              {getPriorityBadge(message.priority)}
                              {!message.isRead && (
                                <Badge className="bg-blue-500 text-white">New</Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-500">
                              {new Date(message.timestamp).toLocaleDateString()}
                            </p>
                          </div>
                          
                          <h3 className="font-medium mb-2">{message.subject}</h3>
                          <p className="text-gray-700 text-sm line-clamp-2">{message.message}</p>
                          
                          <div className="flex items-center gap-2 mt-3">
                            <Button size="sm" variant="outline" onClick={() => markAsRead(message.id)}>
                              <Eye className="h-3 w-3 mr-1" />
                              {message.isRead ? 'Read' : 'Mark as Read'}
                            </Button>
                            <Button size="sm" variant="outline">
                              <Reply className="h-3 w-3 mr-1" />
                              Reply
                            </Button>
                            <Button size="sm" variant="outline">
                              <Forward className="h-3 w-3 mr-1" />
                              Forward
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="announcements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>School Announcements</CardTitle>
              <CardDescription>
                Important announcements and notices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {announcements.length === 0 ? (
                  <div className="text-center py-8">
                    <Bell className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No announcements yet</h3>
                    <p className="text-gray-600 mb-4">
                      No announcements have been published yet.
                    </p>
                    {(user?.role === 'admin' || user?.role === 'teacher') && (
                      <Button onClick={() => setIsAnnouncementOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Announcement
                      </Button>
                    )}
                  </div>
                ) : (
                  announcements.map((announcement) => (
                  <Card key={announcement.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{announcement.title}</h3>
                          {getPriorityBadge(announcement.priority)}
                        </div>
                        <p className="text-sm text-gray-500">
                          {new Date(announcement.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                      
                      <p className="text-gray-700 mb-3">{announcement.content}</p>
                      
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <p>By: {announcement.author}</p>
                        <p>Recipients: {announcement.recipients}</p>
                      </div>
                    </CardContent>
                  </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sent Messages</CardTitle>
              <CardDescription>
                Messages you have sent
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 text-center py-8">No sent messages to display.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
