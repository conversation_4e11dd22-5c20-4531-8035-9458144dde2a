import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { LogIn, Eye, EyeOff, GraduationCap, Users } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

export const SimpleLogin: React.FC = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { signIn } = useSimpleAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const testSupabaseConnection = async () => {
    try {
      console.log('🧪 Testing Supabase connection...');
      const { data, error } = await supabase.from('schools').select('count').limit(1);
      console.log('🧪 Supabase test result:', { data, error });

      if (error) {
        toast({
          title: "Supabase Connection Failed",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Supabase Connected",
          description: "Database connection is working",
        });
      }
    } catch (error: any) {
      console.error('🧪 Supabase test error:', error);
      toast({
        title: "Connection Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      console.log('🔐 SimpleLogin: Attempting login with:', email);
      const result = await signIn(email, password);
      console.log('🔐 SimpleLogin: Login result:', result);

      if (result.success) {
        toast({
          title: "Success!",
          description: "Login successful",
        });
        // Redirect to dashboard
        navigate(`/${schoolSlug}/dashboard`);
      } else {
        console.error('🔐 SimpleLogin: Login failed:', result.error);
        toast({
          title: "Login Failed",
          description: result.error || "Invalid credentials",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('🔐 SimpleLogin: Login error:', error);
      toast({
        title: "Error",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-600">
            Sign in to access {schoolSlug} dashboard
          </p>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LogIn className="h-5 w-5" />
              Login
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>

              {/* Debug: Test Supabase Connection */}
              <Button
                type="button"
                variant="outline"
                className="w-full mt-2"
                onClick={testSupabaseConnection}
              >
                Test Database Connection
              </Button>
            </form>

            {/* Signup Links */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="text-center mb-3">
                <p className="text-sm font-medium text-gray-700">Don't have an account?</p>
              </div>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="default"
                  onClick={() => {
                    console.log('Student signup clicked, navigating to:', `/${schoolSlug}/signup/student`);
                    navigate(`/${schoolSlug}/signup/student`);
                  }}
                  className="w-full bg-blue-100 hover:bg-blue-200 border-blue-300 text-blue-800"
                >
                  <GraduationCap className="h-4 w-4 mr-2" />
                  Student Registration
                </Button>
                <Button
                  variant="outline"
                  size="default"
                  onClick={() => {
                    console.log('Teacher signup clicked, navigating to:', `/${schoolSlug}/signup/teacher`);
                    navigate(`/${schoolSlug}/signup/teacher`);
                  }}
                  className="w-full bg-green-100 hover:bg-green-200 border-green-300 text-green-800"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Teacher Registration
                </Button>
              </div>
              <div className="text-center mt-3 pt-3 border-t border-blue-200">
                <p className="text-xs text-gray-500">
                  Admin access? Contact your school administrator
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
