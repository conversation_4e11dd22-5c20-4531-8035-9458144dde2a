import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/NewAuthContext';
import { parentsService, getCurrentSchoolId } from '@/services/supabaseService';
import { toast } from '@/components/ui/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Users,
  UserPlus,
  Search,
  MoreHorizontal,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Filter,
  Download,
  Baby,
  Key,
} from 'lucide-react';


// Parents data now comes from Supabase database

export const Parents: React.FC = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [selectedParent, setSelectedParent] = useState<any>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isCredentialsDialogOpen, setIsCredentialsDialogOpen] = useState(false);
  const [selectedUserForCredentials, setSelectedUserForCredentials] = useState<any>(null);
  const [parents, setParents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [schoolId, setSchoolId] = useState<string | null>(null);

  // Load school ID and parents data
  useEffect(() => {
    const initializeData = async () => {
      if (!schoolSlug) return;

      try {
        const currentSchoolId = await getCurrentSchoolId(schoolSlug);
        if (currentSchoolId) {
          setSchoolId(currentSchoolId);
        }
      } catch (error) {
        console.error('Error getting school ID:', error);
        toast({
          title: "Error",
          description: "Failed to load school information",
          variant: "destructive",
        });
      }
    };

    initializeData();
  }, [schoolSlug]);

  useEffect(() => {
    if (schoolId) {
      loadParents();
    }
  }, [schoolId]);

  const loadParents = async () => {
    if (!schoolId) return;

    setIsLoading(true);
    try {
      const parentsData = await parentsService.getAll(schoolId);

      // Transform the data to match the expected format
      const transformedParents = parentsData.map((parent: any) => ({
        id: parent.id,
        schoolId: parent.school_id,
        firstName: parent.profile?.first_name || '',
        lastName: parent.profile?.last_name || '',
        email: parent.profile?.email || '',
        phone: parent.profile?.phone || '',
        address: parent.profile?.address || '',
        occupation: parent.occupation || '',
        relationship: parent.relationship || '',
        children: parent.children?.map((child: any) => ({
          id: child.id,
          name: `${child.profile?.first_name || ''} ${child.profile?.last_name || ''}`.trim(),
          class: child.class ? `${child.class.grade_level}-${child.class.section || child.class.name}` : 'Not Assigned',
          grade: child.class?.grade_level || 0
        })) || [],
        emergencyContact: parent.emergency_contact || '',
        isActive: parent.is_active,
        hasCredentials: !!parent.profile?.email,
        lastLogin: parent.last_login || '',
        createdAt: parent.created_at || '',
      }));

      setParents(transformedParents);
    } catch (error) {
      console.error('Error loading parents:', error);
      toast({
        title: "Error",
        description: "Failed to load parents data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user is admin
  if (user?.role !== 'admin') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
          <p className="text-gray-600">Only administrators can access the parents management page.</p>
        </div>
      </div>
    );
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${(firstName || '').charAt(0)}${(lastName || '').charAt(0)}`.toUpperCase();
  };

  const handleViewParent = (parent: any) => {
    setSelectedParent(parent);
    setIsViewDialogOpen(true);
  };

  const handleToggleStatus = async (parentId: string) => {
    try {
      const parent = parents.find(p => p.id === parentId);
      if (!parent) return;

      await parentsService.update(parentId, { is_active: !parent.isActive });

      // Reload parents to get updated data
      await loadParents();

      toast({
        title: "Success",
        description: `Parent ${parent.isActive ? 'deactivated' : 'activated'} successfully`,
      });
    } catch (error) {
      console.error('Error toggling parent status:', error);
      toast({
        title: "Error",
        description: "Failed to update parent status",
        variant: "destructive",
      });
    }
  };

  const handleDeleteParent = async (parentId: string) => {
    try {
      await parentsService.delete(parentId);

      // Reload parents to get updated data
      await loadParents();

      toast({
        title: "Success",
        description: "Parent deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting parent:', error);
      toast({
        title: "Error",
        description: "Failed to delete parent",
        variant: "destructive",
      });
    }
  };

  const handleSetCredentials = (parent: any) => {
    setSelectedUserForCredentials({
      id: parent.id,
      firstName: parent.firstName,
      lastName: parent.lastName,
      role: 'parent',
      email: parent.email,
      hasCredentials: true, // You can track this in your data
    });
    setIsCredentialsDialogOpen(true);
  };

  const handleSaveCredentials = async (userId: string, credentials: { email: string; password: string }) => {
    try {
      // Update parent credentials in database
      await parentsService.update(userId, {
        // Note: You might need to update the profile table instead
        // depending on your database structure
      });

      // Reload parents to get updated data
      await loadParents();

      toast({
        title: "Success",
        description: "Credentials updated successfully",
      });

      return true;
    } catch (error) {
      console.error('Failed to set credentials:', error);
      toast({
        title: "Error",
        description: "Failed to update credentials",
        variant: "destructive",
      });
      return false;
    }
  };

  // Filter parents based on search and status
  const filteredParents = parents.filter(parent => {
    const matchesSearch =
      (parent.firstName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (parent.lastName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (parent.email?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (parent.children || []).some(child => (child.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()));

    const matchesStatus =
      filterStatus === 'all' ||
      (filterStatus === 'active' && parent.isActive) ||
      (filterStatus === 'inactive' && !parent.isActive);

    return matchesSearch && matchesStatus;
  });

  // Calculate statistics for the dashboard
  const stats = {
    total: parents.length,
    active: parents.filter(p => p.isActive).length,
    inactive: parents.filter(p => !p.isActive).length,
    totalChildren: parents.reduce((sum, p) => sum + p.children.length, 0),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Parents Management</h1>
            <p className="text-sm sm:text-base text-gray-600 mt-1">Manage parent accounts and information</p>
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <Button variant="outline" className="flex items-center gap-2 text-xs sm:text-sm px-2 sm:px-4">
              <Download className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="hidden sm:inline">Export</span>
              <span className="sm:hidden">Export</span>
            </Button>
            <Button className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-xs sm:text-sm px-2 sm:px-4">
              <UserPlus className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="hidden sm:inline">Add Parent</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Parents</p>
                <p className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="p-2 sm:p-3 bg-blue-100 rounded-full">
                <Users className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Active Parents</p>
                <p className="text-xl sm:text-2xl md:text-3xl font-bold text-green-600">{stats.active}</p>
              </div>
              <div className="p-2 sm:p-3 bg-green-100 rounded-full">
                <UserCheck className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Inactive Parents</p>
                <p className="text-xl sm:text-2xl md:text-3xl font-bold text-red-600">{stats.inactive}</p>
              </div>
              <div className="p-2 sm:p-3 bg-red-100 rounded-full">
                <UserX className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Children</p>
                <p className="text-xl sm:text-2xl md:text-3xl font-bold text-purple-600">{stats.totalChildren}</p>
              </div>
              <div className="p-2 sm:p-3 bg-purple-100 rounded-full">
                <Baby className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-3 sm:p-4 md:p-6">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search parents by name, email, or children..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 text-sm"
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="border border-gray-300 rounded-md px-2 sm:px-3 py-2 text-xs sm:text-sm"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Parents Table */}
      <Card>
        <CardHeader className="p-3 sm:p-4 md:p-6">
          <CardTitle className="text-lg sm:text-xl">Parents List</CardTitle>
          <CardDescription className="text-sm">
            {filteredParents.length} parent{filteredParents.length !== 1 ? 's' : ''} found
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading parents...</p>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs sm:text-sm">Parent</TableHead>
                    <TableHead className="text-xs sm:text-sm hidden md:table-cell">Contact</TableHead>
                    <TableHead className="text-xs sm:text-sm hidden lg:table-cell">Children</TableHead>
                    <TableHead className="text-xs sm:text-sm hidden lg:table-cell">Occupation</TableHead>
                    <TableHead className="text-xs sm:text-sm">Status</TableHead>
                    <TableHead className="text-xs sm:text-sm hidden sm:table-cell">Last Login</TableHead>
                    <TableHead className="text-xs sm:text-sm text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {filteredParents.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="text-gray-500">
                        <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                        <p>No parents found</p>
                        <p className="text-sm">Try adjusting your search or filters</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredParents.map((parent) => (
                <TableRow key={parent.id}>
                  <TableCell className="p-2 sm:p-4">
                    <div className="flex items-center gap-2 sm:gap-3">
                      <Avatar className="h-8 w-8 sm:h-10 sm:w-10">
                        <AvatarImage src="" />
                        <AvatarFallback className="bg-blue-100 text-blue-600 text-xs sm:text-sm">
                          {getInitials(parent.firstName, parent.lastName)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-sm sm:text-base truncate">{parent.firstName} {parent.lastName}</p>
                        <p className="text-xs sm:text-sm text-gray-500">{parent.relationship}</p>
                        <div className="md:hidden text-xs text-gray-500 mt-1">
                          <div className="flex items-center gap-1">
                            <Mail className="w-3 h-3" />
                            <span className="truncate">{parent.email}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="p-2 sm:p-4 hidden md:table-cell">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm">
                        <Mail className="w-3 h-3 text-gray-400" />
                        <span className="truncate">{parent.email}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="w-3 h-3 text-gray-400" />
                        {parent.phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="p-2 sm:p-4 hidden lg:table-cell">
                    <div className="space-y-1">
                      {parent.children.map((child, index) => (
                        <div key={index} className="text-sm">
                          <span className="font-medium">{child.name}</span>
                          <span className="text-gray-500 ml-2">({child.class})</span>
                        </div>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="p-2 sm:p-4 hidden lg:table-cell">
                    <span className="text-sm">{parent.occupation}</span>
                  </TableCell>
                  <TableCell className="p-2 sm:p-4">
                    <div className="flex flex-col gap-1">
                      <Badge variant={parent.isActive ? "default" : "secondary"} className="text-xs w-fit">
                        {parent.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      {parent.hasCredentials && (
                        <Badge variant="outline" className="bg-green-100 text-green-800 text-xs w-fit">
                          Has Login
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="p-2 sm:p-4 hidden sm:table-cell">
                    <span className="text-xs sm:text-sm text-gray-500">
                      {new Date(parent.lastLogin).toLocaleDateString()}
                    </span>
                  </TableCell>
                  <TableCell className="p-2 sm:p-4 text-right">
                    <div className="flex items-center justify-end gap-1 sm:gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetCredentials(parent)}
                        className="text-green-600 hover:text-green-700 border-green-200 hover:border-green-300 text-xs sm:text-sm px-2 sm:px-3"
                      >
                        <Key className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        <span className="hidden sm:inline">Set Password</span>
                        <span className="sm:hidden">Set</span>
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8 sm:h-10 sm:w-10">
                            <MoreHorizontal className="w-3 h-3 sm:w-4 sm:h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleViewParent(parent)}>
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit Parent
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleToggleStatus(parent.id)}>
                            {parent.isActive ? (
                              <>
                                <UserX className="w-4 h-4 mr-2" />
                                Deactivate
                              </>
                            ) : (
                              <>
                                <UserCheck className="w-4 h-4 mr-2" />
                                Activate
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteParent(parent.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Parent Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Parent Details</DialogTitle>
            <DialogDescription>
              Complete information about the parent
            </DialogDescription>
          </DialogHeader>
          {selectedParent && (
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <Avatar className="w-16 h-16">
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-blue-100 text-blue-600 text-lg">
                    {getInitials(selectedParent.firstName, selectedParent.lastName)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-xl font-semibold">
                    {selectedParent.firstName} {selectedParent.lastName}
                  </h3>
                  <p className="text-gray-600">{selectedParent.relationship}</p>
                  <Badge variant={selectedParent.isActive ? "default" : "secondary"}>
                    {selectedParent.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3">Contact Information</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{selectedParent.email}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{selectedParent.phone}</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                      <span className="text-sm">{selectedParent.address}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">Additional Information</h4>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium">Occupation: </span>
                      <span className="text-sm">{selectedParent.occupation}</span>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Emergency Contact: </span>
                      <span className="text-sm">{selectedParent.emergencyContact}</span>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Last Login: </span>
                      <span className="text-sm">
                        {new Date(selectedParent.lastLogin).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Children</h4>
                <div className="space-y-2">
                  {selectedParent.children.map((child: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{child.name}</p>
                        <p className="text-sm text-gray-600">Grade {child.grade} - Class {child.class}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>


    </div>
  );
};
