import React, { useState } from 'react';
import { useAuth } from '@/contexts/NewAuthContext';
import { MultiTenantService } from '@/services/multiTenantService';

import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  UserPlus,
  Users,
  GraduationCap,
  Heart,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Copy,
  ExternalLink,
  Share2,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

const userSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  role: z.enum(['student', 'teacher', 'parent']),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  // Student specific fields
  studentId: z.string().optional(),
  class: z.string().optional(),
  section: z.string().optional(),
  rollNumber: z.string().optional(),
  admissionDate: z.string().optional(),
  // Teacher specific fields
  employeeId: z.string().optional(),
  department: z.string().optional(),
  subjects: z.string().optional(),
  qualifications: z.string().optional(),
  joiningDate: z.string().optional(),
  // Parent specific fields
  occupation: z.string().optional(),
  relationship: z.string().optional(),
  emergencyContact: z.string().optional(),
  address: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type UserForm = z.infer<typeof userSchema>;

export const UserManagement: React.FC = () => {
  const { user } = useAuth();
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [copiedLink, setCopiedLink] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    watch,
    reset,
  } = useForm<UserForm>({
    resolver: zodResolver(userSchema),
  });

  const watchedRole = watch('role');

  // Check if user is admin
  if (user?.role !== 'admin') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
          <p className="text-gray-600">Only administrators can access user management.</p>
        </div>
      </div>
    );
  }

  const onSubmit = async (data: UserForm) => {
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Get current school context
      const currentUser = await MultiTenantService.getCurrentUserProfile();
      if (!currentUser || !currentUser.school_id) {
        throw new Error('No school context available');
      }

      // Generate a temporary password
      const tempPassword = MultiTenantService.generateSecurePassword();

      const result = await MultiTenantService.createSchoolUser(currentUser.school_id, {
        email: data.email,
        password: tempPassword,
        role: data.role as any,
        first_name: data.firstName,
        last_name: data.lastName,
        phone: data.phone
      });

      if (!result) {
        throw new Error('Failed to create user account');
      }

      setSuccessMessage(`${data.role.charAt(0).toUpperCase() + data.role.slice(1)} account created successfully! Login credentials: Email: ${data.email}, Password: ${tempPassword}`);
      reset();
      setIsAddUserOpen(false);

    } catch (err: any) {
      setError(err.message || 'Failed to create user account. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const generateSchoolLink = () => {
    return `${window.location.origin}/dashboard`;
  };

  const copySchoolLink = async () => {
    const link = generateSchoolLink();
    try {
      await navigator.clipboard.writeText(link);
      setCopiedLink(link);
      setTimeout(() => setCopiedLink(null), 3000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const shareSchoolLink = async () => {
    const link = generateSchoolLink();
    if (navigator.share) {
      try {
        await navigator.share({
          title: `School Management - Dashboard`,
          text: `Access the school management dashboard`,
          url: link,
        });
      } catch (err) {
        console.error('Failed to share:', err);
      }
    } else {
      copySchoolLink();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">User Management</h1>
            <p className="text-sm sm:text-base text-gray-600 mt-1">Add and manage students, teachers, and parents</p>
          </div>
          <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-xs sm:text-sm px-2 sm:px-4">
                <UserPlus className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Add User</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New User</DialogTitle>
              <DialogDescription>
                Create a new account for a student, teacher, or parent
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      placeholder="John"
                      {...register('firstName')}
                    />
                    {errors.firstName && (
                      <p className="text-sm text-red-600 mt-1">{errors.firstName.message}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      placeholder="Doe"
                      {...register('lastName')}
                    />
                    {errors.lastName && (
                      <p className="text-sm text-red-600 mt-1">{errors.lastName.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      {...register('email')}
                    />
                    {errors.email && (
                      <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      placeholder="******-0123"
                      {...register('phone')}
                    />
                    {errors.phone && (
                      <p className="text-sm text-red-600 mt-1">{errors.phone.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="role">Role *</Label>
                  <Controller
                    name="role"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select user role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="student">Student</SelectItem>
                          <SelectItem value="teacher">Teacher</SelectItem>
                          <SelectItem value="parent">Parent</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.role && (
                    <p className="text-sm text-red-600 mt-1">{errors.role.message}</p>
                  )}
                </div>
              </div>

              {/* Role-specific fields */}
              {watchedRole === 'student' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Student Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="studentId">Student ID</Label>
                      <Input
                        id="studentId"
                        placeholder="STU001"
                        {...register('studentId')}
                      />
                    </div>
                    <div>
                      <Label htmlFor="class">Class</Label>
                      <Input
                        id="class"
                        placeholder="10-A"
                        {...register('class')}
                      />
                    </div>
                    <div>
                      <Label htmlFor="section">Section</Label>
                      <Input
                        id="section"
                        placeholder="A"
                        {...register('section')}
                      />
                    </div>
                    <div>
                      <Label htmlFor="rollNumber">Roll Number</Label>
                      <Input
                        id="rollNumber"
                        placeholder="001"
                        {...register('rollNumber')}
                      />
                    </div>
                    <div>
                      <Label htmlFor="admissionDate">Admission Date</Label>
                      <Input
                        id="admissionDate"
                        type="date"
                        {...register('admissionDate')}
                      />
                    </div>
                  </div>
                </div>
              )}

              {watchedRole === 'teacher' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Teacher Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="employeeId">Employee ID</Label>
                      <Input
                        id="employeeId"
                        placeholder="T001"
                        {...register('employeeId')}
                      />
                    </div>
                    <div>
                      <Label htmlFor="department">Department</Label>
                      <Input
                        id="department"
                        placeholder="Mathematics"
                        {...register('department')}
                      />
                    </div>
                    <div>
                      <Label htmlFor="subjects">Subjects (comma separated)</Label>
                      <Input
                        id="subjects"
                        placeholder="Math, Physics"
                        {...register('subjects')}
                      />
                    </div>
                    <div>
                      <Label htmlFor="joiningDate">Joining Date</Label>
                      <Input
                        id="joiningDate"
                        type="date"
                        {...register('joiningDate')}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="qualifications">Qualifications</Label>
                    <Textarea
                      id="qualifications"
                      placeholder="M.Sc Mathematics, B.Ed"
                      {...register('qualifications')}
                    />
                  </div>
                </div>
              )}

              {watchedRole === 'parent' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Parent Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="occupation">Occupation</Label>
                      <Input
                        id="occupation"
                        placeholder="Engineer"
                        {...register('occupation')}
                      />
                    </div>
                    <div>
                      <Label htmlFor="relationship">Relationship</Label>
                      <Select onValueChange={(value) => register('relationship').onChange({ target: { value } })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select relationship" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="father">Father</SelectItem>
                          <SelectItem value="mother">Mother</SelectItem>
                          <SelectItem value="guardian">Guardian</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="emergencyContact">Emergency Contact</Label>
                      <Input
                        id="emergencyContact"
                        placeholder="******-0124"
                        {...register('emergencyContact')}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="address">Address</Label>
                    <Textarea
                      id="address"
                      placeholder="Complete address"
                      {...register('address')}
                    />
                  </div>
                </div>
              )}

              {/* Password */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Login Credentials</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="password">Password *</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Minimum 8 characters"
                      {...register('password')}
                    />
                    {errors.password && (
                      <p className="text-sm text-red-600 mt-1">{errors.password.message}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="confirmPassword">Confirm Password *</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder="Confirm password"
                      {...register('confirmPassword')}
                    />
                    {errors.confirmPassword && (
                      <p className="text-sm text-red-600 mt-1">{errors.confirmPassword.message}</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <Button type="button" variant="outline" onClick={() => setIsAddUserOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </Button>
              </div>
            </form>
          </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            {successMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* School Portal Link */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            School Portal Link
          </CardTitle>
          <CardDescription>
            Share this link with students, teachers, and parents to access their portals
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="flex-1">
              <p className="font-mono text-sm break-all">{generateSchoolLink()}</p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={copySchoolLink}
                title="Copy link"
              >
                <Copy className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={shareSchoolLink}
                title="Share link"
              >
                <Share2 className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => window.open(generateSchoolLink(), '_blank')}
                title="Open in new tab"
              >
                <ExternalLink className="w-4 h-4" />
              </Button>
            </div>
          </div>
          {copiedLink && (
            <p className="text-sm text-green-600 mt-2">✓ Link copied to clipboard!</p>
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-xl sm:text-2xl md:text-3xl font-bold text-blue-600">150</p>
              </div>
              <div className="p-2 sm:p-3 bg-blue-100 rounded-full">
                <GraduationCap className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Teachers</p>
                <p className="text-xl sm:text-2xl md:text-3xl font-bold text-purple-600">25</p>
              </div>
              <div className="p-2 sm:p-3 bg-purple-100 rounded-full">
                <User className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Parents</p>
                <p className="text-xl sm:text-2xl md:text-3xl font-bold text-green-600">0</p>
              </div>
              <div className="p-2 sm:p-3 bg-green-100 rounded-full">
                <Heart className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Add Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold text-blue-600">1</span>
              </div>
              <div>
                <h4 className="font-semibold">Create User Accounts</h4>
                <p className="text-sm text-gray-600">Use the "Add User" button to create accounts for students, teachers, and parents with their login credentials.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold text-blue-600">2</span>
              </div>
              <div>
                <h4 className="font-semibold">Share School Portal Link</h4>
                <p className="text-sm text-gray-600">Copy and share the school portal link with users so they can access their respective portals.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold text-blue-600">3</span>
              </div>
              <div>
                <h4 className="font-semibold">Users Login</h4>
                <p className="text-sm text-gray-600">Users can visit the portal link, select their role, and login with the credentials you provided.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
