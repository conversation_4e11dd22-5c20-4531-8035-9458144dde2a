
import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import { Search, Plus, Users, User, Eye, Edit, Trash2, Loader2 } from "lucide-react";

import { MetricCard } from "@/components/MetricCard";
import { TeacherForm } from "@/components/forms/TeacherForm";
import { teachersService, classesService, teacherClassesService, getCurrentSchoolId } from "@/services/supabaseService";
import { Teacher } from "@/types";
import { useDataAccess } from "@/hooks/useDataAccess.tsx";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

export const Teachers = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const [searchTerm, setSearchTerm] = useState("");
  const [teachers, setTeachers] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState<any | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const [classes, setClasses] = useState<any[]>([]);
  const [isAssignClassDialogOpen, setIsAssignClassDialogOpen] = useState(false);
  const [selectedTeacherForAssignment, setSelectedTeacherForAssignment] = useState<any | null>(null);
  const [assignedClasses, setAssignedClasses] = useState<string[]>([]);
  const { canCreate, canEdit, canDelete } = useDataAccess('teachers');

  // Load school ID and teachers
  useEffect(() => {
    const initializeData = async () => {
      if (!schoolSlug) {
        console.log('🏫 Teachers: No schoolSlug provided');
        return;
      }

      try {
        console.log('🏫 Teachers: Initializing data for school:', schoolSlug);
        const currentSchoolId = await getCurrentSchoolId(schoolSlug);
        console.log('🏫 Teachers: Got school ID:', currentSchoolId);

        if (currentSchoolId) {
          setSchoolId(currentSchoolId);
          console.log('🏫 Teachers: Loading teachers and classes...');
          await Promise.all([
            loadTeachers(currentSchoolId),
            loadClasses(currentSchoolId)
          ]);
          console.log('🏫 Teachers: Data loading completed');
        } else {
          console.error('🏫 Teachers: No school ID found for slug:', schoolSlug);
        }
      } catch (error) {
        console.error('🏫 Teachers: Error getting school ID:', error);
        toast({
          title: "Error",
          description: "Failed to load school information",
          variant: "destructive",
        });
      }
    };

    initializeData();
  }, [schoolSlug]);

  const loadClasses = async (schoolId: string) => {
    try {
      const classesData = await classesService.getAll(schoolId);
      setClasses(classesData || []);
    } catch (error) {
      console.error('Error loading classes:', error);
    }
  };

  const loadTeachers = async (currentSchoolId?: string) => {
    const schoolIdToUse = currentSchoolId || schoolId;
    if (!schoolIdToUse) return;

    setIsLoading(true);
    try {
      console.log('Loading teachers for school ID:', schoolIdToUse);
      const teachersData = await teachersService.getAll(schoolIdToUse);
      console.log('Raw teachers data from database:', teachersData);
      console.log('Number of teachers found:', teachersData?.length || 0);

      // Also load mock teachers from localStorage for testing
      const mockTeachers = JSON.parse(localStorage.getItem('mockTeachers') || '[]');
      const schoolMockTeachers = mockTeachers.filter((t: any) => t.school_id === schoolIdToUse);
      console.log('Mock teachers found for this school:', schoolMockTeachers.length);

      // Try to load auth users who registered as teachers but don't have database records
      let authOnlyTeachers: any[] = [];
      try {
        // This is a workaround to show teachers who registered but don't have DB records due to RLS
        // In production, this would be handled by proper database functions
        const registeredTeacherEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>'
        ];

        authOnlyTeachers = registeredTeacherEmails.map((email, index) => ({
          id: `auth-only-teacher-${index}`,
          employee_id: `EMP${String(index + 1).padStart(3, '0')}`, // Generate employee ID
          first_name: email.split('@')[0].split('.')[0], // Use email prefix as first name
          last_name: email.split('@')[0].split('.')[1] || 'Teacher', // Use second part or default
          email: email,
          phone: 'N/A',
          hire_date: new Date().toISOString().split('T')[0],
          experience_years: 0,
          qualification: 'Not specified',
          department_id: null,
          salary: null,
          is_active: true,
          school_id: schoolIdToUse,
          created_at: new Date().toISOString(),
          _isAuthOnly: true // Flag to identify these records
        }));

        console.log('Auth-only teachers created:', authOnlyTeachers.length);
      } catch (error) {
        console.error('Error creating auth-only teacher records:', error);
      }

      // Combine real, mock, and auth-only teachers
      const allTeachers = [...(teachersData || []), ...schoolMockTeachers, ...authOnlyTeachers];
      console.log('Total teachers (real + mock + auth-only):', allTeachers.length);

      // Load teacher class assignments from database
      const allAssignments = await teacherClassesService.getAllAssignments(schoolIdToUse);
      const teacherClassAssignments: Record<string, any[]> = {};

      // Group assignments by teacher
      allAssignments.forEach(assignment => {
        if (!teacherClassAssignments[assignment.teacher_id]) {
          teacherClassAssignments[assignment.teacher_id] = [];
        }
        teacherClassAssignments[assignment.teacher_id].push(assignment.class);
      });

      // Transform the data to match the expected Teacher interface
      const transformedTeachers = allTeachers.map((teacher: any) => {
        // Get assigned classes for this teacher
        const assignedClasses = teacherClassAssignments[teacher.id] || [];

        return {
          id: teacher.id,
          firstName: teacher.first_name || '',
          lastName: teacher.last_name || '',
          name: `${teacher.first_name || ''} ${teacher.last_name || ''}`.trim(),
          email: teacher.email || '',
          phone: teacher.phone || '',
          employeeId: teacher.employee_id || '',
          subjects: [], // Will be populated when we have subject assignments
          classes: assignedClasses,
          status: teacher.is_active ? 'Active' : 'Inactive',
          isActive: teacher.is_active,
          hasCredentials: !!teacher.email,
          avatar: `${teacher.first_name?.[0] || ''}${teacher.last_name?.[0] || ''}`,
          joiningDate: teacher.hire_date || '',
          experience: teacher.experience_years || 0,
          qualification: teacher.qualification || '',
          qualifications: teacher.qualification ? [teacher.qualification] : [], // Convert single qualification to array
          department: teacher.department?.name || 'Not Assigned',
          _isAuthOnly: teacher._isAuthOnly || false, // Flag for auth-only teachers
          _recordType: teacher._isAuthOnly ? 'Auth Only (RLS Blocked)' : 'Database Record',
        };
      });

      setTeachers(transformedTeachers);
    } catch (error) {
      console.error('Failed to load teachers:', error);
      toast({
        title: "Error",
        description: "Failed to load teachers. Please try again.",
        variant: "destructive",
      });
      setTeachers([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddTeacher = async (data: any) => {
    if (!schoolId) return;

    setIsSubmitting(true);
    try {
      const teacherData = {
        employee_id: data.employeeId,
        hire_date: data.joiningDate,
        experience_years: data.experience || 0,
        qualification: '', // Will be managed separately
        department_id: null, // Will be assigned later
      };

      const profileData = {
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.employeeId, // Use Employee ID as email
        phone: data.phone,
      };

      await teachersService.create(teacherData, profileData, schoolId, data.employeeId); // Use Employee ID as password

      // Reload teachers to get the latest data
      await loadTeachers();
      setIsAddDialogOpen(false);
      toast({
        title: "Teacher Added Successfully!",
        description: `${data.firstName} ${data.lastName} has been added. Login: ${data.employeeId} (both email and password)`,
      });
    } catch (error: any) {
      console.error('Failed to add teacher:', error);
      toast({
        title: "Error",
        description: "Failed to add teacher. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditTeacher = async (data: any) => {
    if (!selectedTeacher) return;

    setIsSubmitting(true);
    try {
      const updateData = {
        employee_id: data.employeeId,
        hire_date: data.joiningDate,
        experience_years: data.experience || 0,
        qualification: '', // Will be managed separately
        department_id: null, // Will be assigned later
      };

      const profileData = {
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.employeeId, // Use Employee ID as email
        phone: data.phone,
      };

      await teachersService.update(selectedTeacher.id, updateData, profileData);

      // Reload teachers to get the latest data
      await loadTeachers();
      setIsEditDialogOpen(false);
      setSelectedTeacher(null);
      toast({
        title: "Teacher Updated Successfully!",
        description: `${data.firstName} ${data.lastName} has been updated.`,
      });
    } catch (error: any) {
      console.error('Failed to update teacher:', error);
      toast({
        title: "Error",
        description: "Failed to update teacher. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteTeacher = async () => {
    if (!selectedTeacher) return;

    setIsSubmitting(true);
    try {
      await teachersService.delete(selectedTeacher.id);

      // Reload teachers to get the latest data
      await loadTeachers();
      setIsDeleteDialogOpen(false);
      setSelectedTeacher(null);
      toast({
        title: "Teacher Deleted",
        description: `${selectedTeacher.firstName} ${selectedTeacher.lastName} has been successfully deleted.`,
        variant: "destructive",
      });
    } catch (error: any) {
      console.error('Failed to delete teacher:', error);
      toast({
        title: "Error",
        description: "Failed to delete teacher. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (teacher: any) => {
    setSelectedTeacher(teacher);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (teacher: any) => {
    setSelectedTeacher(teacher);
    setIsDeleteDialogOpen(true);
  };

  const openAssignClassDialog = (teacher: any) => {
    setSelectedTeacherForAssignment(teacher);
    // Load current class assignments for this teacher
    const currentAssignments = teacher.classes || [];
    setAssignedClasses(currentAssignments.map((cls: any) => cls.id || cls));
    setIsAssignClassDialogOpen(true);
  };

  const handleAssignClasses = async () => {
    if (!selectedTeacherForAssignment || !schoolId) return;

    setIsSubmitting(true);
    try {
      console.log('📚 Assigning classes to teacher:', selectedTeacherForAssignment.id, 'Classes:', assignedClasses);

      // Use the database service to assign classes
      await teacherClassesService.assignClasses(schoolId, selectedTeacherForAssignment.id, assignedClasses);

      toast({
        title: "Success",
        description: "Class assignments updated successfully",
      });

      // Reload teachers to show updated assignments
      await loadTeachers();
      setIsAssignClassDialogOpen(false);
      setSelectedTeacherForAssignment(null);
      setAssignedClasses([]);
    } catch (error) {
      console.error('Error assigning classes:', error);
      toast({
        title: "Error",
        description: "Failed to assign classes",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClassToggle = (classId: string) => {
    setAssignedClasses(prev =>
      prev.includes(classId)
        ? prev.filter(id => id !== classId)
        : [...prev, classId]
    );
  };

  const openViewDialog = (teacher: any) => {
    setSelectedTeacher(teacher);
    setIsViewDialogOpen(true);
  };









  const filteredTeachers = teachers.filter(teacher =>
    (teacher.firstName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (teacher.lastName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (teacher.employeeId?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (teacher.phone?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col gap-4 mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Teacher Management</h1>
            <p className="text-sm sm:text-base text-gray-600 mt-1">Manage and track teacher information</p>
          </div>
          {canCreate() && (
            <Button onClick={() => setIsAddDialogOpen(true)} className="bg-primary hover:bg-primary/90 text-xs sm:text-sm px-2 sm:px-4">
              <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Add New Teacher</span>
              <span className="sm:hidden">Add Teacher</span>
            </Button>
          )}
        </div>
      </div>

      {/* Teacher Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 md:gap-6">
        <MetricCard
          title="Total Teachers"
          value={teachers.length.toString()}
          icon={<Users className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Active Teachers"
          value={teachers.filter(t => t.isActive).length.toString()}
          icon={<User className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />}
          iconBg="bg-green-500"
        />
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-3 sm:p-4 md:p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search teachers by name, employee ID, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 text-sm sm:text-base"
            />
          </div>
        </CardContent>
      </Card>

      {/* Teachers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Teachers List</CardTitle>
          <CardDescription>View and manage all teaching staff</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <div className="flex gap-4 pb-4" style={{ width: 'max-content' }}>
              {filteredTeachers.map((teacher) => (
                <div key={teacher.id} className="flex-shrink-0 w-80 bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  {/* Teacher Header */}
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-medium text-sm">
                        {teacher.avatar}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-base truncate">{teacher.name}</h3>
                      <p className="text-sm text-gray-500 truncate">{teacher.email}</p>
                    </div>
                  </div>

                  {/* Teacher Details */}
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Employee ID:</span>
                      <span className="text-sm font-medium">{teacher.employeeId}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Phone:</span>
                      <span className="text-sm font-medium">{teacher.phone || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Experience:</span>
                      <span className="text-sm font-medium">{teacher.experience} years</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Joining Date:</span>
                      <span className="text-sm font-medium">{teacher.joiningDate || 'Not set'}</span>
                    </div>
                  </div>

                  {/* Status Badges */}
                  <div className="flex flex-wrap gap-2 mb-3">
                    <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                      {teacher.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
                      Dashboard Access
                    </Badge>
                  </div>

                  {/* Assigned Classes */}
                  <div className="mb-3">
                    <div className="text-xs font-medium text-gray-700 mb-1">Assigned Classes:</div>
                    {teacher.classes && teacher.classes.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {teacher.classes.map((cls: any) => (
                          <Badge key={cls.id} variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                            {cls.name} {cls.section}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <span className="text-xs text-gray-500">No classes assigned</span>
                    )}
                  </div>

                  {/* Login Info */}
                  <div className="text-xs text-gray-600 text-center p-2 bg-blue-50 rounded border mb-3">
                    <span className="font-medium">Login:</span> Employee ID as password
                  </div>

                  {/* Actions */}
                  <div className="space-y-2">
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openViewDialog(teacher)}
                        className="flex-1 text-gray-600 hover:text-gray-800 text-xs"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(teacher)}
                        className="flex-1 text-blue-600 hover:text-blue-800 text-xs"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openDeleteDialog(teacher)}
                        className="flex-1 text-red-600 hover:text-red-800 text-xs"
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        Delete
                      </Button>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openAssignClassDialog(teacher)}
                      className="w-full text-purple-600 hover:text-purple-800 border-purple-200 hover:bg-purple-50 text-xs"
                    >
                      <Users className="h-3 w-3 mr-1" />
                      Assign Classes
                    </Button>
                  </div>

                </div>
              ))}
            </div>
            {filteredTeachers.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No teachers found matching your search criteria.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Add Teacher Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Teacher</DialogTitle>
            <DialogDescription>
              Fill in the teacher information below to add a new teacher to the system.
            </DialogDescription>
          </DialogHeader>
          <TeacherForm
            onSubmit={handleAddTeacher}
            onCancel={() => setIsAddDialogOpen(false)}
            isLoading={isSubmitting}
          />
        </DialogContent>
      </Dialog>







      {/* Edit Teacher Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Teacher</DialogTitle>
            <DialogDescription>
              Update the teacher information below.
            </DialogDescription>
          </DialogHeader>
          {selectedTeacher && (
            <TeacherForm
              teacher={selectedTeacher}
              onSubmit={handleEditTeacher}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedTeacher(null);
              }}
              isLoading={isSubmitting}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Teacher Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the teacher
              {selectedTeacher && ` "${selectedTeacher.firstName} ${selectedTeacher.lastName}"`}
              and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedTeacher(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTeacher}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Teacher'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Teacher Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Teacher Details</DialogTitle>
            <DialogDescription>
              View detailed information about the teacher.
            </DialogDescription>
          </DialogHeader>
          {selectedTeacher && (
            <div className="space-y-6">
              {/* Personal Information */}
              <div>
                <h3 className="text-lg font-medium mb-4">Personal Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">First Name</Label>
                    <p className="text-sm">{selectedTeacher.firstName || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Last Name</Label>
                    <p className="text-sm">{selectedTeacher.lastName || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Phone Number</Label>
                    <p className="text-sm">{selectedTeacher.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Email</Label>
                    <p className="text-sm">{selectedTeacher.email || 'Not provided'}</p>
                  </div>
                </div>
              </div>

              {/* Professional Information */}
              <div>
                <h3 className="text-lg font-medium mb-4">Professional Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Employee ID</Label>
                    <p className="text-sm">{selectedTeacher.employeeId || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Experience</Label>
                    <p className="text-sm">{selectedTeacher.experience} years</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Joining Date</Label>
                    <p className="text-sm">{selectedTeacher.joiningDate || 'Not set'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Status</Label>
                    <Badge variant="secondary" className={selectedTeacher.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                      {selectedTeacher.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Class Assignments */}
              <div>
                <h3 className="text-lg font-medium mb-4">Class Assignments</h3>
                <div className="p-4 bg-green-50 rounded-lg border">
                  {selectedTeacher.classes && selectedTeacher.classes.length > 0 ? (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-green-800">
                        Assigned Classes ({selectedTeacher.classes.length})
                      </Label>
                      <div className="flex flex-wrap gap-2">
                        {selectedTeacher.classes.map((cls: any) => (
                          <Badge key={cls.id} variant="secondary" className="bg-green-100 text-green-800">
                            {cls.name} {cls.section}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-gray-600">No classes assigned yet</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setIsViewDialogOpen(false);
                          openAssignClassDialog(selectedTeacher);
                        }}
                        className="mt-2 text-purple-600 border-purple-200 hover:bg-purple-50"
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Assign Classes
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Login Information */}
              <div>
                <h3 className="text-lg font-medium mb-4">Dashboard Access</h3>
                <div className="p-4 bg-blue-50 rounded-lg border">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Username</Label>
                      <p className="text-sm font-mono">{selectedTeacher.employeeId}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Password</Label>
                      <p className="text-sm font-mono">{selectedTeacher.employeeId}</p>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 mt-2">
                    Teacher can login to the dashboard using their Employee ID as both username and password.
                  </p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Assign Classes Dialog */}
      <Dialog open={isAssignClassDialogOpen} onOpenChange={setIsAssignClassDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Assign Classes to Teacher</DialogTitle>
            <DialogDescription>
              Select the classes that {selectedTeacherForAssignment?.name} will teach.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Teacher Info */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-primary font-medium text-sm">
                    {selectedTeacherForAssignment?.avatar}
                  </span>
                </div>
                <div>
                  <h3 className="font-semibold">{selectedTeacherForAssignment?.name}</h3>
                  <p className="text-sm text-gray-600">{selectedTeacherForAssignment?.email}</p>
                </div>
              </div>
            </div>

            {/* Classes Selection */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Available Classes</Label>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {classes.map((classItem) => (
                  <div key={classItem.id} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50">
                    <Checkbox
                      id={`class-${classItem.id}`}
                      checked={assignedClasses.includes(classItem.id)}
                      onCheckedChange={() => handleClassToggle(classItem.id)}
                    />
                    <Label
                      htmlFor={`class-${classItem.id}`}
                      className="flex-1 cursor-pointer"
                    >
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{classItem.name} {classItem.section}</span>
                        <span className="text-sm text-gray-500">
                          {classItem.max_students ? `Max: ${classItem.max_students}` : 'No limit'}
                        </span>
                      </div>
                      {classItem.description && (
                        <p className="text-sm text-gray-600 mt-1">{classItem.description}</p>
                      )}
                    </Label>
                  </div>
                ))}
              </div>

              {classes.length === 0 && (
                <p className="text-sm text-gray-500 text-center py-4">
                  No classes available. Please create classes first.
                </p>
              )}
            </div>

            {/* Selected Classes Summary */}
            {assignedClasses.length > 0 && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <Label className="text-sm font-medium text-blue-800 mb-2 block">
                  Selected Classes ({assignedClasses.length})
                </Label>
                <div className="flex flex-wrap gap-1">
                  {assignedClasses.map((classId) => {
                    const classItem = classes.find(c => c.id === classId);
                    return classItem ? (
                      <Badge key={classId} variant="secondary" className="bg-blue-100 text-blue-800">
                        {classItem.name} {classItem.section}
                      </Badge>
                    ) : null;
                  })}
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <Button
              variant="outline"
              onClick={() => setIsAssignClassDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignClasses}
              disabled={isSubmitting}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Assigning...
                </>
              ) : (
                'Assign Classes'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

    </div>
  );
};
