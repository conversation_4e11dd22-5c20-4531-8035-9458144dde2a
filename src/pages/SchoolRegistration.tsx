
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { School, GraduationCap, MapPin, Globe, CheckCircle } from 'lucide-react';
import { PublicHeader } from '@/components/PublicHeader';
import { PublicFooter } from '@/components/PublicFooter';
import { MultiTenantService, type SchoolRegistrationData } from '@/services/multiTenantService';

export const SchoolRegistration: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    address: '',
    email: '',
    phone: '',
    website: '',
    principal_name: '',
    admin_first_name: '',
    admin_last_name: '',
    admin_email: '',
    admin_phone: ''
  });
  const [loading, setLoading] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [checkingSlug, setCheckingSlug] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Auto-generate slug from school name
  const handleSchoolNameChange = (name: string) => {
    const slug = MultiTenantService.generateSlug(name);

    setFormData(prev => ({
      ...prev,
      name,
      slug
    }));

    // Check slug availability with debouncing
    if (slug.length >= 3) {
      setTimeout(() => checkSlugAvailability(slug), 500);
    }
  };

  const checkSlugAvailability = async (slug: string) => {
    if (!slug || slug.length < 3) {
      setSlugAvailable(null);
      return;
    }

    setCheckingSlug(true);
    try {
      const available = await MultiTenantService.checkSlugAvailability(slug);
      setSlugAvailable(available);
    } catch (error) {
      console.error('Error checking slug availability:', error);
      setSlugAvailable(null);
    } finally {
      setCheckingSlug(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.name || !formData.slug || !formData.address || !formData.email ||
        !formData.admin_first_name || !formData.admin_last_name || !formData.admin_email) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    if (slugAvailable === false) {
      toast({
        title: "Error",
        description: "School URL is already taken. Please choose a different name.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      const registrationData: SchoolRegistrationData = {
        name: formData.name,
        slug: formData.slug,
        address: formData.address,
        email: formData.email,
        phone: formData.phone,
        website: formData.website,
        principal_name: formData.principal_name,
        admin_first_name: formData.admin_first_name,
        admin_last_name: formData.admin_last_name,
        admin_email: formData.admin_email,
        admin_phone: formData.admin_phone
      };

      const result = await MultiTenantService.registerSchoolWithAdmin(registrationData);

      if (!result) {
        throw new Error('Failed to register school');
      }

      const { school, adminCredentials } = result;

      // Set up default school data
      await MultiTenantService.setupSchoolDefaults(school.id);

      toast({
        title: "Success!",
        description: `${school.name} has been registered successfully! Your admin account is ready.`,
      });

      // Navigate to success page with credentials
      navigate(`/school-registered`, {
        state: {
          school,
          adminCredentials,
          schoolPortalLink: `${window.location.origin}/${school.slug}/login`
        }
      });

    } catch (error: any) {
      console.error('Registration error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to register school. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <PublicHeader />
      
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <School className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Register Your School</h1>
            <p className="text-gray-600">Create your school's management portal in minutes</p>
          </div>

          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5" />
                School Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">School Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleSchoolNameChange(e.target.value)}
                      placeholder="Greenwood High School"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="slug">School URL *</Label>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 mr-1">sms.app/</span>
                      <Input
                        id="slug"
                        value={formData.slug}
                        onChange={(e) => {
                          setFormData(prev => ({ ...prev, slug: e.target.value }));
                          if (e.target.value.length >= 3) {
                            setTimeout(() => checkSlugAvailability(e.target.value), 500);
                          } else {
                            setSlugAvailable(null);
                          }
                        }}
                        placeholder="greenwood"
                        required
                        className={slugAvailable === false ? 'border-red-500' : slugAvailable === true ? 'border-green-500' : ''}
                      />
                      {checkingSlug && <span className="ml-2 text-sm text-gray-500">Checking...</span>}
                      {slugAvailable === true && <CheckCircle className="ml-2 h-4 w-4 text-green-500" />}
                      {slugAvailable === false && <span className="ml-2 text-sm text-red-500">Taken</span>}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">School Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="+****************"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">School Address *</Label>
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    placeholder="123 Education Street, Learning City, State 12345"
                    rows={2}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                      placeholder="https://www.greenwood.edu"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="principal_name">Principal Name</Label>
                    <Input
                      id="principal_name"
                      value={formData.principal_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, principal_name: e.target.value }))}
                      placeholder="Dr. Jane Smith"
                    />
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Administrator Account
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="admin_first_name">First Name *</Label>
                      <Input
                        id="admin_first_name"
                        value={formData.admin_first_name}
                        onChange={(e) => setFormData(prev => ({ ...prev, admin_first_name: e.target.value }))}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="admin_last_name">Last Name *</Label>
                      <Input
                        id="admin_last_name"
                        value={formData.admin_last_name}
                        onChange={(e) => setFormData(prev => ({ ...prev, admin_last_name: e.target.value }))}
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="admin_email">Email *</Label>
                      <Input
                        id="admin_email"
                        type="email"
                        value={formData.admin_email}
                        onChange={(e) => setFormData(prev => ({ ...prev, admin_email: e.target.value }))}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="admin_phone">Phone</Label>
                      <Input
                        id="admin_phone"
                        value={formData.admin_phone}
                        onChange={(e) => setFormData(prev => ({ ...prev, admin_phone: e.target.value }))}
                        placeholder="+****************"
                      />
                    </div>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  disabled={loading || slugAvailable === false}
                >
                  {loading ? "Creating School..." : "Register School"}
                </Button>

                <p className="text-sm text-gray-600 text-center mt-4">
                  By registering, you agree to our Terms of Service and Privacy Policy.
                  A temporary password will be sent to your email.
                </p>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>

      <PublicFooter />
    </div>
  );
};
