import React from 'react';
import { MetricCard } from "@/components/MetricCard";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { 
  User, 
  Calendar, 
  DollarSign, 
  MessageCircle,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Award,
  Bell,
  CreditCard,
  BookOpen,
  Clock
} from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";

export const ParentDashboard: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome, {user?.firstName}!
          </h1>
          <p className="text-gray-600 mt-1">
            Stay connected with your child's educational journey.
          </p>
        </div>
        <div className="flex gap-2">
          <Button>
            <MessageCircle className="h-4 w-4 mr-2" />
            Message Teacher
          </Button>
          <Button variant="outline">
            <CreditCard className="h-4 w-4 mr-2" />
            Pay Fees
          </Button>
        </div>
      </div>

      {/* Child Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Child Overview
          </CardTitle>
          <CardDescription>
            Quick overview of your child's information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
              AJ
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold">Alice Johnson</h3>
              <p className="text-gray-600">Grade 10-A • Roll No: 2024001</p>
              <p className="text-sm text-gray-500">Class Teacher: Ms. Sarah Davis</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Overall Grade</p>
              <p className="text-2xl font-bold text-green-600">A-</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Attendance Rate"
          value="96.5%"
          change="Excellent attendance!"
          changeType="increase"
          icon={<CheckCircle className="h-6 w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Academic Performance"
          value="A-"
          change="↑ 0.3 points this term"
          changeType="increase"
          icon={<Award className="h-6 w-6" />}
          iconBg="bg-yellow-500"
        />
        <MetricCard
          title="Fee Status"
          value="$2,450"
          change="Due: Dec 31, 2024"
          changeType="neutral"
          icon={<DollarSign className="h-6 w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Pending Tasks"
          value="3"
          change="2 assignments due soon"
          changeType="decrease"
          icon={<AlertCircle className="h-6 w-6" />}
          iconBg="bg-red-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Academic Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Academic Progress
            </CardTitle>
            <CardDescription>
              Your child's performance across subjects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Mathematics</span>
                  <span className="text-sm text-gray-600">92% (A-)</span>
                </div>
                <Progress value={92} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Physics</span>
                  <span className="text-sm text-gray-600">88% (B+)</span>
                </div>
                <Progress value={88} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">English</span>
                  <span className="text-sm text-gray-600">95% (A)</span>
                </div>
                <Progress value={95} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Chemistry</span>
                  <span className="text-sm text-gray-600">85% (B)</span>
                </div>
                <Progress value={85} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">History</span>
                  <span className="text-sm text-gray-600">90% (A-)</span>
                </div>
                <Progress value={90} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Recent Activities
            </CardTitle>
            <CardDescription>
              Latest updates about your child
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Assignment submitted</p>
                  <p className="text-xs text-gray-500">Physics Lab Report - Grade: A</p>
                </div>
                <span className="text-xs text-gray-400">2 hours ago</span>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Attendance marked</p>
                  <p className="text-xs text-gray-500">Present in all classes today</p>
                </div>
                <span className="text-xs text-gray-400">8 hours ago</span>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">New assignment</p>
                  <p className="text-xs text-gray-500">Math Problem Set 12 - Due: Dec 18</p>
                </div>
                <span className="text-xs text-gray-400">1 day ago</span>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Exam scheduled</p>
                  <p className="text-xs text-gray-500">Physics exam on Dec 15</p>
                </div>
                <span className="text-xs text-gray-400">2 days ago</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Events */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Upcoming Events
            </CardTitle>
            <CardDescription>
              Important dates and events for your child
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center text-white">
                  <BookOpen className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-red-800">Physics Exam</p>
                  <p className="text-sm text-red-600">Dec 15 • 2:00 PM • Lab 3</p>
                </div>
                <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">3 days</span>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white">
                  <MessageCircle className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-blue-800">Parent-Teacher Meeting</p>
                  <p className="text-sm text-blue-600">Dec 22 • 4:00 PM • Room 201</p>
                </div>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">10 days</span>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white">
                  <Award className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-green-800">Science Fair</p>
                  <p className="text-sm text-green-600">Dec 20 • All Day • Main Hall</p>
                </div>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">8 days</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fee Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Fee Information
            </CardTitle>
            <CardDescription>
              Payment status and upcoming dues
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                <div>
                  <p className="font-medium text-green-800">Tuition Fee</p>
                  <p className="text-sm text-green-600">Term 1 - Paid</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-green-800">$1,500</p>
                  <CheckCircle className="h-4 w-4 text-green-500 ml-auto" />
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div>
                  <p className="font-medium text-yellow-800">Term 2 Fee</p>
                  <p className="text-sm text-yellow-600">Due: Dec 31, 2024</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-yellow-800">$1,500</p>
                  <Button size="sm" className="mt-1">
                    Pay Now
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Activity Fee</p>
                  <p className="text-sm text-gray-600">Due: Jan 15, 2025</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">$450</p>
                  <Clock className="h-4 w-4 text-gray-400 ml-auto" />
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium text-blue-800">Total Outstanding</span>
                <span className="text-lg font-bold text-blue-800">$1,950</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Communication */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Recent Communications
          </CardTitle>
          <CardDescription>
            Messages from teachers and school administration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                SD
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <p className="font-medium">Ms. Sarah Davis</p>
                  <span className="text-xs text-gray-500">Class Teacher</span>
                </div>
                <p className="text-sm text-gray-700">Alice has shown excellent improvement in Mathematics this term. Keep up the great work!</p>
                <p className="text-xs text-gray-500 mt-1">2 days ago</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
                AD
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <p className="font-medium">Admin Office</p>
                  <span className="text-xs text-gray-500">School Administration</span>
                </div>
                <p className="text-sm text-gray-700">Reminder: Parent-Teacher meeting scheduled for Dec 22. Please confirm your attendance.</p>
                <p className="text-xs text-gray-500 mt-1">5 days ago</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
