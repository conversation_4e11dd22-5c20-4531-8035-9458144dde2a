import React, { useState, useEffect } from 'react';
import { MetricCard } from "@/components/MetricCard";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  Calendar,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Award,
  Target,
  Bell,
  Users,
  GraduationCap
} from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";
import { assignmentsService, examinationsService, resultsService, timetableService, studentsService, classesService, getCurrentSchoolId } from "@/services/supabaseService";
import { toast } from "@/components/ui/use-toast";

export const StudentDashboard: React.FC = () => {
  const { user, profile } = useAuth();
  const [studentData, setStudentData] = useState<any>(null);
  const [classData, setClassData] = useState<any>(null);
  const [assignments, setAssignments] = useState<any[]>([]);
  const [examinations, setExaminations] = useState<any[]>([]);
  const [results, setResults] = useState<any[]>([]);
  const [timetable, setTimetable] = useState<any[]>([]);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load student's class-based data
  useEffect(() => {
    initializeStudentData();
  }, [user]);

  const initializeStudentData = async () => {
    if (!user?.email) return;

    try {
      // Get school ID from URL
      const pathParts = window.location.pathname.split('/');
      const schoolSlug = pathParts[1];
      const currentSchoolId = await getCurrentSchoolId(schoolSlug);

      if (currentSchoolId) {
        setSchoolId(currentSchoolId);
        await loadStudentClassData(currentSchoolId, user.email);
      }
    } catch (error) {
      console.error('Error initializing student data:', error);
      toast({
        title: "Error",
        description: "Failed to load student data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadStudentClassData = async (schoolId: string, studentEmail: string) => {
    try {
      // Find student record by email
      const allStudents = await studentsService.getAll(schoolId);
      const student = allStudents.find((s: any) => s.parent_email === studentEmail);

      if (student) {
        setStudentData(student);

        // Load class information
        if (student.class_id) {
          const allClasses = await classesService.getAll(schoolId);
          const studentClass = allClasses.find((c: any) => c.id === student.class_id);
          setClassData(studentClass);

          // Load class-specific data
          await Promise.all([
            loadClassAssignments(schoolId, student.class_id),
            loadClassExaminations(schoolId, student.class_id),
            loadStudentResults(schoolId, student.id),
            loadClassTimetable(schoolId, student.class_id)
          ]);
        }
      } else {
        console.log('Student record not found for email:', studentEmail);
      }
    } catch (error) {
      console.error('Error loading student class data:', error);
    }
  };

  const loadClassAssignments = async (schoolId: string, classId: string) => {
    try {
      console.log('📚 Loading assignments for class:', classId);
      const allAssignments = await assignmentsService.getAll(schoolId);
      const classAssignments = allAssignments.filter((a: any) => a.class_id === classId);
      console.log('📚 Found', classAssignments.length, 'assignments for this class');
      setAssignments(classAssignments);
    } catch (error) {
      console.error('Error loading class assignments:', error);
    }
  };

  const loadClassExaminations = async (schoolId: string, classId: string) => {
    try {
      const allExaminations = await examinationsService.getAll(schoolId);
      const classExaminations = allExaminations.filter((e: any) => e.class_id === classId);
      setExaminations(classExaminations);
    } catch (error) {
      console.error('Error loading class examinations:', error);
    }
  };

  const loadStudentResults = async (schoolId: string, studentId: string) => {
    try {
      const allResults = await resultsService.getAll(schoolId);
      const studentResults = allResults.filter((r: any) => r.student_id === studentId);
      setResults(studentResults);
    } catch (error) {
      console.error('Error loading student results:', error);
    }
  };

  const loadClassTimetable = async (schoolId: string, classId: string) => {
    try {
      const classTimetable = await timetableService.getByClass(schoolId, classId);
      setTimetable(classTimetable);
    } catch (error) {
      console.error('Error loading class timetable:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading your dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {studentData?.first_name || user?.firstName}!
          </h1>
          <p className="text-gray-600 mt-1">
            {classData ? (
              <>
                You're in <strong>{classData.name} {classData.section}</strong> - Let's continue your learning journey today.
              </>
            ) : (
              "Let's continue your learning journey today."
            )}
          </p>
          {studentData && (
            <div className="flex gap-2 mt-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                <Users className="h-3 w-3 mr-1" />
                Student ID: {studentData.student_id}
              </Badge>
              {classData && (
                <Badge variant="outline" className="bg-green-50 text-green-700">
                  <GraduationCap className="h-3 w-3 mr-1" />
                  Class: {classData.name} {classData.section}
                </Badge>
              )}
            </div>
          )}
        </div>
        <div className="flex gap-2">
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            View Assignments ({assignments.length})
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="My Class"
          value={classData ? `${classData.name} ${classData.section}` : "Not Assigned"}
          change={classData ? `${classData.max_students || 'N/A'} total students` : "Contact admin"}
          changeType="neutral"
          icon={<Users className="h-6 w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Class Assignments"
          value={assignments.length.toString()}
          change={assignments.filter(a => new Date(a.due_date) > new Date()).length + " pending"}
          changeType="neutral"
          icon={<FileText className="h-6 w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="My Results"
          value={results.length.toString()}
          change={results.length > 0 ? "Latest results available" : "No results yet"}
          changeType="neutral"
          icon={<Award className="h-6 w-6" />}
          iconBg="bg-yellow-500"
        />
        <MetricCard
          title="Class Exams"
          value={examinations.length.toString()}
          change={examinations.filter(e => new Date(e.start_date) > new Date()).length + " upcoming"}
          changeType="neutral"
          icon={<Calendar className="h-6 w-6" />}
          iconBg="bg-purple-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Schedule */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Today's Classes
            </CardTitle>
            <CardDescription>
              Your schedule for today
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white font-bold">
                  9:00
                </div>
                <div className="flex-1">
                  <p className="font-medium">Mathematics</p>
                  <p className="text-sm text-gray-600">Room 201 • Mr. Johnson</p>
                </div>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Completed</span>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold">
                  10:30
                </div>
                <div className="flex-1">
                  <p className="font-medium">Physics</p>
                  <p className="text-sm text-gray-600">Lab 3 • Ms. Davis</p>
                </div>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Current</span>
              </div>
              
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 font-bold">
                  2:00
                </div>
                <div className="flex-1">
                  <p className="font-medium">English Literature</p>
                  <p className="text-sm text-gray-600">Room 105 • Mrs. Smith</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Academic Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Academic Progress
            </CardTitle>
            <CardDescription>
              Your performance across subjects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Mathematics</span>
                  <span className="text-sm text-gray-600">92%</span>
                </div>
                <Progress value={92} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Physics</span>
                  <span className="text-sm text-gray-600">88%</span>
                </div>
                <Progress value={88} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">English</span>
                  <span className="text-sm text-gray-600">95%</span>
                </div>
                <Progress value={95} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Chemistry</span>
                  <span className="text-sm text-gray-600">85%</span>
                </div>
                <Progress value={85} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">History</span>
                  <span className="text-sm text-gray-600">90%</span>
                </div>
                <Progress value={90} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pending Assignments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Pending Assignments
            </CardTitle>
            <CardDescription>
              Assignments due soon
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                <div>
                  <p className="font-medium text-red-800">Physics Lab Report</p>
                  <p className="text-sm text-red-600">Due: Tomorrow</p>
                </div>
                <Button size="sm" variant="outline">
                  Submit
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div>
                  <p className="font-medium text-yellow-800">Math Problem Set 12</p>
                  <p className="text-sm text-yellow-600">Due: Dec 18</p>
                </div>
                <Button size="sm" variant="outline">
                  Work On It
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">English Essay</p>
                  <p className="text-sm text-gray-600">Due: Dec 22</p>
                </div>
                <Button size="sm" variant="outline">
                  Start
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Events */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Upcoming Events
            </CardTitle>
            <CardDescription>
              Important dates and events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white">
                  <Calendar className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <p className="font-medium">Physics Exam</p>
                  <p className="text-sm text-gray-600">Dec 15 • 2:00 PM • Lab 3</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white">
                  <Award className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <p className="font-medium">Science Fair</p>
                  <p className="text-sm text-gray-600">Dec 20 • All Day • Main Hall</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center text-white">
                  <Target className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <p className="font-medium">Parent-Teacher Meeting</p>
                  <p className="text-sm text-gray-600">Dec 22 • 4:00 PM • Room 201</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Achievements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Recent Achievements
          </CardTitle>
          <CardDescription>
            Your recent accomplishments and milestones
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                <Award className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium text-yellow-800">Perfect Attendance</p>
                <p className="text-xs text-yellow-600">November 2024</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium text-blue-800">Grade Improvement</p>
                <p className="text-xs text-blue-600">Math: B+ to A-</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium text-green-800">Assignment Streak</p>
                <p className="text-xs text-green-600">15 on-time submissions</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
