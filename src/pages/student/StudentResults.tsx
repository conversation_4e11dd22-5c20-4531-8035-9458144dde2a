import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Tren<PERSON>Up, Book<PERSON>pen, FileText, Loader2, Award } from "lucide-react";
import { useMultiTenantAuth } from "@/contexts/MultiTenantAuthContext";
import { useSimpleAuth } from "@/contexts/SimpleAuthContext";
import { supabase } from '@/integrations/supabase/client';
import { getCurrentSchoolId } from "@/services/supabaseService";

export const StudentResults = () => {
  // Try to use MultiTenantAuth first, fallback to SimpleAuth
  let user: any, profile: any;
  try {
    const multiTenantAuth = useMultiTenantAuth();
    user = multiTenantAuth.user;
    profile = multiTenantAuth.profile;
  } catch {
    // Fallback to SimpleAuth
    const simpleAuth = useSimpleAuth();
    user = simpleAuth.user;
    // Create a profile-like object from SimpleAuth user
    profile = user ? {
      id: user.id,
      email: user.email,
      role: user.user_metadata?.role || 'student'
    } : null;
  }
  const { toast } = useToast();
  const [results, setResults] = useState<any[]>([]);
  const [studentClass, setStudentClass] = useState<any>(null);
  const [studentInfo, setStudentInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      const schoolSlug = window.location.pathname.split('/')[1];
      const id = await getCurrentSchoolId(schoolSlug);
      if (!id) {
        toast({
          title: "Error",
          description: "School not found",
          variant: "destructive",
        });
        return;
      }
      setSchoolId(id);
      await loadStudentResults(id);
    } catch (error) {
      console.error('Error initializing:', error);
      toast({
        title: "Error",
        description: "Failed to initialize page",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadStudentResults = async (schoolId: string) => {
    try {
      console.log('📊 Loading student results for profile:', profile?.id);

      // Find student by profile_id
      const { data: students } = await supabase
        .from('students')
        .select('*, class:classes(id, name, section)')
        .eq('school_id', schoolId)
        .eq('profile_id', profile?.id)
        .eq('is_active', true);

      if (!students || students.length === 0) {
        console.log('📊 No student found for profile:', profile?.id);
        return;
      }

      const student = students[0];
      setStudentClass(student.class);
      setStudentInfo(student);
      console.log('📊 Student found:', student.first_name, student.last_name, 'Class:', student.class?.name);

      // Load results for this student
      const { data: resultsData } = await supabase
        .from('results')
        .select(`
          *,
          exam_subject:exam_subjects(
            id,
            examination:examinations(id, name, exam_type),
            subject:subjects(id, name, code),
            max_marks
          )
        `)
        .eq('school_id', schoolId)
        .eq('student_id', student.id)
        .order('created_at', { ascending: false });

      setResults(resultsData || []);
      console.log('📊 Loaded', resultsData?.length || 0, 'results');
    } catch (error) {
      console.error('Error loading student results:', error);
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade?.toUpperCase()) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800';
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800';
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800';
      case 'D+':
      case 'D':
        return 'bg-orange-100 text-orange-800';
      case 'F':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const calculatePercentage = (obtained: number, total: number) => {
    return total > 0 ? ((obtained / total) * 100).toFixed(1) : '0.0';
  };

  const calculateOverallStats = () => {
    if (results.length === 0) return { average: 0, totalSubjects: 0, passedSubjects: 0 };

    const validResults = results.filter(r => r.marks_obtained !== null && r.exam_subject?.max_marks);
    const totalPercentages = validResults.reduce((sum, result) => {
      const percentage = (result.marks_obtained / result.exam_subject.max_marks) * 100;
      return sum + percentage;
    }, 0);

    const average = validResults.length > 0 ? totalPercentages / validResults.length : 0;
    const passedSubjects = validResults.filter(r => {
      const percentage = (r.marks_obtained / r.exam_subject.max_marks) * 100;
      return percentage >= 40; // Assuming 40% is passing
    }).length;

    return {
      average: average.toFixed(1),
      totalSubjects: validResults.length,
      passedSubjects
    };
  };

  const stats = calculateOverallStats();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading your results...</span>
      </div>
    );
  }

  if (!studentClass) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Class Assigned</h3>
            <p className="text-gray-600">You haven't been assigned to a class yet. Please contact your school administrator.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">My Results</h1>
        <p className="text-gray-600">Academic performance for {studentInfo?.first_name} {studentInfo?.last_name}</p>
      </div>

      {/* Student Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Student Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium">Student ID</p>
              <p className="text-lg">{studentInfo?.student_id}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Class</p>
              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                {studentClass.name} {studentClass.section}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium">Overall Average</p>
              <p className="text-lg font-bold text-green-600">{stats.average}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Overall Average</p>
                <p className="text-2xl font-bold text-blue-600">{stats.average}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Subjects Passed</p>
                <p className="text-2xl font-bold text-green-600">{stats.passedSubjects}/{stats.totalSubjects}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Total Results</p>
                <p className="text-2xl font-bold text-purple-600">{results.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Results List */}
      <div className="space-y-4">
        {results.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No Results</h3>
              <p className="text-gray-600">No examination results are available yet.</p>
            </CardContent>
          </Card>
        ) : (
          results.map((result) => {
            const maxMarks = result.exam_subject?.max_marks || 100;
            const percentage = calculatePercentage(result.marks_obtained, maxMarks);
            
            return (
              <Card key={result.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        {result.exam_subject?.subject?.name || 'Unknown Subject'}
                      </CardTitle>
                      <CardDescription>
                        {result.exam_subject?.examination?.name} - {result.exam_subject?.examination?.exam_type}
                      </CardDescription>
                    </div>
                    <Badge className={getGradeColor(result.grade)}>
                      {result.grade || 'N/A'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-medium">Marks Obtained</p>
                      <p className="text-2xl font-bold">{result.marks_obtained}/{maxMarks}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Percentage</p>
                      <p className="text-2xl font-bold text-blue-600">{percentage}%</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Grade</p>
                      <Badge className={getGradeColor(result.grade)} variant="outline">
                        {result.grade || 'N/A'}
                      </Badge>
                    </div>
                  </div>
                  
                  {result.remarks && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm font-medium mb-1">Remarks</p>
                      <p className="text-sm text-gray-600">{result.remarks}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
};
