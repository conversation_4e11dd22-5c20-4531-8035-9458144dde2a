import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Clock, User, MapPin, BookOpen, Loader2 } from "lucide-react";
import { useMultiTenantAuth } from "@/contexts/MultiTenantAuthContext";
import { supabase } from '@/integrations/supabase/client';
import { getCurrentSchoolId } from "@/services/supabaseService";

const days = [
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' }
];

export const StudentTimetable = () => {
  const { user, profile } = useMultiTenantAuth();
  const { toast } = useToast();
  const [timetable, setTimetable] = useState<any[]>([]);
  const [studentClass, setStudentClass] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [schoolId, setSchoolId] = useState<string>('');

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      const schoolSlug = window.location.pathname.split('/')[1];
      const id = await getCurrentSchoolId(schoolSlug);
      if (!id) {
        toast({
          title: "Error",
          description: "School not found",
          variant: "destructive",
        });
        return;
      }
      setSchoolId(id);
      await loadStudentTimetable(id);
    } catch (error) {
      console.error('Error initializing:', error);
      toast({
        title: "Error",
        description: "Failed to initialize page",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadStudentTimetable = async (schoolId: string) => {
    try {
      console.log('📚 Loading student timetable for:', user?.email);
      
      // Find student by email
      const { data: students } = await supabase
        .from('students')
        .select('*, class:classes(id, name, section)')
        .eq('school_id', schoolId)
        .eq('parent_email', user?.email)
        .eq('is_active', true);

      if (!students || students.length === 0) {
        console.log('📚 No student found for email:', user?.email);
        return;
      }

      const student = students[0];
      setStudentClass(student.class);
      console.log('📚 Student found:', student.first_name, student.last_name, 'Class:', student.class?.name);

      if (student.class_id) {
        // Load timetable for student's class
        const { data: timetableData } = await supabase
          .from('timetable')
          .select(`
            *,
            subject:subjects(id, name, code),
            teacher:teachers(id, first_name, last_name)
          `)
          .eq('school_id', schoolId)
          .eq('class_id', student.class_id)
          .eq('is_active', true)
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true });

        setTimetable(timetableData || []);
        console.log('📚 Loaded', timetableData?.length || 0, 'timetable entries');
      }
    } catch (error) {
      console.error('Error loading student timetable:', error);
    }
  };

  const groupedTimetable = timetable.reduce((acc, item) => {
    const day = days.find(d => d.value === item.day_of_week);
    const dayName = day ? day.label : 'Unknown';
    if (!acc[dayName]) acc[dayName] = [];
    acc[dayName].push(item);
    return acc;
  }, {} as Record<string, any[]>);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading your timetable...</span>
      </div>
    );
  }

  if (!studentClass) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Class Assigned</h3>
            <p className="text-gray-600">You haven't been assigned to a class yet. Please contact your school administrator.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">My Timetable</h1>
        <p className="text-gray-600">Your class schedule for {studentClass.name} {studentClass.section}</p>
      </div>

      {/* Class Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Class Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            {studentClass.name} {studentClass.section}
          </Badge>
        </CardContent>
      </Card>

      {/* Weekly Timetable */}
      <div className="grid gap-6">
        {days.map(day => (
          <Card key={day.value}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {day.label}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {groupedTimetable[day.label] && groupedTimetable[day.label].length > 0 ? (
                <div className="space-y-3">
                  {groupedTimetable[day.label].map((period) => (
                    <div key={period.id} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex justify-between items-start">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">{period.subject?.name || 'Unknown Subject'}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <User className="h-4 w-4" />
                            <span>
                              {period.teacher 
                                ? `${period.teacher.first_name} ${period.teacher.last_name}`
                                : 'Unknown Teacher'
                              }
                            </span>
                          </div>
                          {period.room_number && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <MapPin className="h-4 w-4" />
                              <span>{period.room_number}</span>
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{period.start_time} - {period.end_time}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-gray-500 py-4">No classes scheduled</p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
