import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Calendar, Clock, BookOpen, FileText, Loader2, AlertCircle } from "lucide-react";
import { useMultiTenantAuth } from "@/contexts/MultiTenantAuthContext";
import { supabase } from '@/integrations/supabase/client';
import { getCurrentSchoolId } from "@/services/supabaseService";

export const StudentAssignments = () => {
  const { user, profile } = useMultiTenantAuth();
  const { toast } = useToast();
  const [assignments, setAssignments] = useState<any[]>([]);
  const [studentClass, setStudentClass] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [schoolId, setSchoolId] = useState<string>('');

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      const schoolSlug = window.location.pathname.split('/')[1];
      const id = await getCurrentSchoolId(schoolSlug);
      if (!id) {
        toast({
          title: "Error",
          description: "School not found",
          variant: "destructive",
        });
        return;
      }
      setSchoolId(id);
      await loadStudentAssignments(id);
    } catch (error) {
      console.error('Error initializing:', error);
      toast({
        title: "Error",
        description: "Failed to initialize page",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadStudentAssignments = async (schoolId: string) => {
    try {
      console.log('📚 Loading student assignments for:', user?.email);
      
      // Find student by email
      const { data: students } = await supabase
        .from('students')
        .select('*, class:classes(id, name, section)')
        .eq('school_id', schoolId)
        .eq('parent_email', user?.email)
        .eq('is_active', true);

      if (!students || students.length === 0) {
        console.log('📚 No student found for email:', user?.email);
        return;
      }

      const student = students[0];
      setStudentClass(student.class);
      console.log('📚 Student found:', student.first_name, student.last_name, 'Class:', student.class?.name);

      if (student.class_id) {
        // Load assignments for student's class
        const { data: assignmentsData } = await supabase
          .from('assignments')
          .select(`
            *,
            subject:subjects(id, name, code)
          `)
          .eq('school_id', schoolId)
          .eq('class_id', student.class_id)
          .eq('is_active', true)
          .order('due_date', { ascending: true });

        setAssignments(assignmentsData || []);
        console.log('📚 Loaded', assignmentsData?.length || 0, 'assignments');
      }
    } catch (error) {
      console.error('Error loading student assignments:', error);
    }
  };

  const getAssignmentStatus = (dueDate: string) => {
    const now = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return { status: 'overdue', color: 'bg-red-100 text-red-800', icon: AlertCircle };
    } else if (diffDays === 0) {
      return { status: 'due today', color: 'bg-orange-100 text-orange-800', icon: Clock };
    } else if (diffDays <= 3) {
      return { status: 'due soon', color: 'bg-yellow-100 text-yellow-800', icon: Clock };
    } else {
      return { status: 'upcoming', color: 'bg-green-100 text-green-800', icon: Calendar };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading your assignments...</span>
      </div>
    );
  }

  if (!studentClass) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Class Assigned</h3>
            <p className="text-gray-600">You haven't been assigned to a class yet. Please contact your school administrator.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">My Assignments</h1>
        <p className="text-gray-600">Assignments for {studentClass.name} {studentClass.section}</p>
      </div>

      {/* Class Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Class Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            {studentClass.name} {studentClass.section}
          </Badge>
        </CardContent>
      </Card>

      {/* Assignments List */}
      <div className="space-y-4">
        {assignments.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No Assignments</h3>
              <p className="text-gray-600">No assignments have been posted yet.</p>
            </CardContent>
          </Card>
        ) : (
          assignments.map((assignment) => {
            const { status, color, icon: StatusIcon } = getAssignmentStatus(assignment.due_date);
            
            return (
              <Card key={assignment.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        {assignment.title}
                      </CardTitle>
                      {assignment.subject && (
                        <CardDescription className="mt-1">
                          Subject: {assignment.subject.name}
                        </CardDescription>
                      )}
                    </div>
                    <Badge className={color}>
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  {assignment.description && (
                    <div className="mb-4">
                      <h4 className="font-medium mb-2">Description</h4>
                      <p className="text-gray-600 text-sm">{assignment.description}</p>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Assigned Date</p>
                        <p className="text-sm text-gray-600">{formatDate(assignment.assigned_date)}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Due Date</p>
                        <p className="text-sm text-gray-600">{formatDateTime(assignment.due_date)}</p>
                      </div>
                    </div>
                    {assignment.total_marks && (
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium">Total Marks</p>
                          <p className="text-sm text-gray-600">{assignment.total_marks}</p>
                        </div>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Type</p>
                        <p className="text-sm text-gray-600">{assignment.assignment_type || 'Assignment'}</p>
                      </div>
                    </div>
                  </div>

                  {assignment.instructions && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-1">Instructions</h4>
                      <p className="text-blue-800 text-sm">{assignment.instructions}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
};
