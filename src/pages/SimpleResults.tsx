import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { Plus, Loader2 } from "lucide-react";
import { supabase } from '@/integrations/supabase/client';
import { getCurrentSchoolId } from "@/services/supabaseService";

export const SimpleResults = () => {
  const { toast } = useToast();
  const [results, setResults] = useState<any[]>([]);
  const [students, setStudents] = useState<any[]>([]);
  const [examinations, setExaminations] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [schoolId, setSchoolId] = useState<string>('');

  // Form state
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedStudent, setSelectedStudent] = useState('');
  const [selectedExam, setSelectedExam] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [marks, setMarks] = useState('');
  const [totalMarks, setTotalMarks] = useState('100');

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      const schoolSlug = window.location.pathname.split('/')[1];
      const id = await getCurrentSchoolId(schoolSlug);
      if (!id) {
        toast({
          title: "Error",
          description: "School not found",
          variant: "destructive",
        });
        return;
      }
      setSchoolId(id);
      await loadData(id);
    } catch (error) {
      console.error('Error initializing:', error);
      toast({
        title: "Error",
        description: "Failed to initialize page",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadData = async (schoolId: string) => {
    try {
      // Load all data in parallel
      const [resultsData, studentsData, examsData, subjectsData, classesData] = await Promise.all([
        supabase.from('results').select('*').eq('school_id', schoolId),
        supabase.from('students').select('*').eq('school_id', schoolId).eq('is_active', true),
        supabase.from('examinations').select('*').eq('school_id', schoolId).eq('is_active', true),
        supabase.from('subjects').select('*').eq('school_id', schoolId).eq('is_active', true),
        supabase.from('classes').select('*').eq('school_id', schoolId).eq('is_active', true)
      ]);

      setResults(resultsData.data || []);
      setStudents(studentsData.data || []);
      setExaminations(examsData.data || []);
      setSubjects(subjectsData.data || []);
      setClasses(classesData.data || []);
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const filteredStudents = selectedClass 
    ? students.filter(student => student.class_id === selectedClass)
    : [];

  const handleSubmit = async () => {
    if (!selectedStudent || !selectedExam || !selectedSubject || !marks) {
      toast({
        title: "Error",
        description: "Please fill all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Simple direct insert to results table
      const { data, error } = await supabase
        .from('results')
        .insert({
          school_id: schoolId,
          student_id: selectedStudent,
          exam_subject_id: `${selectedExam}-${selectedSubject}`, // Simple combination
          marks_obtained: parseFloat(marks),
          grade: calculateGrade(parseFloat(marks), parseFloat(totalMarks))
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Result added successfully",
      });

      // Reset form and reload
      setSelectedClass('');
      setSelectedStudent('');
      setSelectedExam('');
      setSelectedSubject('');
      setMarks('');
      setIsAddDialogOpen(false);
      await loadData(schoolId);
    } catch (error: any) {
      console.error('Error adding result:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add result",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateGrade = (obtained: number, total: number) => {
    const percentage = (obtained / total) * 100;
    if (percentage >= 90) return 'A+';
    if (percentage >= 80) return 'A';
    if (percentage >= 70) return 'B+';
    if (percentage >= 60) return 'B';
    if (percentage >= 50) return 'C';
    if (percentage >= 40) return 'D';
    return 'F';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Results</h1>
          <p className="text-gray-600">Manage student examination results</p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Result
        </Button>
      </div>

      {/* Results List */}
      <Card>
        <CardHeader>
          <CardTitle>Results ({results.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {results.length === 0 ? (
            <p className="text-center text-gray-500 py-8">No results found</p>
          ) : (
            <div className="space-y-4">
              {results.map((result) => (
                <div key={result.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Student: {result.student_id}</p>
                      <p className="text-sm text-gray-600">Marks: {result.marks_obtained}</p>
                      <p className="text-sm text-gray-600">Grade: {result.grade}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Result Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Result</DialogTitle>
            <DialogDescription>Enter the examination result details</DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Class Selection */}
            <div>
              <Label>Class *</Label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger>
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name} {cls.section}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Student Selection */}
            <div>
              <Label>Student *</Label>
              <Select value={selectedStudent} onValueChange={setSelectedStudent} disabled={!selectedClass}>
                <SelectTrigger>
                  <SelectValue placeholder={selectedClass ? "Select student" : "Select class first"} />
                </SelectTrigger>
                <SelectContent>
                  {filteredStudents.map(student => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.first_name} {student.last_name} ({student.student_id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Examination */}
            <div>
              <Label>Examination *</Label>
              <Select value={selectedExam} onValueChange={setSelectedExam}>
                <SelectTrigger>
                  <SelectValue placeholder="Select examination" />
                </SelectTrigger>
                <SelectContent>
                  {examinations.map(exam => (
                    <SelectItem key={exam.id} value={exam.id}>
                      {exam.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Subject */}
            <div>
              <Label>Subject *</Label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map(subject => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Marks */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Marks Obtained *</Label>
                <Input
                  type="number"
                  value={marks}
                  onChange={(e) => setMarks(e.target.value)}
                  placeholder="0"
                />
              </div>
              <div>
                <Label>Total Marks</Label>
                <Input
                  type="number"
                  value={totalMarks}
                  onChange={(e) => setTotalMarks(e.target.value)}
                  placeholder="100"
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2 pt-4">
              <Button onClick={handleSubmit} disabled={isSubmitting} className="flex-1">
                {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Add Result
              </Button>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
