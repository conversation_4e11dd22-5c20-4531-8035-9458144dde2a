import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import {
  Search,
  Download,
  Plus,
  Edit,
  GraduationCap,
  TrendingUp,
  Award,
  BookOpen,
  BarChart3,
  Grid,
  List,
  MoreVertical,
  User,
  Calendar
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { MetricCard } from "@/components/MetricCard";
import { useIsMobile } from "@/hooks/use-mobile";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useDataAccess } from "@/hooks/useDataAccess.tsx";

// Mock data
const mockGradesData = [
  { 
    id: '1', 
    studentId: 'S001', 
    studentName: 'Alice Johnson', 
    subject: 'Mathematics', 
    type: 'exam', 
    title: 'Midterm Exam', 
    score: 92, 
    maxScore: 100, 
    grade: 'A-',
    date: '2024-12-01',
    feedback: 'Excellent work on algebra problems'
  },
  { 
    id: '2', 
    studentId: 'S001', 
    studentName: 'Alice Johnson', 
    subject: 'Physics', 
    type: 'assignment', 
    title: 'Lab Report 3', 
    score: 88, 
    maxScore: 100, 
    grade: 'B+',
    date: '2024-11-28',
    feedback: 'Good analysis, needs more detail in conclusion'
  },
  { 
    id: '3', 
    studentId: 'S002', 
    studentName: 'Bob Smith', 
    subject: 'Mathematics', 
    type: 'quiz', 
    title: 'Chapter 5 Quiz', 
    score: 78, 
    maxScore: 100, 
    grade: 'B-',
    date: '2024-11-25',
    feedback: 'Review quadratic equations'
  },
];

const subjects = ['Mathematics', 'Physics', 'Chemistry', 'English', 'History'];
const gradeTypes = ['exam', 'assignment', 'quiz', 'project'];

export const Grades: React.FC = () => {
  const { user } = useAuth();
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [gradesData, setGradesData] = useState(mockGradesData);
  const [isAddGradeOpen, setIsAddGradeOpen] = useState(false);
  const isMobile = useIsMobile();
  const [viewMode, setViewMode] = useState<'table' | 'cards'>(isMobile ? 'cards' : 'table');
  const { canCreate, canEdit, canDelete, canExport, canManage, getViewMode } = useDataAccess('grades');

  // Auto-switch to cards view on mobile
  useEffect(() => {
    if (isMobile && viewMode === 'table') {
      setViewMode('cards');
    }
  }, [isMobile, viewMode]);

  const getGradeBadge = (grade: string) => {
    const gradeColors: { [key: string]: string } = {
      'A+': 'bg-green-100 text-green-800',
      'A': 'bg-green-100 text-green-800',
      'A-': 'bg-green-100 text-green-800',
      'B+': 'bg-blue-100 text-blue-800',
      'B': 'bg-blue-100 text-blue-800',
      'B-': 'bg-blue-100 text-blue-800',
      'C+': 'bg-yellow-100 text-yellow-800',
      'C': 'bg-yellow-100 text-yellow-800',
      'C-': 'bg-yellow-100 text-yellow-800',
      'D': 'bg-orange-100 text-orange-800',
      'F': 'bg-red-100 text-red-800',
    };
    
    return (
      <Badge className={`${gradeColors[grade] || 'bg-gray-100 text-gray-800'} hover:${gradeColors[grade] || 'bg-gray-100'}`}>
        {grade}
      </Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'exam':
        return <GraduationCap className="h-4 w-4" />;
      case 'assignment':
        return <BookOpen className="h-4 w-4" />;
      case 'quiz':
        return <Award className="h-4 w-4" />;
      case 'project':
        return <BarChart3 className="h-4 w-4" />;
      default:
        return <BookOpen className="h-4 w-4" />;
    }
  };

  const filteredData = gradesData.filter(grade => {
    const matchesSearch = (grade.studentName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (grade.studentId?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (grade.title?.toLowerCase() || '').includes(searchTerm.toLowerCase());
    const matchesSubject = selectedSubject === 'all' || grade.subject === selectedSubject;
    const matchesType = selectedType === 'all' || grade.type === selectedType;

    return matchesSearch && matchesSubject && matchesType;
  });

  // Calculate statistics
  const totalGrades = gradesData.length;
  const averageScore = gradesData.reduce((sum, grade) => sum + (grade.score / grade.maxScore * 100), 0) / totalGrades;
  const highestScore = Math.max(...gradesData.map(grade => grade.score / grade.maxScore * 100));
  const gradeDistribution = {
    A: gradesData.filter(g => g.grade.startsWith('A')).length,
    B: gradesData.filter(g => g.grade.startsWith('B')).length,
    C: gradesData.filter(g => g.grade.startsWith('C')).length,
    D: gradesData.filter(g => g.grade === 'D').length,
    F: gradesData.filter(g => g.grade === 'F').length,
  };

  // Mobile Card Component
  const GradeCard: React.FC<{ grade: any }> = ({ grade }) => (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
              <User className="h-6 w-6 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-base text-gray-900 truncate">
                {grade.studentName}
              </h3>
              <p className="text-sm text-gray-600">ID: {grade.studentId}</p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {user?.role === 'teacher' && (
                <DropdownMenuItem>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Grade
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Subject:</span>
            <span className="text-sm font-medium">{grade.subject}</span>
          </div>

          <div className="flex items-center gap-2">
            {getTypeIcon(grade.type)}
            <div className="flex-1">
              <p className="font-medium text-sm">{grade.title}</p>
              <p className="text-xs text-gray-500 capitalize">{grade.type}</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Score:</span>
            <div className="text-right">
              <p className="font-medium text-sm">{grade.score}/{grade.maxScore}</p>
              <p className="text-xs text-gray-500">{((grade.score / grade.maxScore) * 100).toFixed(1)}%</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Grade:</span>
            {getGradeBadge(grade.grade)}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Date:</span>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3 text-gray-400" />
              <span className="text-sm">{new Date(grade.date).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {grade.feedback && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-xs text-gray-600 mb-1">Feedback:</p>
            <p className="text-sm text-gray-800">{grade.feedback}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Grade Management</h1>
          <p className="text-gray-600 mt-1 text-xs sm:text-sm md:text-base">
            Track and manage student grades and assessments
          </p>
        </div>
        <div className="flex items-center gap-2">
          {/* View Toggle - Only show on mobile */}
          <div className="md:hidden flex bg-gray-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
              className="h-8 px-3"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('cards')}
              className="h-8 px-3"
            >
              <Grid className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
          {canCreate() && (
            <Dialog open={isAddGradeOpen} onOpenChange={setIsAddGradeOpen}>
              <DialogTrigger asChild>
                <Button className="text-xs md:text-sm px-3 py-2 h-9">
                  <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                  <span className="hidden sm:inline">Add Grade</span>
                  <span className="sm:hidden">Add</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md sm:max-w-[95vw] sm:mx-4 max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Grade</DialogTitle>
                  <DialogDescription>
                    Enter grade information for a student
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Student</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select student" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="S001">Alice Johnson (S001)</SelectItem>
                        <SelectItem value="S002">Bob Smith (S002)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Subject</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map(subject => (
                          <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Assessment Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        {gradeTypes.map(type => (
                          <SelectItem key={type} value={type} className="capitalize">{type}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Title</Label>
                    <Input placeholder="e.g., Midterm Exam" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Score</Label>
                      <Input type="number" placeholder="85" />
                    </div>
                    <div className="space-y-2">
                      <Label>Max Score</Label>
                      <Input type="number" placeholder="100" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Feedback</Label>
                    <Textarea placeholder="Optional feedback for the student..." />
                  </div>
                  <div className="flex gap-2">
                    <Button className="flex-1">Save Grade</Button>
                    <Button variant="outline" onClick={() => setIsAddGradeOpen(false)}>Cancel</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
          <Button variant="outline" className="text-xs md:text-sm px-3 py-2 h-9">
            <Download className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Export</span>
            <span className="sm:hidden">Export</span>
          </Button>
          </div>
        </div>
      </div>

      {/* Grade Statistics */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 md:gap-6">
        <MetricCard
          title="Total Grades"
          value={totalGrades.toString()}
          icon={<BookOpen className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Class Average"
          value={`${averageScore.toFixed(1)}%`}
          icon={<TrendingUp className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Highest Score"
          value={`${highestScore.toFixed(1)}%`}
          icon={<Award className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-yellow-500"
        />
        <MetricCard
          title="A Grades"
          value={gradeDistribution.A.toString()}
          icon={<GraduationCap className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-emerald-500"
        />
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base sm:text-lg">Grade Filters</CardTitle>
          <CardDescription className="text-xs sm:text-sm">
            Filter grades by subject, type, or search for specific students
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Subject</Label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="All subjects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {subjects.map(subject => (
                    <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Assessment Type</Label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {gradeTypes.map(type => (
                    <SelectItem key={type} value={type} className="capitalize">{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search students or assessments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button className="w-full" variant="outline">
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Grades List */}
      {isMobile && viewMode === 'cards' ? (
        // Mobile Card View
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Student Grades ({filteredData.length})</h2>
          </div>
          {filteredData.length === 0 ? (
            <Card>
              <CardContent className="py-8">
                <div className="text-center text-gray-500">
                  No grades found matching your search criteria.
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {filteredData.map((grade) => (
                <GradeCard key={grade.id} grade={grade} />
              ))}
            </div>
          )}
        </div>
      ) : (
        // Table View (Desktop and Mobile Table Mode)
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Student Grades</CardTitle>
            <CardDescription className="text-xs sm:text-sm">
              View and manage all student grades and assessments
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table className="min-w-[800px]">
                <TableHeader>
                  <TableRow className="bg-muted/30">
                    <TableHead className="text-xs sm:text-sm w-40">Student</TableHead>
                    <TableHead className="text-xs sm:text-sm w-24 hidden md:table-cell">Subject</TableHead>
                    <TableHead className="text-xs sm:text-sm w-48">Assessment</TableHead>
                    <TableHead className="text-xs sm:text-sm w-24">Score</TableHead>
                    <TableHead className="text-xs sm:text-sm w-20">Grade</TableHead>
                    <TableHead className="text-xs sm:text-sm w-24 hidden lg:table-cell">Date</TableHead>
                    <TableHead className="text-xs sm:text-sm w-20">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((grade) => (
                    <TableRow key={grade.id} className="hover:bg-gray-50">
                      <TableCell className="py-2">
                        <div>
                          <p className="font-medium text-xs sm:text-sm">{grade.studentName}</p>
                          <p className="text-xs text-gray-500">{grade.studentId}</p>
                        </div>
                      </TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 hidden md:table-cell">{grade.subject}</TableCell>
                      <TableCell className="py-2">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(grade.type)}
                          <div className="min-w-0">
                            <p className="font-medium text-xs sm:text-sm truncate">{grade.title}</p>
                            <p className="text-xs text-gray-500 capitalize">{grade.type}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-2">
                        <div>
                          <p className="font-medium text-xs sm:text-sm">{grade.score}/{grade.maxScore}</p>
                          <p className="text-xs text-gray-500">{((grade.score / grade.maxScore) * 100).toFixed(1)}%</p>
                        </div>
                      </TableCell>
                      <TableCell className="py-2">{getGradeBadge(grade.grade)}</TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 hidden lg:table-cell">{new Date(grade.date).toLocaleDateString()}</TableCell>
                      <TableCell className="py-2">
                        {user?.role === 'teacher' && (
                          <Button size="sm" variant="outline" className="h-7 w-7 p-0" title="Edit Grade">
                            <Edit className="h-3 w-3" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
