import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { 
  Search, 
  Plus,
  Edit,
  Eye,
  Calendar as CalendarIcon,
  Clock,
  Users,
  BookOpen,
  AlertCircle,
  CheckCircle,
  FileText,
  MapPin
} from "lucide-react";
import { format } from "date-fns";
import { useAuth } from "@/contexts/AuthContext";
import { MetricCard } from "@/components/MetricCard";
import { ExaminationForm } from "@/components/forms/ExaminationForm";
import { examinationsService, classesService, subjectsService, getCurrentSchoolId } from "@/services/supabaseService";
import { toast } from "@/components/ui/use-toast";

// Mock data
const mockExams = [
  {
    id: '1',
    title: 'Mathematics Midterm Exam',
    subject: 'Mathematics',
    class: '10-A',
    date: '2024-12-15',
    startTime: '09:00',
    endTime: '11:00',
    duration: 120,
    maxMarks: 100,
    room: 'Room 201',
    supervisor: 'Mr. Johnson',
    status: 'scheduled',
    instructions: 'Bring calculator and drawing instruments',
    studentsCount: 30
  },
  {
    id: '2',
    title: 'Physics Final Exam',
    subject: 'Physics',
    class: '11-B',
    date: '2024-12-18',
    startTime: '14:00',
    endTime: '16:30',
    duration: 150,
    maxMarks: 100,
    room: 'Lab 3',
    supervisor: 'Ms. Davis',
    status: 'scheduled',
    instructions: 'Formula sheet will be provided',
    studentsCount: 25
  },
  {
    id: '3',
    title: 'English Literature Quiz',
    subject: 'English',
    class: '12-A',
    date: '2024-12-12',
    startTime: '10:00',
    endTime: '11:00',
    duration: 60,
    maxMarks: 50,
    room: 'Room 105',
    supervisor: 'Mrs. Smith',
    status: 'completed',
    instructions: 'No external materials allowed',
    studentsCount: 22
  },
  {
    id: '4',
    title: 'Chemistry Practical Exam',
    subject: 'Chemistry',
    class: '11-A',
    date: '2024-12-20',
    startTime: '09:00',
    endTime: '12:00',
    duration: 180,
    maxMarks: 75,
    room: 'Chemistry Lab',
    supervisor: 'Dr. Brown',
    status: 'draft',
    instructions: 'Lab coats and safety goggles required',
    studentsCount: 28
  }
];

const rooms = ['Room 201', 'Room 202', 'Lab 3', 'Chemistry Lab', 'Physics Lab', 'Room 105'];

export const Examinations: React.FC = () => {
  const { user } = useAuth();
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const [selectedClass, setSelectedClass] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [exams, setExams] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedExam, setSelectedExam] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [examDate, setExamDate] = useState<Date>();

  // Load data on component mount
  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      // Get school ID from URL
      const pathParts = window.location.pathname.split('/');
      const schoolSlug = pathParts[1];
      const currentSchoolId = await getCurrentSchoolId(schoolSlug);

      if (currentSchoolId) {
        setSchoolId(currentSchoolId);
        await Promise.all([
          loadExaminations(currentSchoolId),
          loadSubjects(currentSchoolId),
          loadClasses(currentSchoolId)
        ]);
      }
    } catch (error) {
      console.error('Error initializing data:', error);
      toast({
        title: "Error",
        description: "Failed to initialize data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadExaminations = async (schoolId: string) => {
    try {
      const data = await examinationsService.getAll(schoolId);
      setExams(data || []);

      // If no data, show a friendly message
      if (!data || data.length === 0) {
        console.log('No examinations data found for this school');
      }
    } catch (error) {
      console.error('Error loading examinations:', error);
      setExams([]); // Set empty array on error
      toast({
        title: "Info",
        description: "Examinations feature is not yet available for this school",
        variant: "default",
      });
    }
  };

  const loadSubjects = async (schoolId: string) => {
    try {
      const data = await subjectsService.getAll(schoolId);
      setSubjects(data || []);
    } catch (error) {
      console.error('Error loading subjects:', error);
    }
  };

  const loadClasses = async (schoolId: string) => {
    try {
      const data = await classesService.getAll(schoolId);
      setClasses(data || []);
    } catch (error) {
      console.error('Error loading classes:', error);
    }
  };

  const handleCreateExamination = async (data: any) => {
    if (!schoolId) return;

    setIsSubmitting(true);
    try {
      const examData = {
        name: data.name,
        description: data.description,
        subjectId: data.subjectId,
        classId: data.classId,
        examType: data.examType,
        startDate: data.startDate,
        endDate: data.endDate,
        startTime: data.startTime,
        endTime: data.endTime,
        totalMarks: data.totalMarks,
        passingMarks: data.passingMarks,
        room: data.room,
        instructions: data.instructions
      };

      console.log('📋 Submitting examination data:', examData);

      await examinationsService.create(examData, schoolId);
      await loadExaminations(schoolId);
      setIsCreateOpen(false);

      toast({
        title: "Success",
        description: "Examination scheduled successfully",
      });
    } catch (error) {
      console.error('Error creating examination:', error);
      toast({
        title: "Error",
        description: "Failed to schedule examination",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditExamination = async (data: any) => {
    if (!selectedExam || !schoolId) return;

    setIsSubmitting(true);
    try {
      const examData = {
        name: data.name,
        description: data.description,
        subjectId: data.subjectId,
        classId: data.classId,
        examType: data.examType,
        startDate: data.startDate,
        endDate: data.endDate,
        startTime: data.startTime,
        endTime: data.endTime,
        totalMarks: data.totalMarks,
        passingMarks: data.passingMarks,
        room: data.room,
        instructions: data.instructions
      };

      await examinationsService.update(selectedExam.id, examData);
      await loadExaminations(schoolId);
      setIsEditOpen(false);
      setSelectedExam(null);

      toast({
        title: "Success",
        description: "Examination updated successfully",
      });
    } catch (error) {
      console.error('Error updating examination:', error);
      toast({
        title: "Error",
        description: "Failed to update examination",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (exam: any) => {
    setSelectedExam(exam);
    setIsEditOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Scheduled</Badge>;
      case 'ongoing':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Ongoing</Badge>;
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Completed</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Cancelled</Badge>;
      case 'draft':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Draft</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const filteredExams = exams.filter(exam => {
    const subjectName = subjects.find(s => s.id === exam.subject_id)?.name || '';
    const className = classes.find(c => c.id === exam.class_id)?.name || '';

    const matchesSearch = (exam.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (subjectName.toLowerCase() || '').includes(searchTerm.toLowerCase());
    const matchesSubject = selectedSubject === 'all' || exam.subject_id === selectedSubject;
    const matchesClass = selectedClass === 'all' || exam.class_id === selectedClass;
    const matchesStatus = selectedStatus === 'all' || getExamStatus(exam) === selectedStatus;

    return matchesSearch && matchesSubject && matchesClass && matchesStatus;
  });

  // Calculate statistics
  const totalExams = exams.length;
  const scheduledExams = exams.filter(e => e.status === 'scheduled').length;
  const completedExams = exams.filter(e => e.status === 'completed').length;
  const upcomingExams = exams.filter(e => e.status === 'scheduled' && new Date(e.date) > new Date()).length;

  const isUpcoming = (date: string) => {
    return new Date(date) > new Date();
  };

  const isToday = (date: string) => {
    const today = new Date();
    const examDate = new Date(date);
    return today.toDateString() === examDate.toDateString();
  };

  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(`1970-01-01T${startTime}:00`);
    const end = new Date(`1970-01-01T${endTime}:00`);
    const diffMs = end.getTime() - start.getTime();
    return Math.round(diffMs / (1000 * 60)); // Convert to minutes
  };

  const getExamStatus = (exam: any) => {
    if (!exam.start_date) return 'scheduled';

    const today = new Date();
    const examDate = new Date(exam.start_date);

    if (examDate < today) {
      return 'completed';
    } else if (isToday(exam.start_date)) {
      return 'ongoing';
    } else {
      return 'scheduled';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Examination Management</h1>
          <p className="text-gray-600 mt-1">
            Schedule, manage, and track examinations and assessments
          </p>
        </div>
        <div className="flex gap-2">
          {(user?.role === 'admin' || user?.role === 'teacher') && (
            <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Exam
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Schedule New Examination</DialogTitle>
                  <DialogDescription>
                    Create a new examination schedule
                  </DialogDescription>
                </DialogHeader>
                <ExaminationForm
                  subjects={subjects}
                  classes={classes}
                  onSubmit={handleCreateExamination}
                  onCancel={() => setIsCreateOpen(false)}
                  isLoading={isSubmitting}
                />
              </DialogContent>
            </Dialog>
          )}

          {/* Edit Examination Dialog */}
          <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Edit Examination</DialogTitle>
                <DialogDescription>
                  Update examination details
                </DialogDescription>
              </DialogHeader>
              <ExaminationForm
                examination={selectedExam}
                subjects={subjects}
                classes={classes}
                onSubmit={handleEditExamination}
                onCancel={() => {
                  setIsEditOpen(false);
                  setSelectedExam(null);
                }}
                isLoading={isSubmitting}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Exam Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Exams"
          value={totalExams.toString()}
          icon={<BookOpen className="h-6 w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Scheduled"
          value={scheduledExams.toString()}
          icon={<CalendarIcon className="h-6 w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Completed"
          value={completedExams.toString()}
          icon={<CheckCircle className="h-6 w-6" />}
          iconBg="bg-gray-500"
        />
        <MetricCard
          title="Upcoming"
          value={upcomingExams.toString()}
          icon={<Clock className="h-6 w-6" />}
          iconBg="bg-yellow-500"
        />
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Examination Filters</CardTitle>
          <CardDescription>
            Filter examinations by subject, class, status, or search
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>Subject</Label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="All subjects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {subjects.map(subject => (
                    <SelectItem key={subject.id} value={subject.id}>{subject.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Class</Label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger>
                  <SelectValue placeholder="All classes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>{cls.name} {cls.section}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="ongoing">Ongoing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search exams..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button className="w-full" variant="outline">
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Examinations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Scheduled Examinations</CardTitle>
          <CardDescription>
            View and manage all examinations and assessments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Examination</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>Class</TableHead>
                <TableHead>Date & Time</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Room</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredExams.map((exam) => (
                <TableRow key={exam.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{exam.name}</p>
                      <p className="text-sm text-gray-500">
                        {exam.exam_type && `Type: ${exam.exam_type}`}
                        {exam.total_marks && ` • Max Marks: ${exam.total_marks}`}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      {exam.subject_id ? (
                        <span className="text-sm">
                          {subjects.find(s => s.id === exam.subject_id)?.name || exam.subject_id}
                        </span>
                      ) : (
                        <span className="text-gray-400">Not specified</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      {exam.class_id ? (
                        <span className="text-sm">
                          {classes.find(c => c.id === exam.class_id)?.name || exam.class_id}
                          {classes.find(c => c.id === exam.class_id)?.section &&
                            ` ${classes.find(c => c.id === exam.class_id)?.section}`}
                        </span>
                      ) : (
                        <span className="text-gray-400">Not specified</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {exam.start_date && isToday(exam.start_date) && (
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                      )}
                      <div>
                        <p className={`font-medium ${exam.start_date && isToday(exam.start_date) ? 'text-orange-600' : ''}`}>
                          {exam.start_date ? new Date(exam.start_date).toLocaleDateString() : 'Not set'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {exam.start_time || 'No time'} - {exam.end_time || 'No time'}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>
                        {exam.start_time && exam.end_time ?
                          `${calculateDuration(exam.start_time, exam.end_time)} min` :
                          'Not set'
                        }
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span>{exam.room || 'Not specified'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(getExamStatus(exam))}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedExam(exam);
                          setIsViewOpen(true);
                        }}
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                      {(user?.role === 'admin' || user?.role === 'teacher') && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedExam(exam);
                            setIsEditOpen(true);
                          }}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Examination Dialog */}
      {selectedExam && (
        <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Examination Details</DialogTitle>
              <DialogDescription>
                View examination information
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Name</Label>
                  <p className="text-sm">{selectedExam.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Type</Label>
                  <p className="text-sm">{selectedExam.exam_type || 'Not specified'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Subject</Label>
                  <p className="text-sm">
                    {subjects.find(s => s.id === selectedExam.subject_id)?.name || 'Not specified'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Class</Label>
                  <p className="text-sm">
                    {classes.find(c => c.id === selectedExam.class_id)?.name || 'Not specified'}
                    {classes.find(c => c.id === selectedExam.class_id)?.section &&
                      ` ${classes.find(c => c.id === selectedExam.class_id)?.section}`}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Start Date</Label>
                  <p className="text-sm">
                    {selectedExam.start_date ? new Date(selectedExam.start_date).toLocaleDateString() : 'Not set'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">End Date</Label>
                  <p className="text-sm">
                    {selectedExam.end_date ? new Date(selectedExam.end_date).toLocaleDateString() : 'Not set'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Start Time</Label>
                  <p className="text-sm">{selectedExam.start_time || 'Not set'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">End Time</Label>
                  <p className="text-sm">{selectedExam.end_time || 'Not set'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Total Marks</Label>
                  <p className="text-sm">{selectedExam.total_marks || 'Not set'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Passing Marks</Label>
                  <p className="text-sm">{selectedExam.passing_marks || 'Not set'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Room</Label>
                  <p className="text-sm">{selectedExam.room || 'Not specified'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Status</Label>
                  <p className="text-sm">{getExamStatus(selectedExam)}</p>
                </div>
              </div>
              {selectedExam.description && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">Description</Label>
                  <p className="text-sm">{selectedExam.description}</p>
                </div>
              )}
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setIsViewOpen(false)}>
                Close
              </Button>
              {(user?.role === 'admin' || user?.role === 'teacher') && (
                <Button onClick={() => {
                  setIsViewOpen(false);
                  setIsEditOpen(true);
                }}>
                  Edit Examination
                </Button>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
