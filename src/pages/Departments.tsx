import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { DepartmentForm } from '@/components/forms/DepartmentForm';
import { departmentsService, getCurrentSchoolId } from '@/services/supabaseService';
import { useDataAccess } from '@/hooks/useDataAccess.tsx';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  Building, 
  Users, 
  BookOpen, 
  GraduationCap,
  Filter,
  MoreVertical
} from 'lucide-react';
import { Department } from '@/types';



export const Departments: React.FC = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { user, hasRole } = useAuth();
  const { toast } = useToast();
  const [departments, setDepartments] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const { canCreate, canEdit, canDelete } = useDataAccess('departments');

  // Check permissions - Only admin can access
  const canManageDepartments = hasRole('admin');

  // Load school ID and departments
  useEffect(() => {
    const initializeData = async () => {
      if (!schoolSlug) return;

      try {
        const currentSchoolId = await getCurrentSchoolId(schoolSlug);
        if (currentSchoolId) {
          setSchoolId(currentSchoolId);
        }
      } catch (error) {
        console.error('Error getting school ID:', error);
        toast({
          title: "Error",
          description: "Failed to load school information",
          variant: "destructive",
        });
      }
    };

    initializeData();
  }, [schoolSlug]);

  useEffect(() => {
    if (schoolId) {
      loadDepartments();
    }
  }, [schoolId]);

  const loadDepartments = async () => {
    if (!schoolId) return;

    setIsLoading(true);
    try {
      const departmentsData = await departmentsService.getAll(schoolId);

      // Transform the data to match the expected Department interface
      const transformedDepartments = departmentsData.map((dept: any) => ({
        id: dept.id,
        name: dept.name,
        code: dept.name.substring(0, 3).toUpperCase(), // Generate code from name
        description: dept.description || '',
        headOfDepartment: dept.head_teacher_id ? 'Assigned' : 'Not Assigned',
        totalSubjects: 0, // Will be calculated when we have subject assignments
        totalTeachers: 0, // Will be calculated when we have teacher assignments
        isActive: dept.is_active,
        createdAt: new Date(dept.created_at),
        updatedAt: new Date(dept.updated_at)
      }));

      setDepartments(transformedDepartments);
    } catch (error) {
      console.error('Failed to load departments:', error);
      toast({
        title: "Error",
        description: "Failed to load departments. Please try again.",
        variant: "destructive",
      });
      setDepartments([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter departments based on search
  const filteredDepartments = departments.filter(department => {
    const matchesSearch = (department.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (department.code?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (department.description?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (department.headOfDepartment?.toLowerCase() || '').includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  const handleAddDepartment = async (departmentData: any) => {
    if (!schoolId) return;

    try {
      const newDepartmentData = {
        name: departmentData.name,
        description: departmentData.description,
        head_teacher_id: departmentData.headOfDepartmentId || null,
      };

      await departmentsService.create(newDepartmentData, schoolId);

      // Reload departments to get the latest data
      await loadDepartments();
      setIsAddDialogOpen(false);
      toast({
        title: "Department Added Successfully!",
        description: `${departmentData.name} has been created.`,
      });
    } catch (error: any) {
      console.error('Failed to create department:', error);

      let errorMessage = "Failed to create department. Please try again.";

      if (error?.code === '23505') {
        errorMessage = "A department with this name or code already exists.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleEditDepartment = async (departmentData: any) => {
    if (!selectedDepartment) return;

    try {
      const updateData = {
        name: departmentData.name,
        description: departmentData.description,
        head_teacher_id: departmentData.headOfDepartmentId || null,
      };

      await departmentsService.update(selectedDepartment.id, updateData);

      // Reload departments to get the latest data
      await loadDepartments();
      setIsEditDialogOpen(false);
      setSelectedDepartment(null);
      toast({
        title: "Department Updated Successfully!",
        description: `${departmentData.name} has been updated.`,
      });
    } catch (error: any) {
      console.error('Failed to update department:', error);

      let errorMessage = "Failed to update department. Please try again.";

      if (error?.code === '23505') {
        errorMessage = "A department with this name or code already exists.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleDeleteDepartment = async (department: any) => {
    try {
      await departmentsService.delete(department.id);

      // Reload departments to get the latest data
      await loadDepartments();
      toast({
        title: "Department Deleted",
        description: `${department.name} has been successfully deleted.`,
        variant: "destructive",
      });
    } catch (error: any) {
      console.error('Failed to delete department:', error);
      toast({
        title: "Error",
        description: "Failed to delete department. Please try again.",
        variant: "destructive",
      });
    }
  };

  const openViewDialog = (department: Department) => {
    setSelectedDepartment(department);
    setIsViewDialogOpen(true);
  };

  const openEditDialog = (department: Department) => {
    setSelectedDepartment(department);
    setIsEditDialogOpen(true);
  };

  const getDepartmentStats = () => {
    const totalDepartments = departments.length;
    const activeDepartments = departments.filter(d => d.isActive).length;
    const totalSubjects = departments.reduce((sum, d) => sum + d.totalSubjects, 0);
    const totalTeachers = departments.reduce((sum, d) => sum + d.totalTeachers, 0);
    
    return { totalDepartments, activeDepartments, totalSubjects, totalTeachers };
  };

  const stats = getDepartmentStats();

  if (!canManageDepartments) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Building className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
          <p className="text-gray-600">Only administrators can manage departments.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Department Management</h1>
          <p className="text-gray-600 mt-1 text-sm md:text-base">Manage academic departments and organizational structure</p>
        </div>
        {canCreate() && (
          <Button onClick={() => setIsAddDialogOpen(true)} className="bg-primary hover:bg-primary/90 text-xs md:text-sm">
            <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Add New Department</span>
            <span className="sm:hidden">Add Department</span>
          </Button>
        )}
      </div>

      {/* Department Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
        <Card className="stat-card">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center gap-3">
              <div className="metric-icon bg-blue-500">
                <Building className="h-5 w-5 md:h-6 md:w-6" />
              </div>
              <div>
                <p className="text-xs md:text-sm font-medium text-gray-600">Total Departments</p>
                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.totalDepartments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="stat-card">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center gap-3">
              <div className="metric-icon bg-green-500">
                <GraduationCap className="h-5 w-5 md:h-6 md:w-6" />
              </div>
              <div>
                <p className="text-xs md:text-sm font-medium text-gray-600">Active Departments</p>
                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.activeDepartments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="stat-card">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center gap-3">
              <div className="metric-icon bg-purple-500">
                <BookOpen className="h-5 w-5 md:h-6 md:w-6" />
              </div>
              <div>
                <p className="text-xs md:text-sm font-medium text-gray-600">Total Subjects</p>
                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.totalSubjects}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="stat-card">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center gap-3">
              <div className="metric-icon bg-orange-500">
                <Users className="h-5 w-5 md:h-6 md:w-6" />
              </div>
              <div>
                <p className="text-xs md:text-sm font-medium text-gray-600">Total Teachers</p>
                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.totalTeachers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardContent className="p-4 md:p-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Quick Actions</h3>
              <div className="flex flex-wrap gap-2">
                <Button 
                  onClick={() => setIsAddDialogOpen(true)} 
                  size="sm" 
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Department
                </Button>
                <Button 
                  onClick={() => setSearchTerm('')} 
                  variant="outline" 
                  size="sm"
                >
                  Clear Search
                </Button>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              Showing {filteredDepartments.length} of {departments.length} departments
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search */}
      <Card>
        <CardContent className="p-4 md:p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search departments by name, code, description, or head of department..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Departments Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDepartments.map((department) => (
          <Card key={department.id} className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Building className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{department.name}</CardTitle>
                    <Badge variant="outline" className="text-xs mt-1">
                      {department.code}
                    </Badge>
                  </div>
                </div>
                <Badge
                  variant="secondary"
                  className={`text-xs ${department.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                >
                  {department.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <CardDescription className="text-sm">
                {department.description}
              </CardDescription>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Head of Department:</span>
                  <span className="font-medium">{department.headOfDepartment}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Subjects:</span>
                  <span className="font-medium">{department.totalSubjects}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Teachers:</span>
                  <span className="font-medium">{department.totalTeachers}</span>
                </div>
              </div>

              <div className="flex gap-2 pt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => openViewDialog(department)}
                  className="flex-1 text-gray-600 hover:text-gray-800"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                {canEdit() && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => openEditDialog(department)}
                    className="flex-1 text-blue-600 hover:text-blue-800"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                )}
                {canDelete() && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-1 text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Department</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete "{department.name}"? This action cannot be undone and may affect associated subjects and teachers.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleDeleteDepartment(department)}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredDepartments.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Building className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No departments found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first department.'}
            </p>
            {!searchTerm && (
              <Button onClick={() => setIsAddDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Department
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Add Department Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Department</DialogTitle>
            <DialogDescription>
              Create a new academic department for your institution.
            </DialogDescription>
          </DialogHeader>
          <DepartmentForm onSubmit={handleAddDepartment} onCancel={() => setIsAddDialogOpen(false)} />
        </DialogContent>
      </Dialog>

      {/* Edit Department Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Department</DialogTitle>
            <DialogDescription>
              Update the department information.
            </DialogDescription>
          </DialogHeader>
          {selectedDepartment && (
            <DepartmentForm
              department={selectedDepartment}
              onSubmit={handleEditDepartment}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedDepartment(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Department Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Department Details</DialogTitle>
          </DialogHeader>
          {selectedDepartment && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Department Name</label>
                  <p className="text-gray-900">{selectedDepartment.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Department Code</label>
                  <p className="text-gray-900">{selectedDepartment.code}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Head of Department</label>
                  <p className="text-gray-900">{selectedDepartment.headOfDepartment}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <Badge
                    variant="secondary"
                    className={selectedDepartment.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                  >
                    {selectedDepartment.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Total Subjects</label>
                  <p className="text-gray-900">{selectedDepartment.totalSubjects}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Total Teachers</label>
                  <p className="text-gray-900">{selectedDepartment.totalTeachers}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Description</label>
                <p className="text-gray-900">{selectedDepartment.description}</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Created</label>
                  <p className="text-gray-900">{selectedDepartment.createdAt.toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Last Updated</label>
                  <p className="text-gray-900">{selectedDepartment.updatedAt.toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
