import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import { Search, Plus, Edit, Trash2, Eye, Loader2, Lock, Grid, List, Phone, Mail, MapPin, MoreVertical } from "lucide-react";

import { MetricCard } from "@/components/MetricCard";
import { User, Users, Calendar } from "lucide-react";
import { StudentForm } from "@/components/forms/StudentForm";
import { StudentCard } from "@/components/StudentCard";
import { studentsService, classesService, getCurrentSchoolId } from "@/services/supabaseService";
import { Student } from "@/types";
import { useDataAccess } from "@/hooks/useDataAccess.tsx";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/contexts/NewAuthContext";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

export const Students: React.FC = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const [searchTerm, setSearchTerm] = useState("");
  const [students, setStudents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<any | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [schoolId, setSchoolId] = useState<string | null>(null);
  const isMobile = useIsMobile();
  const [viewMode, setViewMode] = useState<'table' | 'cards'>(isMobile ? 'cards' : 'table');
  // Auth and permissions setup
  const { user, profile } = useAuth();
  const dataAccess = useDataAccess('students');

  const isAuthenticated = !!user;
  const canAccessData = () => true;
  const getAccessibleStudentIds = () => [];
  const canCreate = () => profile?.role === 'admin';
  const canEdit = () => profile?.role === 'admin';
  const canDelete = () => profile?.role === 'admin';
  const canExport = () => profile?.role === 'admin';
  const canManage = () => profile?.role === 'admin';
  const getViewMode = () => 'table';
  const canView = () => profile?.role === 'admin';

  // Check if user can access students page
  if (!canView()) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
          <p className="text-gray-600">You don't have permission to view students.</p>
          <p className="text-sm text-gray-500 mt-2">Current role: {profile?.role}</p>
        </div>
      </div>
    );
  }

  // Load school ID and students on component mount
  useEffect(() => {
    const initializeData = async () => {
      if (!schoolSlug) {
        console.log('🏫 Students: No schoolSlug provided');
        return;
      }

      try {
        console.log('🏫 Students: Initializing data for school:', schoolSlug);
        const currentSchoolId = await getCurrentSchoolId(schoolSlug);
        console.log('🏫 Students: Got school ID:', currentSchoolId);

        if (currentSchoolId) {
          setSchoolId(currentSchoolId);
          console.log('🏫 Students: Loading students...');
          await loadStudents(currentSchoolId);
          console.log('🏫 Students: Data loading completed');
        } else {
          console.error('🏫 Students: No school ID found for slug:', schoolSlug);
        }
      } catch (error) {
        console.error('🏫 Students: Error getting school ID:', error);
        toast({
          title: "Error",
          description: "Failed to load school information",
          variant: "destructive",
        });
      }
    };

    initializeData();
  }, [schoolSlug]);

  // Auto-switch to cards view on mobile
  useEffect(() => {
    if (isMobile && viewMode === 'table') {
      setViewMode('cards');
    }
  }, [isMobile, viewMode]);

  const loadStudents = async (currentSchoolId?: string) => {
    const schoolIdToUse = currentSchoolId || schoolId;
    if (!schoolIdToUse) return;

    setIsLoading(true);
    try {
      console.log('🏫 Students: Loading students for school ID:', schoolIdToUse);
      const studentsData = await studentsService.getAll(schoolIdToUse);
      console.log('Raw students data from database:', studentsData);
      console.log('Number of students found:', studentsData?.length || 0);

      // Load registered students from localStorage (students who registered through signup)
      const registeredStudents = JSON.parse(localStorage.getItem('students') || '[]');
      const schoolRegisteredStudents = registeredStudents.filter((s: any) => s.school_id === schoolIdToUse);
      console.log('Registered students found for this school:', schoolRegisteredStudents.length);

      // Load updated existing students data from localStorage
      const existingStudentsData = JSON.parse(localStorage.getItem('existingStudentsData') || '[]');
      const schoolExistingStudentsData = existingStudentsData.filter((s: any) => s.school_id === schoolIdToUse);

      // Add existing students who registered before (like <EMAIL>, <EMAIL>)
      const existingStudents = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      // Create records for existing students, but check if they have updated data first
      const existingStudentRecords = existingStudents.map((email, index) => {
        const existingId = `existing-${index}`;

        // Check if this student has updated data in localStorage
        const updatedData = schoolExistingStudentsData.find((s: any) => s.id === existingId);

        if (updatedData) {
          // Use the updated data
          return updatedData;
        } else {
          // Use default data
          return {
            id: existingId,
            student_id: email.split('@')[0],
            first_name: email.split('@')[0].charAt(0).toUpperCase() + email.split('@')[0].slice(1),
            last_name: 'Student',
            parent_email: email,
            phone: 'Not provided',
            address: 'Not provided',
            date_of_birth: '2000-01-01',
            gender: 'Not specified',
            class_id: null,
            admission_date: new Date().toISOString().split('T')[0],
            parent_contact: 'Not provided',
            emergency_contact: 'Not provided',
            medical_info: 'None',
            is_active: true,
            school_id: schoolIdToUse,
            created_at: new Date().toISOString()
          };
        }
      });

      // Combine database students, registered students, and existing students
      const allStudents = [...(studentsData || []), ...schoolRegisteredStudents, ...existingStudentRecords];
      console.log('Total students (database + registered + existing):', allStudents.length);

      if (!allStudents || !Array.isArray(allStudents) || allStudents.length === 0) {
        console.log('No students data received (real or mock)');
        setStudents([]);
        return;
      }

      // Load classes to properly display class names
      let classesData: any[] = [];
      try {
        classesData = await classesService.getAll(schoolId);
        console.log('Classes loaded for display:', classesData.length);
        console.log('Classes data:', classesData);

        // If no classes loaded, try to use mock classes
        if (!classesData || classesData.length === 0) {
          console.log('No classes found, using mock classes for display');
          classesData = [
            { id: '2788c108-989c-40e1-a2c3-c3e200cee0f8', name: 'Form One', section: 'A' },
            { id: 'mock-class-2', name: 'Form Two', section: 'B' },
            { id: 'mock-class-3', name: 'Form Three', section: 'A' }
          ];
        }
      } catch (error) {
        console.error('Error loading classes for display:', error);
        // Use mock classes as fallback
        classesData = [
          { id: '2788c108-989c-40e1-a2c3-c3e200cee0f8', name: 'Form One', section: 'A' },
          { id: 'mock-class-2', name: 'Form Two', section: 'B' },
          { id: 'mock-class-3', name: 'Form Three', section: 'A' }
        ];
      }

      // Transform the data to match the expected format
      const transformedStudents = allStudents.map((student: any) => {
        // Look up class information if class_id exists
        let classDisplay = 'Not Assigned';
        let sectionDisplay = '';

        if (student.class_id) {
          console.log('Looking up class for student:', student.student_id, 'class_id:', student.class_id);
          console.log('Available classes:', classesData.map(c => ({ id: c.id, name: c.name })));

          const studentClass = classesData.find((cls: any) => cls.id === student.class_id);
          console.log('Found class:', studentClass);

          if (studentClass) {
            classDisplay = `${studentClass.name} ${studentClass.section || ''}`.trim();
            sectionDisplay = studentClass.section || '';
            console.log('Class display set to:', classDisplay);
          } else {
            // If class not found in database, try to show a more user-friendly name
            console.log('Class not found in database, using fallback');
            classDisplay = `Unknown Class (${student.class_id.substring(0, 8)}...)`;
          }
        }

        return {
          id: student.id,
          studentId: student.student_id,
          firstName: student.first_name || '',
          lastName: student.last_name || '',
          email: student.parent_email || '', // This is parent's email for login
          phone: student.phone || '',
          class: classDisplay,
          classId: student.class_id || '',
          section: sectionDisplay,
          isActive: student.is_active,
          // All students have login credentials through their email
          address: student.address || '',
          dateOfBirth: student.date_of_birth || '',
          gender: student.gender || '',
          admissionDate: student.admission_date || '',
          // All students are treated equally - no special flags needed
          emergencyContact: student.emergency_contact || '',
          parentContact: student.parent_contact || '',
          medicalInfo: student.medical_info || '',
          parentId: student.parent_id || '',
          // Login credentials: Parent's email + Student ID as password
          loginEmail: student.parent_email || '',
          loginPassword: student.student_id, // Student ID is used as password
        };
      });

      // Apply role-based filtering
      let filteredStudents = transformedStudents;

      if (profile?.role === 'teacher') {
        // Teachers can only see students in their assigned classes
        const accessibleStudentIds = getAccessibleStudentIds();
        if (accessibleStudentIds.length > 0) {
          filteredStudents = transformedStudents.filter(student =>
            accessibleStudentIds.includes(student.id)
          );
        }
      } else if (profile?.role === 'parent') {
        // Parents can only see their own children
        const childrenIds = getAccessibleStudentIds();
        filteredStudents = transformedStudents.filter(student =>
          childrenIds.includes(student.id)
        );
      } else if (profile?.role === 'student') {
        // Students can only see themselves
        filteredStudents = transformedStudents.filter(student =>
          student.id === user.id
        );
      }

      setStudents(filteredStudents);
    } catch (error) {
      console.error('Failed to load students:', error);
      toast({
        title: "Database Connection Error",
        description: "Unable to connect to the database. Please check your internet connection and try again.",
        variant: "destructive",
      });
      setStudents([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddStudent = async (data: any) => {
    if (!schoolId) return;

    setIsSubmitting(true);
    try {
      const studentData = {
        studentId: data.studentId || `STU${Date.now()}`,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email, // Parent's email
        phone: data.phone,
        address: data.address,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
        classId: data.classId,
        admissionDate: data.admissionDate || new Date().toISOString().split('T')[0],
        parentContact: data.parentContact,
        emergencyContact: data.emergencyContact,
        medicalInfo: data.medicalInfo,
      };

      const newStudent = await studentsService.create(studentData, schoolId);

      // Reload students to get the latest data with relationships
      await loadStudents();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Student added successfully",
      });
    } catch (error: any) {
      console.error('Failed to add student:', error);

      let errorMessage = "Failed to add student. Please try again.";

      if (error?.code === '23505') {
        if (error.message?.includes('student_id')) {
          errorMessage = "A student with this Student ID already exists. Please use a different Student ID.";
        } else {
          errorMessage = "This student information conflicts with an existing record. Please check the Student ID.";
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditStudent = async (data: any) => {
    if (!selectedStudent) return;

    if (!schoolId) {
      toast({
        title: "Error",
        description: "School ID not found. Please refresh the page and try again.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const updateData = {
        studentId: data.studentId,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email, // Parent's email
        phone: data.phone,
        address: data.address,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
        classId: data.classId,
        admissionDate: data.admissionDate,
        parentContact: data.parentContact,
        emergencyContact: data.emergencyContact,
        medicalInfo: data.medicalInfo,
      };

      // Check if this is a localStorage student or existing student or database student
      if (selectedStudent.id.startsWith('student-')) {
        // Update localStorage student
        const existingStudents = JSON.parse(localStorage.getItem('students') || '[]');
        const updatedStudents = existingStudents.map((s: any) => {
          if (s.id === selectedStudent.id) {
            return {
              ...s,
              student_id: data.studentId,
              first_name: data.firstName,
              last_name: data.lastName,
              parent_email: data.email,
              phone: data.phone,
              address: data.address,
              date_of_birth: data.dateOfBirth,
              gender: data.gender,
              class_id: data.classId,
              admission_date: data.admissionDate,
              parent_contact: data.parentContact,
              emergency_contact: data.emergencyContact,
              medical_info: data.medicalInfo,
              updated_at: new Date().toISOString()
            };
          }
          return s;
        });
        localStorage.setItem('students', JSON.stringify(updatedStudents));
        console.log('Updated localStorage student record');
      } else if (selectedStudent.id.startsWith('existing-')) {
        // For existing students, update them in a special localStorage array
        const existingStudentsData = JSON.parse(localStorage.getItem('existingStudentsData') || '[]');

        // Create updated student record
        const updatedStudentRecord = {
          id: selectedStudent.id,
          student_id: data.studentId,
          first_name: data.firstName,
          last_name: data.lastName,
          parent_email: data.email,
          phone: data.phone,
          address: data.address,
          date_of_birth: data.dateOfBirth,
          gender: data.gender,
          class_id: data.classId,
          admission_date: data.admissionDate,
          parent_contact: data.parentContact,
          emergency_contact: data.emergencyContact,
          medical_info: data.medicalInfo,
          is_active: true,
          school_id: schoolId,
          updated_at: new Date().toISOString()
        };

        // Update or add the student record
        const existingIndex = existingStudentsData.findIndex((s: any) => s.id === selectedStudent.id);
        if (existingIndex >= 0) {
          existingStudentsData[existingIndex] = updatedStudentRecord;
        } else {
          existingStudentsData.push(updatedStudentRecord);
        }

        localStorage.setItem('existingStudentsData', JSON.stringify(existingStudentsData));
        console.log('Updated existing student record in localStorage:', updatedStudentRecord);
      } else {
        // Update database student
        await studentsService.update(selectedStudent.id, updateData);
        console.log('Updated database student record');
      }

      // Reload students to get the latest data
      await loadStudents();
      setIsEditDialogOpen(false);
      setSelectedStudent(null);
      toast({
        title: "Success",
        description: "Student updated successfully",
      });
      setIsEditDialogOpen(false);
      setSelectedStudent(null);
      await loadStudents(); // Reload to get fresh data
    } catch (error: any) {
      console.error('Failed to update student:', error);

      let errorMessage = "Failed to update student. Please try again.";

      if (error?.code === '23505') {
        if (error.message?.includes('student_id')) {
          errorMessage = "A student with this Student ID already exists. Please use a different Student ID.";
        } else {
          errorMessage = "This student information conflicts with an existing record. Please check the Student ID.";
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteStudent = async () => {
    if (!selectedStudent) return;

    setIsSubmitting(true);
    try {
      await studentsService.delete(selectedStudent.id);

      // Reload students to get the latest data
      await loadStudents();
      setIsDeleteDialogOpen(false);
      setSelectedStudent(null);
      toast({
        title: "Success",
        description: "Student deleted successfully",
      });
    } catch (error) {
      console.error('Failed to delete student:', error);
      toast({
        title: "Error",
        description: "Failed to delete student. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (student: Student) => {
    setSelectedStudent(student);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (student: Student) => {
    setSelectedStudent(student);
    setIsDeleteDialogOpen(true);
  };

  const openViewDialog = (student: Student) => {
    setSelectedStudent(student);
    setIsViewDialogOpen(true);
  };



  const filteredStudents = students.filter(
    (student) =>
      (student.firstName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (student.lastName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (student.studentId?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (student.class?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  // Mobile Card Component
  const StudentCard: React.FC<{ student: Student }> = ({ student }) => (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-primary font-medium text-sm">
                {(student.firstName || '').charAt(0)}{(student.lastName || '').charAt(0)}
              </span>
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-base text-gray-900 truncate">
                {student.firstName} {student.lastName}
              </h3>
              <p className="text-sm text-gray-600">ID: {student.studentId}</p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => openViewDialog(student)}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              {canEdit() && (
                <DropdownMenuItem onClick={() => openEditDialog(student)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Student
                </DropdownMenuItem>
              )}

              {canDelete() && (
                <DropdownMenuItem
                  onClick={() => openDeleteDialog(student)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Student
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <div className="w-4 h-4 bg-blue-100 rounded flex items-center justify-center">
              <span className="text-xs font-medium text-blue-600">C</span>
            </div>
            <span>Class {student.class}</span>
          </div>

        </div>

        <div className="space-y-2 mb-3">
          {student.email && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Mail className="h-4 w-4 text-gray-400" />
              <span className="truncate">{student.email}</span>
            </div>
          )}
          {student.phone && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Phone className="h-4 w-4 text-gray-400" />
              <span>{student.phone}</span>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Badge
              variant="secondary"
              className={`text-xs ${student.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
            >
              {student.isActive ? "Active" : "Inactive"}
            </Badge>
            {student.hasCredentials && (
              <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
                Has Login
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <>
      <div className="space-y-4 md:space-y-6">


        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">
              {profile?.role === 'parent' ? 'My Children' :
               profile?.role === 'student' ? 'My Profile' :
               profile?.role === 'teacher' ? 'My Students' : 'Student Management'}
            </h1>
            <p className="text-gray-600 mt-1 text-xs sm:text-sm md:text-base">
              {profile?.role === 'parent' ? 'View your children\'s information and progress' :
               profile?.role === 'student' ? 'View your academic information' :
               profile?.role === 'teacher' ? 'Manage students in your assigned classes' : 'Manage and track student information'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            {/* View Toggle - Only show on mobile */}
            <div className="md:hidden flex bg-gray-100 rounded-lg p-1">
              <Button
                variant={viewMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('table')}
                className="h-8 px-3"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'cards' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('cards')}
                className="h-8 px-3"
              >
                <Grid className="h-4 w-4" />
              </Button>
            </div>
            {canCreate() && (
              <Button
                onClick={() => setIsAddDialogOpen(true)}
                className="bg-primary hover:bg-primary/90 text-xs md:text-sm px-3 py-2 h-9"
              >
                <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Add New Student</span>
                <span className="sm:hidden">Add</span>
              </Button>
            )}
          </div>
        </div>

        {/* Student Metrics */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 md:gap-6">
          <MetricCard
            title="Total Students"
            value={students.length.toString()}
            icon={<User className="h-5 w-5 sm:h-6 sm:w-6" />}
            iconBg="bg-blue-500"
          />
          <MetricCard
            title="Active Students"
            value={students.filter(s => s.isActive).length.toString()}
            icon={<Users className="h-5 w-5 sm:h-6 sm:w-6" />}
            iconBg="bg-green-500"
          />
          <MetricCard
            title="Inactive Students"
            value={students.filter(s => !s.isActive).length.toString()}
            icon={<Calendar className="h-5 w-5 sm:h-6 sm:w-6" />}
            iconBg="bg-red-500"
          />
        </div>

        {/* Search */}
        <Card>
          <CardContent className="pt-4 sm:pt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search students by name, student ID, or class..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 text-sm sm:text-base py-2"
              />
            </div>
          </CardContent>
        </Card>

        {/* Students List */}
        {isMobile && viewMode === 'cards' ? (
          // Mobile Card View
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Students ({filteredStudents.length})</h2>
            </div>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading students...</span>
              </div>
            ) : filteredStudents.length === 0 ? (
              <Card>
                <CardContent className="py-8">
                  <div className="text-center text-gray-500">
                    No students found matching your search criteria.
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {filteredStudents.map((student) => (
                  <StudentCard
                    key={student.id}
                    student={student}
                    onEdit={openEditDialog}
                    onDelete={openDeleteDialog}
                    onView={openViewDialog}

                  />
                ))}
              </div>
            )}
          </div>
        ) : (
          // Table View (Desktop and Mobile Table Mode)
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">Students List</CardTitle>
              <CardDescription className="text-xs sm:text-sm">View and manage all registered students</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading students...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full min-w-[700px] sm:min-w-[900px] table-auto">
                  <thead>
                    <tr className="border-b bg-muted/30">
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-48">NAME</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-24">STUDENT ID</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-20">CLASS</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-16 hidden lg:table-cell">GENDER</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-36 hidden md:table-cell">PARENT EMAIL</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-20">STATUS</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-32">ACTIONS</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredStudents.map((student) => (
                      <tr key={student.id} className="border-b hover:bg-gray-50">
                        <td className="py-2 px-2 sm:py-3 sm:px-4">
                          <div className="flex items-center gap-2 sm:gap-3">
                            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                              <span className="text-primary font-medium text-xs sm:text-sm">
                                {(student.firstName || '').charAt(0)}{(student.lastName || '').charAt(0)}
                              </span>
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="font-medium text-xs sm:text-base">{student.firstName} {student.lastName}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4 text-gray-600 text-xs sm:text-sm font-mono">{student.studentId}</td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4 text-xs sm:text-sm font-medium">{student.class}</td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4 text-xs sm:text-sm text-gray-600 hidden lg:table-cell">{student.gender}</td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4 text-xs sm:text-sm text-gray-600 hidden md:table-cell">
                          <div className="flex flex-col">
                            <span>{student.email}</span>
                            <span className="text-xs text-gray-400">Login: {student.email}</span>
                            <span className="text-xs text-gray-400">Password: {student.studentId}</span>
                            <span className="text-xs text-blue-600 font-medium">Auto-generated</span>
                          </div>
                        </td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4">
                          <div className="flex flex-col gap-1">
                            <Badge
                              variant="secondary"
                              className={`text-xs w-fit ${student.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                            >
                              {student.isActive ? "Active" : "Inactive"}
                            </Badge>
                            <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs w-fit">
                              Has Login
                            </Badge>
                            {/* Simple status badges */}
                          </div>
                        </td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4">
                          <div className="flex gap-1 sm:flex-col sm:gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openViewDialog(student)}
                              className="text-gray-600 hover:text-gray-800 p-1 h-8 w-8"
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {canEdit() && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openEditDialog(student)}
                                className="text-blue-600 hover:text-blue-800 p-1 h-8 w-8"
                                title="Edit Student"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                            {canDelete() && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openDeleteDialog(student)}
                                className="text-red-600 hover:text-red-800 p-1 h-8 w-8"
                                title="Delete Student"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}

                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                  {filteredStudents.length === 0 && !isLoading && (
                    <div className="text-center py-8 text-gray-500 px-6 text-xs sm:text-base">
                      No students found matching your search criteria.
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Add Student Dialog */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto sm:max-w-[95vw] sm:mx-4">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Add New Student</DialogTitle>
              <DialogDescription className="text-sm">
                Fill in the student information below to add a new student to the system.
              </DialogDescription>
            </DialogHeader>
            <StudentForm
              onSubmit={handleAddStudent}
              onCancel={() => setIsAddDialogOpen(false)}
              isLoading={isSubmitting}
            />
          </DialogContent>
        </Dialog>

        {/* Edit Student Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto sm:max-w-[95vw] sm:mx-4">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Edit Student</DialogTitle>
              <DialogDescription className="text-sm">
                Update the student information below.
              </DialogDescription>
            </DialogHeader>
            {selectedStudent && (
              <StudentForm
                student={selectedStudent}
                onSubmit={handleEditStudent}
                onCancel={() => {
                  setIsEditDialogOpen(false);
                  setSelectedStudent(null);
                }}
                isLoading={isSubmitting}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* View Student Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-2xl sm:max-w-[95vw] sm:mx-4 max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Student Details</DialogTitle>
              <DialogDescription className="text-sm">
                View complete student information
              </DialogDescription>
            </DialogHeader>
            {selectedStudent && (
              <div className="space-y-4">
                {/* Student Avatar and Basic Info */}
                <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-primary font-medium text-lg">
                      {(selectedStudent.firstName || '').charAt(0)}{(selectedStudent.lastName || '').charAt(0)}
                    </span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-lg text-gray-900">
                      {selectedStudent.firstName} {selectedStudent.lastName}
                    </h3>
                    <p className="text-sm text-gray-600">Student ID: {selectedStudent.studentId}</p>
                    <div className="flex gap-2 mt-2">
                      <Badge
                        variant="secondary"
                        className={`text-xs ${selectedStudent.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                      >
                        {selectedStudent.isActive ? "Active" : "Inactive"}
                      </Badge>
                      {selectedStudent.hasCredentials && (
                        <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
                          Has Login
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Email</Label>
                    <p className="text-sm break-all">{selectedStudent.email}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Phone</Label>
                    <p className="text-sm">{selectedStudent.phone}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Class</Label>
                    <p className="text-sm">{selectedStudent.class}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Date of Birth</Label>
                    <p className="text-sm">{selectedStudent.dateOfBirth}</p>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Address</Label>
                  <p className="text-sm">{selectedStudent.address}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Emergency Contact</Label>
                  <p className="text-sm">{selectedStudent.emergencyContact}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Medical Information</Label>
                  <p className="text-sm">{selectedStudent.medicalInfo || 'None'}</p>
                </div>
                <div className="flex justify-end pt-4 border-t">
                  <Button onClick={() => setIsViewDialogOpen(false)} className="w-full sm:w-auto">
                    Close
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Student Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the student
                {selectedStudent && ` "${selectedStudent.firstName} ${selectedStudent.lastName}"`}
                and remove all associated data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setSelectedStudent(null)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteStudent}
                disabled={isSubmitting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isSubmitting ? 'Deleting...' : 'Delete Student'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>


      </div>
    </>
  );
};
