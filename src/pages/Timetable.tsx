import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Calendar,
  Clock,
  MapPin,
  User,
  BookOpen,
  Coffee,
  Utensils,
  Users,
  Edit,
  Plus,
  Loader2,
  Grid,
  List,
  MoreVertical
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { PeriodForm } from "@/components/forms/NewPeriodForm";
import { timetableService, classesService, subjectsService, teachersService, teacherClassesService, studentsService, getCurrentSchoolId } from "@/services/supabaseService";
import { useIsMobile } from "@/hooks/use-mobile";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

// Day mappings for timetable
const dayNumberToName = {
  1: 'monday',
  2: 'tuesday',
  3: 'wednesday',
  4: 'thursday',
  5: 'friday',
  6: 'saturday',
  7: 'sunday'
};

const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
const dayLabels = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
export const Timetable: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedDay, setSelectedDay] = useState<string>('monday');
  const [periods, setPeriods] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddPeriodDialogOpen, setIsAddPeriodDialogOpen] = useState(false);
  const [isEditTimetableDialogOpen, setIsEditTimetableDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingPeriod, setEditingPeriod] = useState<any>(null);
  const [isEditPeriodDialogOpen, setIsEditPeriodDialogOpen] = useState(false);
  const isMobile = useIsMobile();
  const [viewMode, setViewMode] = useState<'week' | 'day'>(isMobile ? 'day' : 'week');

  // Load data on component mount
  useEffect(() => {
    initializeData();
  }, []);

  // Load periods when class changes
  useEffect(() => {
    if (schoolId && selectedClass) {
      loadTimetable(schoolId, selectedClass);
    }
  }, [schoolId, selectedClass]);

  const initializeData = async () => {
    try {
      // Get school ID from URL
      const pathParts = window.location.pathname.split('/');
      const schoolSlug = pathParts[1];
      const currentSchoolId = await getCurrentSchoolId(schoolSlug);

      if (currentSchoolId) {
        setSchoolId(currentSchoolId);
        await Promise.all([
          loadClasses(currentSchoolId),
          loadSubjects(currentSchoolId),
          loadTeachers(currentSchoolId)
        ]);
      }
    } catch (error) {
      console.error('Error initializing data:', error);
      toast({
        title: "Error",
        description: "Failed to initialize data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadTimetable = async (schoolId: string, classId: string) => {
    try {
      console.log('📅 Loading timetable for class:', classId);
      const data = await timetableService.getByClass(schoolId, classId);
      console.log('📅 Timetable data loaded:', data?.length || 0, 'periods');
      setPeriods(data || []);
    } catch (error) {
      console.error('Error loading timetable:', error);
      setPeriods([]);
      toast({
        title: "Error",
        description: "Failed to load timetable data",
        variant: "destructive",
      });
    }
  };

  const loadClasses = async (schoolId: string) => {
    try {
      let availableClasses = [];

      if (user?.role === 'admin') {
        // Admin can see all classes
        const data = await classesService.getAll(schoolId);
        availableClasses = data || [];
      } else if (user?.role === 'teacher') {
        // Teacher can only see assigned classes
        const allTeachers = await teachersService.getAll(schoolId);
        const teacher = allTeachers.find((t: any) => t.email === user?.email);

        if (teacher) {
          const classAssignments = await teacherClassesService.getByTeacher(schoolId, teacher.id);
          availableClasses = classAssignments.map(assignment => assignment.class);
        }
      } else if (user?.role === 'student') {
        // Student can only see their own class
        const allStudents = await studentsService.getAll(schoolId);
        const student = allStudents.find((s: any) => s.parent_email === user?.email);

        if (student && student.class_id) {
          const allClasses = await classesService.getAll(schoolId);
          const studentClass = allClasses.find((c: any) => c.id === student.class_id);
          if (studentClass) {
            availableClasses = [studentClass];
          }
        }
      }

      console.log('📅 Timetable: Loaded classes for', user?.role, ':', availableClasses?.length || 0);
      setClasses(availableClasses);

      // Set first class as default
      if (availableClasses && availableClasses.length > 0) {
        setSelectedClass(availableClasses[0].id);
        console.log('📅 Timetable: Selected default class:', availableClasses[0].name, availableClasses[0].section);
      }
    } catch (error) {
      console.error('Error loading classes:', error);
    }
  };

  const loadSubjects = async (schoolId: string) => {
    try {
      const data = await subjectsService.getAll(schoolId);
      console.log('📅 Timetable: Loaded subjects:', data?.length || 0);
      setSubjects(data || []);
    } catch (error) {
      console.error('Error loading subjects:', error);
    }
  };

  const loadTeachers = async (schoolId: string) => {
    try {
      const data = await teachersService.getAll(schoolId);
      console.log('📅 Timetable: Loaded teachers:', data?.length || 0);
      setTeachers(data || []);
    } catch (error) {
      console.error('Error loading teachers:', error);
    }
  };

  const handleAddPeriod = async (data: any) => {
    setIsSubmitting(true);
    try {
      console.log('📅 Adding period with data:', data);

      // Prepare data for database
      const periodData = {
        class_id: data.class_id,
        subject_id: data.subject_id,
        teacher_id: data.teacher_id,
        day_of_week: parseInt(data.day_of_week),
        start_time: data.start_time,
        end_time: data.end_time,
        room_number: data.room_number || null,
        academic_year: '2024-25',
        is_active: true
      };

      // Create period in database
      await timetableService.create(periodData, schoolId);

      // Reload timetable data
      if (selectedClass) {
        await loadTimetable(schoolId, selectedClass);
      }

      setIsAddPeriodDialogOpen(false);
      toast({
        title: "Success",
        description: "Period added successfully to timetable",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while adding the period",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditTimetable = () => {
    toast({
      title: "Edit Mode Activated",
      description: "Click on any period in the timetable to edit it.",
    });
    setIsEditTimetableDialogOpen(false);
  };

  const handleEditPeriod = (period: any) => {
    setEditingPeriod(period);
    setIsEditPeriodDialogOpen(true);
  };

  const handleUpdatePeriod = async (data: any) => {
    if (!editingPeriod) return;

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check for time conflicts (excluding current period)
      const conflictingPeriod = periods.find(p =>
        p.id !== editingPeriod.id &&
        p.day === data.day &&
        p.class === data.class &&
        ((data.startTime >= p.startTime && data.startTime < p.endTime) ||
         (data.endTime > p.startTime && data.endTime <= p.endTime) ||
         (data.startTime <= p.startTime && data.endTime >= p.endTime))
      );

      if (conflictingPeriod) {
        toast({
          title: "Time Conflict",
          description: `This time slot conflicts with ${conflictingPeriod.subject} (${conflictingPeriod.startTime} - ${conflictingPeriod.endTime})`,
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      const updatedPeriod = {
        ...editingPeriod,
        ...data,
      };

      setPeriods(prev => prev.map(p => p.id === editingPeriod.id ? updatedPeriod : p));
      setIsEditPeriodDialogOpen(false);
      setEditingPeriod(null);
      toast({
        title: "Period Updated Successfully!",
        description: `${data.subject || data.type} has been updated in the timetable`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while updating the period",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeletePeriod = async (period: any) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setPeriods(prev => prev.filter(p => p.id !== period.id));
      toast({
        title: "Period Deleted",
        description: `${period.subject || period.type} has been removed from the timetable`,
        variant: "destructive",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while deleting the period",
        variant: "destructive",
      });
    }
  };

  const getPeriodIcon = (type: string) => {
    switch (type) {
      case 'regular':
        return <BookOpen className="h-4 w-4" />;
      case 'break':
        return <Coffee className="h-4 w-4" />;
      case 'lunch':
        return <Utensils className="h-4 w-4" />;
      case 'assembly':
        return <Users className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getPeriodColor = (type: string) => {
    switch (type) {
      case 'regular':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'break':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'lunch':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'assembly':
        return 'bg-purple-50 border-purple-200 text-purple-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getCurrentPeriod = () => {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    const currentDayNumber = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Find periods for current day and selected class
    const todayPeriods = periods.filter(period =>
      period.day_of_week === currentDayNumber &&
      period.class_id === selectedClass
    );

    return todayPeriods.find(period => {
      const startTime = parseInt(period.start_time.split(':')[0]) * 60 + parseInt(period.start_time.split(':')[1]);
      const endTime = parseInt(period.end_time.split(':')[0]) * 60 + parseInt(period.end_time.split(':')[1]);
      return currentTime >= startTime && currentTime <= endTime;
    }) || null;
  };

  const currentPeriod = getCurrentPeriod();

  const renderDaySchedule = (day: string) => {
    // Get day number from day name (monday = 1, tuesday = 2, etc.)
    const dayNumber = days.indexOf(day) + 1;

    // Filter periods for this specific day and class
    const dayPeriods = periods.filter(period =>
      period.day_of_week === dayNumber &&
      period.class_id === selectedClass
    );

    // Transform database periods to match expected format
    const schedule = dayPeriods.map(period => {
      // Use the joined subject and teacher data directly
      return {
        id: period.id,
        startTime: period.start_time,
        endTime: period.end_time,
        subject: period.subject?.name || 'Unknown Subject',
        teacher: period.teacher ? `${period.teacher.first_name} ${period.teacher.last_name}` : 'Unknown Teacher',
        room: period.room_number || 'TBA',
        type: 'regular'
      };
    });

    // If no periods found, show empty state
    if (schedule.length === 0) {
      return (
        <div className="space-y-3">
          <Card className="border-dashed border-2 border-gray-300">
            <CardContent className="p-6 text-center">
              <div className="text-gray-500">
                <Clock className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">No periods scheduled for {dayLabels[days.indexOf(day)]}</p>
                <p className="text-xs text-gray-400 mt-1">Add periods to see the timetable</p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {schedule.map((period) => (
          <Card
            key={period.id}
            className={`${getPeriodColor(period.type)} border-l-4 cursor-pointer hover:shadow-md transition-shadow duration-200`}
            onClick={() => (user?.role === 'admin' || user?.role === 'teacher') && handleEditPeriod(period)}
          >
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-2 sm:gap-3 flex-1 min-w-0">
                  {getPeriodIcon(period.type)}
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-sm sm:text-base truncate">{period.subject}</h3>
                    {period.teacher && (
                      <p className="text-xs sm:text-sm opacity-80 flex items-center gap-1 mt-1">
                        <User className="h-3 w-3 flex-shrink-0" />
                        <span className="truncate">{period.teacher}</span>
                      </p>
                    )}
                    {period.room && (
                      <p className="text-xs sm:text-sm opacity-80 flex items-center gap-1 mt-1">
                        <MapPin className="h-3 w-3 flex-shrink-0" />
                        <span className="truncate">{period.room}</span>
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="text-right">
                    <p className="font-medium text-xs sm:text-sm">{period.startTime} - {period.endTime}</p>
                    <p className="text-xs opacity-80">
                      {Math.abs(
                        (parseInt(period.endTime.split(':')[0]) * 60 + parseInt(period.endTime.split(':')[1])) -
                        (parseInt(period.startTime.split(':')[0]) * 60 + parseInt(period.startTime.split(':')[1]))
                      )} min
                    </p>
                  </div>
                  {(user?.role === 'admin' || user?.role === 'teacher') && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleEditPeriod(period);
                        }}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Period
                        </DropdownMenuItem>
                        {user?.role === 'admin' && (
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              if (confirm('Are you sure you want to delete this period?')) {
                                handleDeletePeriod(period);
                              }
                            }}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Plus className="h-4 w-4 mr-2 rotate-45" />
                            Delete Period
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
              {currentPeriod?.id === period.id && (
                <Badge className="mt-2 bg-green-500 text-white text-xs">Current Period</Badge>
              )}
              {(user?.role === 'admin' || user?.role === 'teacher') && !isMobile && (
                <p className="text-xs opacity-60 mt-2">Click to edit</p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderWeekView = () => {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
        {days.slice(0, 6).map((day, index) => (
          <Card key={day}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <Calendar className="h-4 w-4 sm:h-5 sm:w-5" />
                {dayLabels[index]}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              {renderDaySchedule(day)}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Timetable</h1>
          <p className="text-gray-600 mt-1 text-xs sm:text-sm md:text-base">
            View and manage class schedules and timetables
          </p>
        </div>
        <div className="flex items-center gap-2">
          {/* View Toggle - Only show on mobile */}
          <div className="md:hidden flex bg-gray-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'week' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('week')}
              className="h-8 px-3"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'day' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('day')}
              className="h-8 px-3"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
            {(user?.role === 'admin' || user?.role === 'teacher') && (
              <Button onClick={() => setIsAddPeriodDialogOpen(true)} className="text-xs md:text-sm px-3 py-2 h-9">
                <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Add Period</span>
                <span className="sm:hidden">Add</span>
              </Button>
            )}
            {(user?.role === 'admin' || user?.role === 'teacher') && (
              <Button variant="outline" onClick={handleEditTimetable} className="text-xs md:text-sm px-3 py-2 h-9">
                <Edit className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Edit Timetable</span>
                <span className="sm:hidden">Edit</span>
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Current Period Alert */}
      {currentPeriod && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              <div>
                <p className="font-semibold text-blue-800">Current Period</p>
                <p className="text-blue-600">
                  {currentPeriod.subject} • {currentPeriod.startTime} - {currentPeriod.endTime}
                  {currentPeriod.room && ` • ${currentPeriod.room}`}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Class Filter */}
      {(user?.role === 'admin' || user?.role === 'teacher') && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Timetable Filters</CardTitle>
            <CardDescription className="text-xs sm:text-sm">
              Select class to view specific timetable
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="space-y-2 flex-1 sm:flex-initial">
                <label className="text-sm font-medium">Class</label>
                <Select value={selectedClass} onValueChange={setSelectedClass}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Select class" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes.map(cls => (
                      <SelectItem key={cls.id} value={cls.id}>{cls.name} {cls.section}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Timetable Views */}
      {isMobile ? (
        // Mobile: Use state-controlled view mode
        <div className="space-y-4">
          {viewMode === 'week' ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Weekly Timetable - {selectedClass}</CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Complete weekly schedule overview
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin" />
                    <span className="ml-2 text-sm">Loading timetable...</span>
                  </div>
                ) : (
                  renderWeekView()
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Daily Timetable - {selectedClass}</CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Detailed view of daily schedule
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Select Day</label>
                    <Select value={selectedDay} onValueChange={setSelectedDay}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent>
                        {days.slice(0, 6).map((day, index) => (
                          <SelectItem key={day} value={day}>{dayLabels[index]}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin" />
                      <span className="ml-2 text-sm">Loading schedule...</span>
                    </div>
                  ) : (
                    renderDaySchedule(selectedDay)
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        // Desktop: Use tabs
        <Tabs defaultValue="week" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
            <TabsTrigger value="week">Week View</TabsTrigger>
            <TabsTrigger value="day">Day View</TabsTrigger>
          </TabsList>

          <TabsContent value="week" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Weekly Timetable - {selectedClass}</CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Complete weekly schedule overview
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin" />
                    <span className="ml-2">Loading timetable...</span>
                  </div>
                ) : (
                  renderWeekView()
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="day" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Daily Timetable - {selectedClass}</CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Detailed view of daily schedule
                </CardDescription>
              </CardHeader>
              <CardContent>
              <div className="mb-4">
                <Select value={selectedDay} onValueChange={setSelectedDay}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    {days.map((day, index) => (
                      <SelectItem key={day} value={day}>{dayLabels[index]}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading schedule...</span>
                </div>
              ) : (
                renderDaySchedule(selectedDay)
              )}
            </CardContent>
          </Card>
        </TabsContent>
        </Tabs>
      )}

      {/* Timetable Legend */}
      <Card>
        <CardHeader>
          <CardTitle>Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-200 border border-blue-300 rounded"></div>
              <span className="text-sm">Regular Classes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-200 border border-green-300 rounded"></div>
              <span className="text-sm">Break Time</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-200 border border-orange-300 rounded"></div>
              <span className="text-sm">Lunch Break</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-purple-200 border border-purple-300 rounded"></div>
              <span className="text-sm">Assembly</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Period Dialog */}
      <Dialog open={isAddPeriodDialogOpen} onOpenChange={setIsAddPeriodDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Period</DialogTitle>
            <DialogDescription>
              Add a new period to the timetable schedule.
            </DialogDescription>
          </DialogHeader>
          <PeriodForm
            onSubmit={handleAddPeriod}
            onCancel={() => setIsAddPeriodDialogOpen(false)}
            isLoading={isSubmitting}
            subjects={subjects}
            teachers={teachers}
            classes={classes}
            selectedClass={selectedClass}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Period Dialog */}
      <Dialog open={isEditPeriodDialogOpen} onOpenChange={setIsEditPeriodDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Period</DialogTitle>
            <DialogDescription>
              Update the period information and schedule.
            </DialogDescription>
          </DialogHeader>
          {editingPeriod && (
            <PeriodForm
              period={editingPeriod}
              onSubmit={handleUpdatePeriod}
              onCancel={() => {
                setIsEditPeriodDialogOpen(false);
                setEditingPeriod(null);
              }}
              isLoading={isSubmitting}
              subjects={subjects}
              teachers={teachers}
              classes={classes}
              selectedClass={selectedClass}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Timetable Dialog */}
      <Dialog open={isEditTimetableDialogOpen} onOpenChange={setIsEditTimetableDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Timetable Management</DialogTitle>
            <DialogDescription>
              Choose how you want to manage the timetable.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  setIsEditTimetableDialogOpen(false);
                  toast({
                    title: "Edit Mode Activated",
                    description: "Click on any period in the timetable to edit it.",
                  });
                }}
              >
                <Edit className="h-6 w-6 mb-2" />
                <span>Edit Individual Periods</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  setIsEditTimetableDialogOpen(false);
                  setIsAddPeriodDialogOpen(true);
                }}
              >
                <Plus className="h-6 w-6 mb-2" />
                <span>Add New Period</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  setIsEditTimetableDialogOpen(false);
                  toast({
                    title: "Bulk Operations",
                    description: "Bulk operations feature coming soon!",
                  });
                }}
              >
                <Calendar className="h-6 w-6 mb-2" />
                <span>Bulk Schedule Changes</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  setIsEditTimetableDialogOpen(false);
                  toast({
                    title: "Teacher Management",
                    description: "Teacher assignment feature coming soon!",
                  });
                }}
              >
                <Users className="h-6 w-6 mb-2" />
                <span>Teacher Assignments</span>
              </Button>
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setIsEditTimetableDialogOpen(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
