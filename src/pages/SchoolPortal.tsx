import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Building2, 
  Users, 
  GraduationCap, 
  UserCheck, 
  Eye, 
  EyeOff,
  Loader2,
  Shield
} from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";
import { studentsService, teachersService, getCurrentSchoolId } from "@/services/supabaseService";

export const SchoolPortal: React.FC = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const navigate = useNavigate();
  const { signIn } = useAuth();

  const [activeRole, setActiveRole] = useState<'admin' | 'teacher' | 'student'>('admin');
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [availableCredentials, setAvailableCredentials] = useState<{
    teachers: Array<{ employeeId: string; name: string }>;
    students: Array<{ studentId: string; parentEmail: string; name: string }>;
  }>({ teachers: [], students: [] });

  // Handle login for different roles
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!credentials.username || !credentials.password) {
      setError('Please enter both username and password');
      return;
    }

    setIsLoading(true);
    setError('');

    console.log('Login attempt:', {
      role: activeRole,
      username: credentials.username,
      password: credentials.password,
      schoolSlug
    });

    try {
      // Simulate role-based login logic
      const loginData = {
        email: credentials.username,
        password: credentials.password,
        role: activeRole,
        schoolSlug: schoolSlug
      };

      // Call the signIn function
      const result = await signIn(credentials.username, credentials.password);

      if (result.success) {
        // Navigate to role-specific dashboard
        navigate(`/${schoolSlug}/dashboard`);
      } else {
        setError(result.error || 'Invalid credentials. Please check your username and password.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const roleInfo = {
    admin: {
      title: 'Admin Login',
      description: 'Access the administrative dashboard to manage school operations',
      icon: Shield,
      color: 'bg-red-500',
      placeholder: {
        username: '<EMAIL>',
        password: 'Admin password'
      }
    },
    teacher: {
      title: 'Teacher Login',
      description: 'Access your teaching dashboard and manage your classes',
      icon: Users,
      color: 'bg-green-500',
      placeholder: {
        username: 'Your email address',
        password: 'Your Employee ID'
      }
    },
    student: {
      title: 'Student Login',
      description: 'Access your student portal and view your academic progress',
      icon: GraduationCap,
      color: 'bg-blue-500',
      placeholder: {
        username: 'Parent email address',
        password: 'Your Student ID'
      }
    }
  };

  const currentRole = roleInfo[activeRole];
  const IconComponent = currentRole.icon;

  // Load available credentials for testing
  const loadAvailableCredentials = async () => {
    if (!schoolSlug) return;

    try {
      const schoolId = await getCurrentSchoolId(schoolSlug);
      if (!schoolId) return;

      const [teachersData, studentsData] = await Promise.all([
        teachersService.getAll(schoolId),
        studentsService.getAll(schoolId)
      ]);

      const teachers = teachersData?.map(t => ({
        employeeId: t.employee_id || 'N/A',
        name: `${t.first_name || ''} ${t.last_name || ''}`.trim() || 'Unknown'
      })) || [];

      const students = studentsData?.map(s => ({
        studentId: s.student_id || 'N/A',
        parentEmail: s.parent_email || `parent.${s.student_id?.toLowerCase()}@example.com`,
        name: `${s.first_name || ''} ${s.last_name || ''}`.trim() || 'Unknown'
      })) || [];

      setAvailableCredentials({ teachers, students });
    } catch (error) {
      console.error('Failed to load credentials:', error);
    }
  };

  // Load credentials when component mounts
  React.useEffect(() => {
    loadAvailableCredentials();
  }, [schoolSlug]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        
        {/* School Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center">
            <div className="p-3 bg-white rounded-full shadow-lg">
              <Building2 className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 capitalize">
            {schoolSlug} School Portal
          </h1>
          <p className="text-gray-600">
            Select your role and login to access your dashboard
          </p>
        </div>

        {/* Login Card */}
        <Card className="shadow-xl">
          <CardHeader className="space-y-4">
            <div className="flex items-center justify-center">
              <div className={`p-3 rounded-full ${currentRole.color}`}>
                <IconComponent className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="text-center">
              <CardTitle className="text-xl">{currentRole.title}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {currentRole.description}
              </p>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Role Selection Tabs */}
            <Tabs value={activeRole} onValueChange={(value) => setActiveRole(value as any)}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="admin" className="text-xs">
                  <Shield className="h-3 w-3 mr-1" />
                  Admin
                </TabsTrigger>
                <TabsTrigger value="teacher" className="text-xs">
                  <Users className="h-3 w-3 mr-1" />
                  Teacher
                </TabsTrigger>
                <TabsTrigger value="student" className="text-xs">
                  <GraduationCap className="h-3 w-3 mr-1" />
                  Student
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Login Form */}
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder={currentRole.placeholder.username}
                  value={credentials.username}
                  onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder={currentRole.placeholder.password}
                    value={credentials.password}
                    onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  `Sign in as ${activeRole.charAt(0).toUpperCase() + activeRole.slice(1)}`
                )}
              </Button>
            </form>

            {/* Login Instructions */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
              <h4 className="text-sm font-medium text-gray-900">Login Instructions:</h4>
              {activeRole === 'admin' && (
                <p className="text-xs text-gray-600">
                  Use your admin email and password to access the administrative dashboard.
                </p>
              )}
              {activeRole === 'teacher' && (
                <div className="space-y-2">
                  <p className="text-xs text-gray-600">
                    Use your email address and Employee ID to login. If you don't have an account, please sign up first.
                  </p>
                  {availableCredentials.teachers.length > 0 && (
                    <div className="bg-blue-50 p-2 rounded border">
                      <p className="text-xs font-medium text-blue-800 mb-1">Registered Teachers:</p>
                      {availableCredentials.teachers.slice(0, 3).map((teacher, index) => (
                        <p key={index} className="text-xs text-blue-600">
                          {teacher.name} (ID: {teacher.employeeId})
                        </p>
                      ))}
                    </div>
                  )}
                </div>
              )}
              {activeRole === 'student' && (
                <div className="space-y-2">
                  <p className="text-xs text-gray-600">
                    Use parent's email and Student ID to login. If you don't have an account, please sign up first.
                  </p>
                  {availableCredentials.students.length > 0 && (
                    <div className="bg-blue-50 p-2 rounded border">
                      <p className="text-xs font-medium text-blue-800 mb-1">Registered Students:</p>
                      {availableCredentials.students.slice(0, 3).map((student, index) => (
                        <p key={index} className="text-xs text-blue-600">
                          {student.name} (ID: {student.studentId})
                        </p>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Signup Links */}
        <div className="text-center space-y-3">
          <p className="text-sm text-gray-600">Don't have an account?</p>
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/${schoolSlug}/signup/student`)}
              className="flex items-center gap-2"
            >
              <GraduationCap className="h-4 w-4" />
              Student Signup
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/${schoolSlug}/signup/teacher`)}
              className="flex items-center gap-2"
            >
              <Users className="h-4 w-4" />
              Teacher Signup
            </Button>
          </div>
        </div>

        {/* Alternative Login Link */}
        <div className="text-center">
          <Button
            variant="link"
            size="sm"
            onClick={() => navigate(`/${schoolSlug}/login`)}
            className="text-gray-500 hover:text-gray-700 text-xs"
          >
            Alternative Login Page
          </Button>
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Secure school management system
          </p>
        </div>

      </div>
    </div>
  );
};
