
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  School,
  Users,
  BookOpen,
  Calendar,
  BarChart3,
  Shield,
  Smartphone,
  ArrowRight,
  CheckCircle,
  Star,
  LogIn,
  Building2,
  ExternalLink
} from 'lucide-react';
import { PublicHeader } from '@/components/PublicHeader';
import { PublicFooter } from '@/components/PublicFooter';
import { SchoolFinder } from '@/components/SchoolFinder';

export const LandingPage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <Users className="h-8 w-8 text-blue-600" />,
      title: "Student Management",
      description: "Complete student information system with enrollment, profiles, and academic tracking."
    },
    {
      icon: <BookOpen className="h-8 w-8 text-green-600" />,
      title: "Academic Management",
      description: "Manage classes, subjects, assignments, and examinations with ease."
    },
    {
      icon: <Calendar className="h-8 w-8 text-purple-600" />,
      title: "Attendance Tracking",
      description: "Digital attendance system with real-time reporting and notifications."
    },
    {
      icon: <BarChart3 className="h-8 w-8 text-orange-600" />,
      title: "Analytics & Reports",
      description: "Comprehensive reporting and analytics for data-driven decisions."
    },
    {
      icon: <Shield className="h-8 w-8 text-red-600" />,
      title: "Multi-Tenant Security",
      description: "Each school has completely isolated data with role-based access control."
    },
    {
      icon: <Smartphone className="h-8 w-8 text-indigo-600" />,
      title: "Mobile Responsive",
      description: "Access your school management system from any device, anywhere."
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <PublicHeader />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4 text-center">
          <Badge className="mb-6 bg-blue-100 text-blue-800 border-blue-200">
            🚀 Multi-Tenant School Management Platform
          </Badge>
          
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Modern School Management
            <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent"> Made Simple</span>
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Give your school its own dedicated management portal with custom URLs, complete data isolation, 
            and role-based access for administrators, teachers, students, and parents.
          </p>

          {/* School Access Section */}
          <div className="max-w-2xl mx-auto mb-12">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Access Your School Portal</h3>
                <p className="text-gray-600">Enter your school name to access your dedicated management portal</p>
              </div>

              <SchoolFinder
                variant="full"
                showSuggestions={true}
                placeholder="Search for your school..."
                className="border-0 shadow-none bg-transparent"
              />

              <div className="flex items-center justify-center mt-6 pt-6 border-t border-gray-200">
                <span className="text-sm text-gray-500 mr-3">Don't have a school portal yet?</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/register-school')}
                  className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                >
                  <Building2 className="mr-2 h-4 w-4" />
                  Register Your School
                </Button>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button
              size="lg"
              variant="outline"
              className="px-8 py-4 text-lg border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white"
              onClick={() => navigate('/demo')}
            >
              <Star className="mr-2 h-5 w-5" />
              View Live Demo
            </Button>

            <Button
              size="lg"
              variant="outline"
              className="px-8 py-4 text-lg border-2"
              onClick={() => navigate('/features')}
            >
              <BookOpen className="mr-2 h-5 w-5" />
              View All Features
            </Button>
          </div>

          <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Free Setup</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Custom School URL</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Secure & Isolated</span>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything Your School Needs
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Comprehensive school management features designed for modern educational institutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-white shadow-lg hover:shadow-xl transition-all border-0">
                <CardHeader>
                  <div className="flex items-center gap-4">
                    {feature.icon}
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Multi-Tenant Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Each School Gets Its Own Portal
              </h2>
              
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <School className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Custom School URLs</h3>
                    <p className="text-gray-600">
                      Each school gets their own branded URL like <code className="bg-gray-100 px-2 py-1 rounded">sms.app/greenwood</code>
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-green-100 p-2 rounded-lg">
                    <Shield className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Complete Data Isolation</h3>
                    <p className="text-gray-600">
                      Your school's data is completely separate and secure from other schools.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-purple-100 p-2 rounded-lg">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Role-Based Access</h3>
                    <p className="text-gray-600">
                      Different access levels for administrators, teachers, students, and parents.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-blue-500 to-green-500 p-8 rounded-2xl text-white">
              <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
              <p className="text-blue-100 mb-6">
                Set up your school's management portal in less than 5 minutes. 
                Complete with admin account and custom URL.
              </p>
              
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-300" />
                  <span>Instant setup</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-300" />
                  <span>Admin account included</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-300" />
                  <span>No technical knowledge required</span>
                </div>
              </div>

              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-gray-100 w-full"
                onClick={() => navigate('/register-school')}
              >
                Register Your School Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Schools Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Try Demo School Portals
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Experience the full functionality by accessing our demo school portals with different user roles.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {/* Demo School Cards */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Greenwood High School</CardTitle>
                  <Badge className="bg-green-100 text-green-800">Demo</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">
                  A comprehensive high school with 1,200+ students and advanced features.
                </p>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/greenwood/login')}
                  >
                    <span>Admin Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/greenwood/login')}
                  >
                    <span>Teacher Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/greenwood/login')}
                  >
                    <span>Student Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Riverside Elementary</CardTitle>
                  <Badge className="bg-blue-100 text-blue-800">Demo</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">
                  Elementary school focused on parent engagement and basic management.
                </p>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/riverside/login')}
                  >
                    <span>Admin Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/riverside/login')}
                  >
                    <span>Teacher Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/riverside/login')}
                  >
                    <span>Parent Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Tech Academy</CardTitle>
                  <Badge className="bg-purple-100 text-purple-800">Demo</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">
                  Modern technical school with advanced grading and project management.
                </p>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/techacademy/login')}
                  >
                    <span>Admin Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/techacademy/login')}
                  >
                    <span>Teacher Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => navigate('/techacademy/login')}
                  >
                    <span>Student Portal</span>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">
              Use demo credentials: <code className="bg-gray-200 px-2 py-1 rounded"><EMAIL></code> / <code className="bg-gray-200 px-2 py-1 rounded">demo123</code>
            </p>
            <Button
              variant="outline"
              size="lg"
              onClick={() => navigate('/features')}
              className="border-2"
            >
              <BookOpen className="mr-2 h-5 w-5" />
              View All Features
            </Button>
          </div>
        </div>
      </section>

      <PublicFooter />
    </div>
  );
};
