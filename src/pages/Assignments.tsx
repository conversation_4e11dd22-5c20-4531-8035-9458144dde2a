import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import { 
  Search, 
  Plus,
  Edit,
  Eye,
  FileText,
  Clock,
  Users,
  CheckCircle,
  AlertCircle,
  Upload,
  Loader2,
  Trash2
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { MetricCard } from "@/components/MetricCard";
import { AssignmentForm } from "@/components/forms/AssignmentForm";
import { assignmentsService, classesService, subjectsService, getCurrentSchoolId } from "@/services/supabaseService";
import { Assignment } from "@/types";

export const Assignments: React.FC = () => {
  const { user } = useAuth();
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const [selectedClass, setSelectedClass] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState<Assignment | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load data on component mount
  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      // Get school ID from URL
      const pathParts = window.location.pathname.split('/');
      const schoolSlug = pathParts[1];
      const currentSchoolId = await getCurrentSchoolId(schoolSlug);

      if (currentSchoolId) {
        setSchoolId(currentSchoolId);
        await Promise.all([
          loadAssignments(currentSchoolId),
          loadSubjects(currentSchoolId),
          loadClasses(currentSchoolId)
        ]);
      }
    } catch (error) {
      console.error('Error initializing data:', error);
      toast({
        title: "Error",
        description: "Failed to initialize data",
        variant: "destructive",
      });
    }
  };

  const loadAssignments = async (schoolId: string) => {
    setIsLoading(true);
    try {
      const data = await assignmentsService.getAll(schoolId);
      setAssignments(data || []);
    } catch (error) {
      console.error('Error loading assignments:', error);
      toast({
        title: "Error",
        description: "Failed to load assignments",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubjects = async (schoolId: string) => {
    try {
      const data = await subjectsService.getAll(schoolId);
      setSubjects(data || []);
    } catch (error) {
      console.error('Error loading subjects:', error);
    }
  };

  const loadClasses = async (schoolId: string) => {
    try {
      const data = await classesService.getAll(schoolId);
      setClasses(data || []);
    } catch (error) {
      console.error('Error loading classes:', error);
    }
  };

  const handleCreateAssignment = async (data: any) => {
    if (!schoolId) return;

    setIsSubmitting(true);
    try {
      const assignmentData = {
        title: data.title,
        description: data.description,
        subject_id: data.subjectId,
        class_id: data.classId,
        teacher_id: user?.id || '',
        due_date: data.dueDate,
        total_marks: data.totalMarks || 100,
        instructions: data.instructions,
        is_active: true
      };

      const newAssignment = await assignmentsService.create(assignmentData, schoolId);
      await loadAssignments(schoolId); // Reload to get updated data
      setIsCreateOpen(false);

      toast({
        title: "Success",
        description: "Assignment created successfully",
      });
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast({
        title: "Error",
        description: "Failed to create assignment",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditAssignment = async (data: any) => {
    if (!selectedAssignment || !schoolId) return;

    setIsSubmitting(true);
    try {
      const assignmentData = {
        title: data.title,
        description: data.description,
        subject_id: data.subjectId,
        class_id: data.classId,
        due_date: data.dueDate,
        total_marks: data.totalMarks || 100,
        instructions: data.instructions
      };

      await assignmentsService.update(selectedAssignment.id, assignmentData);
      await loadAssignments(schoolId); // Reload to get updated data
      setIsEditOpen(false);
      setSelectedAssignment(null);

      toast({
        title: "Success",
        description: "Assignment updated successfully",
      });
    } catch (error) {
      console.error('Error updating assignment:', error);
      toast({
        title: "Error",
        description: "Failed to update assignment",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteAssignment = async () => {
    if (!selectedAssignment || !schoolId) return;

    setIsSubmitting(true);
    try {
      await assignmentsService.delete(selectedAssignment.id);
      await loadAssignments(schoolId); // Reload to get updated data
      setIsDeleteOpen(false);
      setSelectedAssignment(null);

      toast({
        title: "Success",
        description: "Assignment deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting assignment:', error);
      toast({
        title: "Error",
        description: "Failed to delete assignment",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (assignment: Assignment) => {
    setSelectedAssignment(assignment);
    setIsEditOpen(true);
  };

  const openDeleteDialog = (assignment: Assignment) => {
    setSelectedAssignment(assignment);
    setIsDeleteOpen(true);
  };

  const openViewDialog = (assignment: Assignment) => {
    setSelectedAssignment(assignment);
    setIsViewOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Published</Badge>;
      case 'draft':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Draft</Badge>;
      case 'closed':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Closed</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getSubmissionRate = (submissions: number, total: number) => {
    const rate = (submissions / total) * 100;
    return rate.toFixed(0);
  };

  const filteredAssignments = assignments.filter(assignment => {
    const matchesSearch = (assignment.title?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (assignment.description?.toLowerCase() || '').includes(searchTerm.toLowerCase());
    const matchesSubject = selectedSubject === 'all' || assignment.subject === selectedSubject;
    const matchesClass = selectedClass === 'all' || assignment.class === selectedClass;

    return matchesSearch && matchesSubject && matchesClass;
  });

  // Calculate statistics
  const totalAssignments = assignments.length;
  const publishedAssignments = assignments.filter(a => a.status === 'published').length;
  const draftAssignments = assignments.filter(a => a.status === 'draft').length;
  const totalSubmissions = assignments.reduce((sum, a) => sum + a.submissions, 0);
  const totalPossibleSubmissions = assignments.reduce((sum, a) => sum + a.totalStudents, 0);
  const averageSubmissionRate = totalPossibleSubmissions > 0 ? (totalSubmissions / totalPossibleSubmissions * 100).toFixed(1) : '0';

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Assignment Management</h1>
          <p className="text-gray-600 mt-1">
            Create, manage, and track student assignments
          </p>
        </div>
        <div className="flex gap-2">
          {user?.role === 'teacher' && (
            <Button onClick={() => setIsCreateOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Assignment
            </Button>
          )}
        </div>
      </div>

      {/* Assignment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Assignments"
          value={totalAssignments.toString()}
          icon={<FileText className="h-6 w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Published"
          value={publishedAssignments.toString()}
          icon={<CheckCircle className="h-6 w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Drafts"
          value={draftAssignments.toString()}
          icon={<Edit className="h-6 w-6" />}
          iconBg="bg-yellow-500"
        />
        <MetricCard
          title="Submission Rate"
          value={`${averageSubmissionRate}%`}
          icon={<Upload className="h-6 w-6" />}
          iconBg="bg-purple-500"
        />
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Assignment Filters</CardTitle>
          <CardDescription>
            Filter assignments by subject, class, or search by title
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Subject</Label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="All subjects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {subjects.map(subject => (
                    <SelectItem key={subject.id} value={subject.id}>{subject.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Class</Label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger>
                  <SelectValue placeholder="All classes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>{cls.name} {cls.section}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search assignments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button className="w-full" variant="outline">
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Assignments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Assignments</CardTitle>
          <CardDescription>
            Manage all assignments and track student submissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading assignments...</span>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Assignment</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Class</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Submissions</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssignments.map((assignment) => (
                  <TableRow key={assignment.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{assignment.title}</p>
                        <p className="text-sm text-gray-500 line-clamp-1">{assignment.description}</p>
                      </div>
                    </TableCell>
                    <TableCell>{assignment.subject}</TableCell>
                    <TableCell>{assignment.class}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {isOverdue(assignment.dueDate) && assignment.status === 'published' && (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        )}
                        <div>
                          <p className={`font-medium ${isOverdue(assignment.dueDate) && assignment.status === 'published' ? 'text-red-600' : ''}`}>
                            {new Date(assignment.dueDate).toLocaleDateString()}
                          </p>
                          <p className="text-sm text-gray-500">
                            {isOverdue(assignment.dueDate) && assignment.status === 'published' ? 'Overdue' : 'Upcoming'}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{assignment.submissions}/{assignment.totalStudents}</p>
                        <p className="text-sm text-gray-500">{getSubmissionRate(assignment.submissions, assignment.totalStudents)}% submitted</p>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(assignment.status)}</TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => openViewDialog(assignment)}>
                          <Eye className="h-3 w-3" />
                        </Button>
                        {user?.role === 'teacher' && (
                          <>
                            <Button size="sm" variant="outline" onClick={() => openEditDialog(assignment)}>
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => openDeleteDialog(assignment)}>
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
          {filteredAssignments.length === 0 && !isLoading && (
            <div className="text-center py-8 text-gray-500">
              No assignments found matching your search criteria.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Assignment Dialog */}
      <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Assignment</DialogTitle>
            <DialogDescription>
              Create a new assignment for your students
            </DialogDescription>
          </DialogHeader>
          <AssignmentForm
            subjects={subjects}
            classes={classes}
            onSubmit={handleCreateAssignment}
            onCancel={() => setIsCreateOpen(false)}
            isLoading={isSubmitting}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Assignment Dialog */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Assignment</DialogTitle>
            <DialogDescription>
              Update the assignment details
            </DialogDescription>
          </DialogHeader>
          {selectedAssignment && (
            <AssignmentForm
              assignment={selectedAssignment}
              subjects={subjects}
              classes={classes}
              onSubmit={handleEditAssignment}
              onCancel={() => {
                setIsEditOpen(false);
                setSelectedAssignment(null);
              }}
              isLoading={isSubmitting}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Assignment Dialog */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Assignment Details</DialogTitle>
            <DialogDescription>
              View complete assignment information
            </DialogDescription>
          </DialogHeader>
          {selectedAssignment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Title</Label>
                  <p className="text-sm">{selectedAssignment.title}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Subject</Label>
                  <p className="text-sm">{selectedAssignment.subject}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Class</Label>
                  <p className="text-sm">{selectedAssignment.class}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Due Date</Label>
                  <p className="text-sm">{new Date(selectedAssignment.dueDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Max Score</Label>
                  <p className="text-sm">{selectedAssignment.maxScore}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Status</Label>
                  <p className="text-sm capitalize">{selectedAssignment.status}</p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Description</Label>
                <p className="text-sm">{selectedAssignment.description}</p>
              </div>
              {selectedAssignment.instructions && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">Instructions</Label>
                  <p className="text-sm">{selectedAssignment.instructions}</p>
                </div>
              )}
              <div className="flex justify-end">
                <Button onClick={() => setIsViewOpen(false)}>Close</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Assignment Dialog */}
      <AlertDialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the assignment
              {selectedAssignment && ` "${selectedAssignment.title}"`}
              and all associated submissions.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedAssignment(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAssignment}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? 'Deleting...' : 'Delete Assignment'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
