import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  CalendarIcon,
  Search,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  UserCheck,
  UserX,
  AlertTriangle,
  Grid,
  List,
  MoreVertical,
  User
} from "lucide-react";
import { format } from "date-fns";
import { useAuth } from "@/contexts/NewAuthContext";
import { MetricCard } from "@/components/MetricCard";
import { useIsMobile } from "@/hooks/use-mobile";
import { useDataAccess } from "@/hooks/useDataAccess.tsx";
import { AttendanceService } from "@/services";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

export const Attendance: React.FC = () => {
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedClass, setSelectedClass] = useState<string>('10-A');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [attendanceData, setAttendanceData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const isMobile = useIsMobile();
  const [viewMode, setViewMode] = useState<'table' | 'cards'>(isMobile ? 'cards' : 'table');
  const { canCreate, canEdit } = useDataAccess('attendance');

  // Load attendance data on component mount
  useEffect(() => {
    loadAttendanceData();
  }, [selectedDate, selectedClass]);

  const loadAttendanceData = async () => {
    setIsLoading(true);
    try {
      const attendanceRecords = await AttendanceService.getAttendanceByDate(
        format(selectedDate, 'yyyy-MM-dd')
      );

      // Transform the data to match the expected format
      const transformedData = attendanceRecords.map((record: any) => ({
        id: record.id,
        studentId: record.student_id,
        name: `Student ${record.student_id}`, // Will be replaced with actual student names
        class: selectedClass,
        status: record.status,
        time: record.marked_at ? format(new Date(record.marked_at), 'hh:mm a') : '-'
      }));

      setAttendanceData(transformedData);
    } catch (error) {
      console.error('Failed to load attendance data:', error);
      // Use mock data as fallback
      const mockData = [
        { id: '1', studentId: 'S001', name: 'Alice Johnson', class: '10-A', status: 'present', time: '09:00 AM' },
        { id: '2', studentId: 'S002', name: 'Bob Smith', class: '10-A', status: 'absent', time: '-' },
        { id: '3', studentId: 'S003', name: 'Charlie Brown', class: '10-A', status: 'late', time: '09:15 AM' },
        { id: '4', studentId: 'S004', name: 'Diana Prince', class: '10-A', status: 'present', time: '08:55 AM' },
        { id: '5', studentId: 'S005', name: 'Edward Norton', class: '10-A', status: 'excused', time: '-' },
      ];
      setAttendanceData(mockData);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-switch to cards view on mobile
  useEffect(() => {
    if (isMobile && viewMode === 'table') {
      setViewMode('cards');
    }
  }, [isMobile, viewMode]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'present':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100"><CheckCircle className="w-3 h-3 mr-1" />Present</Badge>;
      case 'absent':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100"><XCircle className="w-3 h-3 mr-1" />Absent</Badge>;
      case 'late':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100"><Clock className="w-3 h-3 mr-1" />Late</Badge>;
      case 'excused':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100"><AlertTriangle className="w-3 h-3 mr-1" />Excused</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const updateAttendance = (studentId: string, newStatus: string) => {
    setAttendanceData(prev => 
      prev.map(student => 
        student.studentId === studentId 
          ? { ...student, status: newStatus, time: newStatus === 'present' || newStatus === 'late' ? new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '-' }
          : student
      )
    );
  };

  const filteredData = attendanceData.filter(student =>
    (student.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (student.studentId?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  const attendanceStats = {
    total: attendanceData.length,
    present: attendanceData.filter(s => s.status === 'present').length,
    absent: attendanceData.filter(s => s.status === 'absent').length,
    late: attendanceData.filter(s => s.status === 'late').length,
    excused: attendanceData.filter(s => s.status === 'excused').length,
  };

  const attendanceRate = ((attendanceStats.present + attendanceStats.late) / attendanceStats.total * 100).toFixed(1);

  // Mobile Card Component
  const AttendanceCard: React.FC<{ student: any }> = ({ student }) => (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
              <User className="h-6 w-6 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-base text-gray-900 truncate">
                {student.name}
              </h3>
              <p className="text-sm text-gray-600">ID: {student.studentId} • Class: {student.class}</p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => updateAttendance(student.studentId, 'present')}>
                <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                Mark Present
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => updateAttendance(student.studentId, 'absent')}>
                <XCircle className="h-4 w-4 mr-2 text-red-600" />
                Mark Absent
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => updateAttendance(student.studentId, 'late')}>
                <Clock className="h-4 w-4 mr-2 text-yellow-600" />
                Mark Late
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => updateAttendance(student.studentId, 'excused')}>
                <AlertTriangle className="h-4 w-4 mr-2 text-blue-600" />
                Mark Excused
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Status:</span>
            {getStatusBadge(student.status)}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Time:</span>
            <span className="text-sm font-medium">{student.time}</span>
          </div>
        </div>

        <div className="mt-4 pt-3 border-t">
          <div className="grid grid-cols-3 gap-2">
            <Button
              size="sm"
              variant={student.status === 'present' ? 'default' : 'outline'}
              onClick={() => updateAttendance(student.studentId, 'present')}
              className="h-8 text-xs"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Present
            </Button>
            <Button
              size="sm"
              variant={student.status === 'absent' ? 'destructive' : 'outline'}
              onClick={() => updateAttendance(student.studentId, 'absent')}
              className="h-8 text-xs"
            >
              <XCircle className="h-3 w-3 mr-1" />
              Absent
            </Button>
            <Button
              size="sm"
              variant={student.status === 'late' ? 'secondary' : 'outline'}
              onClick={() => updateAttendance(student.studentId, 'late')}
              className="h-8 text-xs"
            >
              <Clock className="h-3 w-3 mr-1" />
              Late
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Attendance Management</h1>
          <p className="text-gray-600 mt-1 text-xs sm:text-sm md:text-base">
            Track and manage student attendance efficiently
          </p>
        </div>
        <div className="flex items-center gap-2">
          {/* View Toggle - Only show on mobile */}
          <div className="md:hidden flex bg-gray-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
              className="h-8 px-3"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('cards')}
              className="h-8 px-3"
            >
              <Grid className="h-4 w-4" />
            </Button>
          </div>
          <Button variant="outline" className="text-xs md:text-sm px-3 py-2 h-9">
            <Download className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Export</span>
            <span className="sm:hidden">Export</span>
          </Button>
        </div>
      </div>

      {/* Attendance Statistics */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 md:gap-4">
        <MetricCard
          title="Total Students"
          value={attendanceStats.total.toString()}
          icon={<Users className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Present"
          value={attendanceStats.present.toString()}
          icon={<UserCheck className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Absent"
          value={attendanceStats.absent.toString()}
          icon={<UserX className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-red-500"
        />
        <MetricCard
          title="Late"
          value={attendanceStats.late.toString()}
          icon={<Clock className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-yellow-500"
        />
        <MetricCard
          title="Attendance Rate"
          value={`${attendanceRate}%`}
          icon={<CheckCircle className="h-4 w-4 sm:h-6 sm:w-6" />}
          iconBg="bg-emerald-500"
        />
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base sm:text-lg">Attendance Filters</CardTitle>
          <CardDescription className="text-xs sm:text-sm">
            Select date, class, and search for specific students
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label className="text-sm">Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal h-10 text-sm">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => date && setSelectedDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label className="text-sm">Class</Label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10-A">Grade 10-A</SelectItem>
                  <SelectItem value="10-B">Grade 10-B</SelectItem>
                  <SelectItem value="11-A">Grade 11-A</SelectItem>
                  <SelectItem value="11-B">Grade 11-B</SelectItem>
                  <SelectItem value="12-A">Grade 12-A</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2 sm:col-span-2 lg:col-span-1">
              <Label className="text-sm">Search Student</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name or ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10 text-sm"
                />
              </div>
            </div>

            <div className="space-y-2 flex flex-col justify-end">
              <Button className="w-full h-10 text-sm">
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attendance List */}
      {isMobile && viewMode === 'cards' ? (
        // Mobile Card View
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Student Attendance - {selectedClass} ({filteredData.length})
            </h2>
          </div>
          <p className="text-sm text-gray-600">
            Mark attendance for {format(selectedDate, "MMMM d, yyyy")}
          </p>
          {filteredData.length === 0 ? (
            <Card>
              <CardContent className="py-8">
                <div className="text-center text-gray-500">
                  No students found matching your search criteria.
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {filteredData.map((student) => (
                <AttendanceCard key={student.id} student={student} />
              ))}
            </div>
          )}
        </div>
      ) : (
        // Table View (Desktop and Mobile Table Mode)
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Student Attendance - {selectedClass}</CardTitle>
            <CardDescription className="text-xs sm:text-sm">
              Mark attendance for {format(selectedDate, "MMMM d, yyyy")}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table className="min-w-[700px]">
                <TableHeader>
                  <TableRow className="bg-muted/30">
                    <TableHead className="text-xs sm:text-sm w-24">Student ID</TableHead>
                    <TableHead className="text-xs sm:text-sm w-48">Name</TableHead>
                    <TableHead className="text-xs sm:text-sm w-20 hidden md:table-cell">Class</TableHead>
                    <TableHead className="text-xs sm:text-sm w-24">Status</TableHead>
                    <TableHead className="text-xs sm:text-sm w-20 hidden lg:table-cell">Time</TableHead>
                    <TableHead className="text-xs sm:text-sm w-32">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((student) => (
                    <TableRow key={student.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium text-xs sm:text-sm py-2">{student.studentId}</TableCell>
                      <TableCell className="text-xs sm:text-sm py-2">{student.name}</TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 hidden md:table-cell">{student.class}</TableCell>
                      <TableCell className="py-2">{getStatusBadge(student.status)}</TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 hidden lg:table-cell">{student.time}</TableCell>
                      <TableCell className="py-2">
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant={student.status === 'present' ? 'default' : 'outline'}
                            onClick={() => updateAttendance(student.studentId, 'present')}
                            className="h-7 w-7 p-0"
                            title="Mark Present"
                          >
                            <CheckCircle className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant={student.status === 'absent' ? 'destructive' : 'outline'}
                            onClick={() => updateAttendance(student.studentId, 'absent')}
                            className="h-7 w-7 p-0"
                            title="Mark Absent"
                          >
                            <XCircle className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant={student.status === 'late' ? 'secondary' : 'outline'}
                            onClick={() => updateAttendance(student.studentId, 'late')}
                            className="h-7 w-7 p-0"
                            title="Mark Late"
                          >
                            <Clock className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
