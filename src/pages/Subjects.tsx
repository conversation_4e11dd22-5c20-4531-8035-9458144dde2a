import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { SubjectForm } from '@/components/forms/SubjectForm';
import { subjectsService, getCurrentSchoolId } from '@/services/supabaseService';
import { useDataAccess } from '@/hooks/useDataAccess.tsx';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  BookOpen, 
  Users, 
  Clock, 
  GraduationCap,
  Filter,
  MoreVertical
} from 'lucide-react';
import { Subject } from '@/types';
export const Subjects: React.FC = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { user, hasPermission, hasRole } = useAuth();
  const { toast } = useToast();
  const [subjects, setSubjects] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('');

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const { canCreate, canEdit, canDelete } = useDataAccess('subjects');

  // Check permissions - in single-user admin system, admin has all permissions
  const canManageSubjects = hasRole('admin') || hasRole('teacher');
  const canDeleteSubjects = hasRole('admin');

  // Load school ID and subjects
  useEffect(() => {
    const initializeData = async () => {
      if (!schoolSlug) return;

      try {
        const currentSchoolId = await getCurrentSchoolId(schoolSlug);
        if (currentSchoolId) {
          setSchoolId(currentSchoolId);
        }
      } catch (error) {
        console.error('Error getting school ID:', error);
        toast({
          title: "Error",
          description: "Failed to load school information",
          variant: "destructive",
        });
      }
    };

    initializeData();
  }, [schoolSlug]);

  useEffect(() => {
    if (schoolId) {
      loadSubjects();
    }
  }, [schoolId]);

  const loadSubjects = async () => {
    if (!schoolId) return;

    setIsLoading(true);
    try {
      const subjectsData = await subjectsService.getAll(schoolId);

      // Transform the data to match the expected Subject interface
      const transformedSubjects = subjectsData.map((subject: any) => ({
        id: subject.id,
        name: subject.name,
        department: subject.department || 'General', // Use stored department or default
        isActive: subject.is_active,
        createdAt: new Date(subject.created_at),
        updatedAt: new Date(subject.updated_at)
      }));

      setSubjects(transformedSubjects);
    } catch (error) {
      console.error('Failed to load subjects:', error);
      toast({
        title: "Error",
        description: "Failed to load subjects. Please try again.",
        variant: "destructive",
      });
      setSubjects([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Get unique departments for filtering
  const departments = [...new Set(subjects.map(subject => subject.department))];

  // Filter subjects based on search and filters
  const filteredSubjects = subjects.filter(subject => {
    const matchesSearch = (subject.name?.toLowerCase() || '').includes(searchTerm.toLowerCase());
    const matchesDepartment = !filterDepartment || subject.department === filterDepartment;

    return matchesSearch && matchesDepartment;
  });

  const handleAddSubject = async (subjectData: any) => {
    if (!schoolId) return;

    try {
      const newSubjectData = {
        name: subjectData.name,
        department: subjectData.department || null, // Optional department
      };

      await subjectsService.create(newSubjectData, schoolId);

      // Reload subjects to get the latest data
      await loadSubjects();
      setIsAddDialogOpen(false);
      toast({
        title: "Subject Added Successfully!",
        description: `${subjectData.name} has been added.`,
      });
    } catch (error: any) {
      console.error('Failed to create subject:', error);

      let errorMessage = "Failed to create subject. Please try again.";

      if (error?.code === '23505') {
        errorMessage = "A subject with this name already exists.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleEditSubject = async (subjectData: any) => {
    if (!selectedSubject) return;

    try {
      const updateData = {
        name: subjectData.name,
        department: subjectData.department || null,
      };

      await subjectsService.update(selectedSubject.id, updateData);

      // Reload subjects to get the latest data
      await loadSubjects();
      setIsEditDialogOpen(false);
      setSelectedSubject(null);
      toast({
        title: "Subject Updated Successfully!",
        description: `${subjectData.name} has been updated.`,
      });
    } catch (error: any) {
      console.error('Failed to update subject:', error);

      let errorMessage = "Failed to update subject. Please try again.";

      if (error?.code === '23505') {
        errorMessage = "A subject with this name already exists.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleDeleteSubject = async (subject: any) => {
    try {
      await subjectsService.delete(subject.id);

      // Reload subjects to get the latest data
      await loadSubjects();
      toast({
        title: "Subject Deleted",
        description: `${subject.name} has been successfully deleted.`,
        variant: "destructive",
      });
    } catch (error: any) {
      console.error('Failed to delete subject:', error);
      toast({
        title: "Error",
        description: "Failed to delete subject. Please try again.",
        variant: "destructive",
      });
    }
  };

  const openViewDialog = (subject: Subject) => {
    setSelectedSubject(subject);
    setIsViewDialogOpen(true);
  };

  const openEditDialog = (subject: Subject) => {
    setSelectedSubject(subject);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (subject: Subject) => {
    setSelectedSubject(subject);
  };

  const getSubjectStats = () => {
    const totalSubjects = subjects.length;
    const activeSubjects = subjects.filter(s => s.isActive).length;
    const departmentCount = departments.length;

    return { totalSubjects, activeSubjects, departmentCount };
  };

  const stats = getSubjectStats();

  if (!canManageSubjects) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
          <p className="text-gray-600">You don't have permission to manage subjects.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Subject Management</h1>
          <p className="text-gray-600 mt-1 text-sm md:text-base">Manage academic subjects and curricula</p>
        </div>
        {canCreate() && (
          <Button onClick={() => setIsAddDialogOpen(true)} className="bg-primary hover:bg-primary/90 text-xs md:text-sm">
            <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Add New Subject</span>
            <span className="sm:hidden">Add Subject</span>
          </Button>
        )}
      </div>

      {/* Subject Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        <Card className="stat-card">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center gap-3">
              <div className="metric-icon bg-blue-500">
                <BookOpen className="h-5 w-5 md:h-6 md:w-6" />
              </div>
              <div>
                <p className="text-xs md:text-sm font-medium text-gray-600">Total Subjects</p>
                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.totalSubjects}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="stat-card">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center gap-3">
              <div className="metric-icon bg-green-500">
                <GraduationCap className="h-5 w-5 md:h-6 md:w-6" />
              </div>
              <div>
                <p className="text-xs md:text-sm font-medium text-gray-600">Active Subjects</p>
                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.activeSubjects}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="stat-card">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center gap-3">
              <div className="metric-icon bg-purple-500">
                <Users className="h-5 w-5 md:h-6 md:w-6" />
              </div>
              <div>
                <p className="text-xs md:text-sm font-medium text-gray-600">Departments</p>
                <p className="text-lg md:text-2xl font-bold text-gray-900">{stats.departmentCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>


      </div>

      {/* Quick Actions */}
      <Card>
        <CardContent className="p-4 md:p-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Quick Actions</h3>
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => setIsAddDialogOpen(true)}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Subject
                </Button>
                <Button
                  onClick={() => {
                    setSearchTerm('');
                    setFilterDepartment('');
                  }}
                  variant="outline"
                  size="sm"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              Showing {filteredSubjects.length} of {subjects.length} subjects
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4 md:p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search subjects by name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={filterDepartment}
                onChange={(e) => setFilterDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">All Departments</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>

            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subjects Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg md:text-xl">Subjects ({filteredSubjects.length})</CardTitle>
          <CardDescription>
            Manage your academic subjects and their details
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-2 md:px-4 font-medium text-gray-500 text-xs md:text-sm">SUBJECT</th>
                  <th className="text-left py-3 px-2 md:px-4 font-medium text-gray-500 text-xs md:text-sm">DEPARTMENT</th>
                  <th className="text-left py-3 px-2 md:px-4 font-medium text-gray-500 text-xs md:text-sm hidden lg:table-cell">STATUS</th>
                  <th className="text-left py-3 px-2 md:px-4 font-medium text-gray-500 text-xs md:text-sm">ACTIONS</th>
                </tr>
              </thead>
              <tbody>
                {filteredSubjects.map((subject) => (
                  <tr key={subject.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 md:py-4 px-2 md:px-4">
                      <div>
                        <div className="font-medium text-sm md:text-base">{subject.name}</div>
                      </div>
                    </td>
                    <td className="py-3 md:py-4 px-2 md:px-4 text-sm md:text-base">
                      <Badge variant="outline" className="text-xs">
                        {subject.department}
                      </Badge>
                    </td>
                    <td className="py-3 md:py-4 px-2 md:px-4 hidden lg:table-cell">
                      <Badge
                        variant="secondary"
                        className={`text-xs ${subject.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                      >
                        {subject.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </td>
                    <td className="py-3 md:py-4 px-2 md:px-4">
                      <div className="flex gap-1 md:gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openViewDialog(subject)}
                          className="text-gray-600 hover:text-gray-800 p-1 md:p-2"
                        >
                          <Eye className="h-3 w-3 md:h-4 md:w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(subject)}
                          className="text-blue-600 hover:text-blue-800 p-1 md:p-2"
                        >
                          <Edit className="h-3 w-3 md:h-4 md:w-4" />
                        </Button>
                        {canDeleteSubjects && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-800 p-1 md:p-2"
                              >
                                <Trash2 className="h-3 w-3 md:h-4 md:w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Subject</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{subject.name}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteSubject(subject)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Add Subject Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Subject</DialogTitle>
            <DialogDescription>
              Create a new subject for your academic curriculum.
            </DialogDescription>
          </DialogHeader>
          <SubjectForm onSubmit={handleAddSubject} onCancel={() => setIsAddDialogOpen(false)} />
        </DialogContent>
      </Dialog>

      {/* Edit Subject Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Subject</DialogTitle>
            <DialogDescription>
              Update the subject information.
            </DialogDescription>
          </DialogHeader>
          {selectedSubject && (
            <SubjectForm
              subject={selectedSubject}
              onSubmit={handleEditSubject}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedSubject(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Subject Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Subject Details</DialogTitle>
          </DialogHeader>
          {selectedSubject && (
            <div className="space-y-4">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Subject Name</label>
                  <p className="text-gray-900">{selectedSubject.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Department</label>
                  <p className="text-gray-900">{selectedSubject.department}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Status</label>
                <Badge
                  variant="secondary"
                  className={selectedSubject.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                >
                  {selectedSubject.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
