
import React from "react";
import { <PERSON> } from "react-router-dom";
import { GraduationCap } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PublicHeader } from '@/components/PublicHeader';
import { PublicFooter } from '@/components/PublicFooter';

export const HelpCenter: React.FC = () => (
  <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex flex-col">
    <PublicHeader />

    {/* Main Content */}
    <main className="flex-1 flex flex-col items-center justify-center py-24 px-4">
      <div className="max-w-xl w-full text-center mb-8">
        <Badge className="mb-6 bg-green-100 text-green-800 border-green-200 inline-block">
          Help Center
        </Badge>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Help Center</h1>
        <p className="text-lg text-gray-600 mb-6">
          Find answers to your questions, troubleshooting guides, and more for VertiQ school management system.<br />
          If you can’t find what you need, feel free to <Link to="/contact" className="text-green-600 underline">contact our support team</Link>.
        </p>
        <Link to="/documentation">
          <Button className="bg-green-600 text-white hover:bg-green-700">Go to Documentation</Button>
        </Link>
      </div>
      <div className="max-w-2xl mx-auto w-full bg-white rounded-xl shadow p-8 mt-4">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">How can we help you?</h2>
        <ul className="list-disc text-left pl-5 text-gray-700 space-y-2">
          <li>Browse our <Link to="/documentation" className="text-green-700 underline">Documentation</Link> for step-by-step guides</li>
          <li>Visit the <Link to="/features" className="text-green-700 underline">Features</Link> page to learn what VertiQ can do</li>
          <li>Contact us directly for account support or technical issues</li>
        </ul>
      </div>
    </main>

    {/* Footer: same as homepage */}
    <footer className="bg-gray-900 text-white py-12 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="h-5 w-5 text-white" />
              </div>
              <span className="text-lg font-bold">VertiQ</span>
            </div>
            <p className="text-gray-400">
              School management made seamless with VertiQ school management system.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Product</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/features" className="hover:text-white transition-colors">Features</Link></li>
              <li><Link to="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
              <li><Link to="/documentation" className="hover:text-white transition-colors">Documentation</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Company</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/about" className="hover:text-white transition-colors">About</Link></li>
              <li><Link to="/privacy" className="hover:text-white transition-colors">Privacy</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Support</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/help-center" className="hover:text-white transition-colors">Help Center</Link></li>
              <li><Link to="/contact" className="hover:text-white transition-colors">Contact Us</Link></li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 VertiQ. All rights reserved.</p>
        </div>
    <PublicFooter />
  </div>
);

export default HelpCenter;

