import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { GraduationCap, Shield, Heart, Eye, EyeOff, LogIn } from 'lucide-react';
import { UserRole } from '@/types';
import { PublicHeader } from '@/components/PublicHeader';
import { PublicFooter } from '@/components/PublicFooter';

const demoUsers = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as UserRole,
    description: 'Full access to all features and system management'
  },
  {
    email: '<EMAIL>',
    password: 'teacher123',
    firstName: 'John',
    lastName: '<PERSON>',
    role: 'teacher' as UserRole,
    description: 'Access to assigned classes, students, and academic management'
  },
  {
    email: '<EMAIL>',
    password: 'parent123',
    firstName: 'Robert',
    lastName: 'Johnson',
    role: 'parent' as UserRole,
    description: 'Access to children\'s academic records, results, and fees'
  }
];

const getRoleIcon = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return <Shield className="h-5 w-5 text-red-500" />;
    case 'teacher':
      return <GraduationCap className="h-5 w-5 text-blue-500" />;
    case 'parent':
      return <Heart className="h-5 w-5 text-purple-500" />;
    default:
      return <Shield className="h-5 w-5 text-gray-500" />;
  }
};

const getRoleColor = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800';
    case 'teacher':
      return 'bg-blue-100 text-blue-800';
    case 'parent':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const NewLogin: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const success = await login(email, password);
      if (success) {
        toast({
          title: "Login Successful",
          description: "Welcome to School Management System!",
        });
        navigate('/dashboard');
      } else {
        toast({
          title: "Login Failed",
          description: "Invalid email or password. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred during login. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickLogin = async (userEmail: string, userPassword: string) => {
    setEmail(userEmail);
    setPassword(userPassword);
    setIsLoading(true);

    try {
      const success = await login(userEmail, userPassword);
      if (success) {
        const user = demoUsers.find(u => u.email === userEmail);
        toast({
          title: "Login Successful",
          description: `Welcome ${user?.firstName}! Logged in as ${user?.role}.`,
        });
        navigate('/dashboard');
      } else {
        toast({
          title: "Login Failed",
          description: "Authentication failed. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred during login. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      <PublicHeader />

      <div className="flex items-center justify-center py-12 px-4">
        <div className="w-full max-w-6xl space-y-8">
          {/* Header */}
          <div className="text-center">
            <Badge className="mb-6 bg-green-100 text-green-800 border-green-200">
              🔐 Secure Login Portal
            </Badge>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Welcome to
              <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent"> SchoolMS</span>
            </h1>
            <p className="text-lg text-gray-600 mb-8">Sign in to access your dashboard and manage your school efficiently</p>
          </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Login Form */}
          <Card className="w-full shadow-lg border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-xl">
                <LogIn className="h-6 w-6 text-green-600" />
                Sign In
              </CardTitle>
              <p className="text-sm text-gray-600">Enter your credentials to access your account</p>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-green-600 hover:bg-green-700"
                  disabled={isLoading}
                >
                  {isLoading ? "Signing in..." : "Sign In"}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Demo Users */}
          <Card className="w-full shadow-lg border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-xl">
                <Shield className="h-6 w-6 text-green-600" />
                Demo User Accounts
              </CardTitle>
              <p className="text-sm text-gray-600">
                Click on any user below to sign in instantly with real authentication
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {demoUsers.map((user) => (
                  <div
                    key={user.email}
                    className="p-4 border rounded-lg hover:border-green-300 hover:shadow-md transition-all cursor-pointer bg-white"
                    onClick={() => handleQuickLogin(user.email, user.password)}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      {getRoleIcon(user.role)}
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">
                          {user.firstName} {user.lastName}
                        </h3>
                        <Badge variant="secondary" className={`text-xs ${getRoleColor(user.role)}`}>
                          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">{user.description}</p>
                    <p className="text-xs text-gray-500">{user.email}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        </div>
      </div>

      <PublicFooter />
    </div>
  );
};
