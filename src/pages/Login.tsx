import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { GraduationCap, Shield, User, Heart, Eye, EyeOff } from 'lucide-react';
import { UserRole } from '@/types';
import { SupabaseTest } from '@/components/SupabaseTest';

const demoUsers = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as UserRole,
    description: 'Full access to all features and system management'
  },
  {
    email: '<EMAIL>',
    password: 'teacher123',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'teacher' as User<PERSON><PERSON>,
    description: 'Access to assigned classes, students, and academic management'
  },
  {
    email: '<EMAIL>',
    password: 'parent123',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'parent' as UserRole,
    description: 'Access to children\'s academic records, results, and fees'
  }
];

const getRoleIcon = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return <Shield className="h-5 w-5 text-red-500" />;
    case 'teacher':
      return <GraduationCap className="h-5 w-5 text-blue-500" />;
    case 'parent':
      return <Heart className="h-5 w-5 text-purple-500" />;
    default:
      return <User className="h-5 w-5 text-gray-500" />;
  }
};

const getRoleColor = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800';
    case 'teacher':
      return 'bg-blue-100 text-blue-800';
    case 'parent':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Add timeout to prevent infinite loading
      const loginPromise = login(email, password);
      const timeoutPromise = new Promise<boolean>((_, reject) =>
        setTimeout(() => reject(new Error('Login timeout')), 10000)
      );

      const success = await Promise.race([loginPromise, timeoutPromise]);

      if (success) {
        toast({
          title: "Login Successful",
          description: "Welcome to School Management System!",
        });
        navigate('/dashboard');
      } else {
        toast({
          title: "Login Failed",
          description: "Invalid email or password. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message === 'Login timeout'
          ? "Login is taking too long. Please try again."
          : "An error occurred during login. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickLogin = async (userEmail: string, userPassword: string) => {
    setEmail(userEmail);
    setPassword(userPassword);
    setIsLoading(true);

    try {
      // Add timeout for quick login too
      const loginPromise = login(userEmail, userPassword);
      const timeoutPromise = new Promise<boolean>((_, reject) =>
        setTimeout(() => reject(new Error('Login timeout')), 10000)
      );

      const success = await Promise.race([loginPromise, timeoutPromise]);

      if (success) {
        const user = demoUsers.find(u => u.email === userEmail);
        toast({
          title: "Login Successful",
          description: `Welcome ${user?.firstName}! Logged in as ${user?.role}.`,
        });
        navigate('/dashboard');
      } else {
        toast({
          title: "Login Failed",
          description: "Authentication failed. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message === 'Login timeout'
          ? "Login is taking too long. Please try again."
          : "An error occurred during login. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Login Form */}
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 rounded-full" style={{ backgroundColor: '#007100' }}>
                <GraduationCap className="h-8 w-8 text-white" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold">School Management System</CardTitle>
            <p className="text-gray-600">Sign in to your account</p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                  disabled={isLoading}
                />
              </div>
              
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                style={{ backgroundColor: '#007100' }}
                disabled={isLoading}
              >
                {isLoading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Demo Users */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-6 w-6" />
              Demo User Accounts
            </CardTitle>
            <p className="text-sm text-gray-600">
              Click on any user below to sign in with real Supabase authentication
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {demoUsers.map((user) => (
                <div
                  key={user.email}
                  className="p-4 border rounded-lg hover:border-gray-300 transition-colors cursor-pointer"
                  onClick={() => handleQuickLogin(user.email, user.password)}
                >
                  <div className="flex items-center gap-3 mb-3">
                    {getRoleIcon(user.role)}
                    <div>
                      <h3 className="font-medium text-sm">
                        {user.firstName} {user.lastName}
                      </h3>
                      <Badge variant="secondary" className={`text-xs ${getRoleColor(user.role)}`}>
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="text-xs text-gray-600 mb-3">
                    {user.description}
                  </p>
                  
                  <div className="space-y-1 text-xs text-gray-500">
                    <div><strong>Email:</strong> {user.email}</div>
                    <div><strong>Password:</strong> {user.password}</div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">How to Test:</h4>
              <ol className="list-decimal list-inside text-sm text-blue-700 space-y-1">
                <li>Click on any user card above for quick login</li>
                <li>Or manually enter email/password in the form</li>
                <li>Navigate through different pages to see role-based access</li>
                <li>Visit the "Role Demo" page to see detailed permissions</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Supabase Test Component */}
      <SupabaseTest />
    </div>
  );
};
