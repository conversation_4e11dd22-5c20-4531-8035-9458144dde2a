import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { Plus, Loader2, Clock, MapPin, User, BookOpen } from "lucide-react";
import { supabase } from '@/integrations/supabase/client';
import { getCurrentSchoolId } from "@/services/supabaseService";

const days = [
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' }
];

export const SimpleTimetable = () => {
  const { toast } = useToast();
  const [timetable, setTimetable] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [schoolId, setSchoolId] = useState<string>('');
  const [selectedClass, setSelectedClass] = useState<string>('');

  // Form state
  const [formClass, setFormClass] = useState('');
  const [formSubject, setFormSubject] = useState('');
  const [formTeacher, setFormTeacher] = useState('');
  const [formDay, setFormDay] = useState('');
  const [formStartTime, setFormStartTime] = useState('');
  const [formEndTime, setFormEndTime] = useState('');
  const [formRoom, setFormRoom] = useState('');

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      const schoolSlug = window.location.pathname.split('/')[1];
      const id = await getCurrentSchoolId(schoolSlug);
      if (!id) {
        toast({
          title: "Error",
          description: "School not found",
          variant: "destructive",
        });
        return;
      }
      setSchoolId(id);
      await loadData(id);
    } catch (error) {
      console.error('Error initializing:', error);
      toast({
        title: "Error",
        description: "Failed to initialize page",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadData = async (schoolId: string) => {
    try {
      // Load all data in parallel
      const [timetableData, classesData, subjectsData, teachersData] = await Promise.all([
        supabase.from('timetable').select(`
          *,
          class:classes(id, name, section),
          subject:subjects(id, name),
          teacher:teachers(id, first_name, last_name)
        `).eq('school_id', schoolId).eq('is_active', true),
        supabase.from('classes').select('*').eq('school_id', schoolId).eq('is_active', true),
        supabase.from('subjects').select('*').eq('school_id', schoolId).eq('is_active', true),
        supabase.from('teachers').select('*').eq('school_id', schoolId).eq('is_active', true)
      ]);

      setTimetable(timetableData.data || []);
      setClasses(classesData.data || []);
      setSubjects(subjectsData.data || []);
      setTeachers(teachersData.data || []);

      // Set first class as default
      if (classesData.data && classesData.data.length > 0) {
        setSelectedClass(classesData.data[0].id);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const filteredTimetable = selectedClass 
    ? timetable.filter(item => item.class_id === selectedClass)
    : timetable;

  const handleSubmit = async () => {
    if (!formClass || !formSubject || !formTeacher || !formDay || !formStartTime || !formEndTime) {
      toast({
        title: "Error",
        description: "Please fill all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const { data, error } = await supabase
        .from('timetable')
        .insert({
          school_id: schoolId,
          class_id: formClass,
          subject_id: formSubject,
          teacher_id: formTeacher,
          day_of_week: parseInt(formDay),
          start_time: formStartTime,
          end_time: formEndTime,
          room_number: formRoom || null,
          academic_year: '2024-25',
          is_active: true
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Timetable entry added successfully",
      });

      // Reset form and reload
      setFormClass('');
      setFormSubject('');
      setFormTeacher('');
      setFormDay('');
      setFormStartTime('');
      setFormEndTime('');
      setFormRoom('');
      setIsAddDialogOpen(false);
      await loadData(schoolId);
    } catch (error: any) {
      console.error('Error adding timetable entry:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add timetable entry",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDayName = (dayNumber: number) => {
    const day = days.find(d => d.value === dayNumber);
    return day ? day.label : 'Unknown';
  };

  const groupedTimetable = filteredTimetable.reduce((acc, item) => {
    const day = getDayName(item.day_of_week);
    if (!acc[day]) acc[day] = [];
    acc[day].push(item);
    return acc;
  }, {} as Record<string, any[]>);

  // Sort periods by start time within each day
  Object.keys(groupedTimetable).forEach(day => {
    groupedTimetable[day].sort((a, b) => a.start_time.localeCompare(b.start_time));
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Timetable</h1>
          <p className="text-gray-600">Manage class schedules and periods</p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Period
        </Button>
      </div>

      {/* Class Filter */}
      <div className="mb-6">
        <Label>Select Class</Label>
        <Select value={selectedClass} onValueChange={setSelectedClass}>
          <SelectTrigger className="w-64">
            <SelectValue placeholder="Select class" />
          </SelectTrigger>
          <SelectContent>
            {classes.map(cls => (
              <SelectItem key={cls.id} value={cls.id}>
                {cls.name} {cls.section}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Timetable Display */}
      <div className="grid gap-6">
        {days.map(day => (
          <Card key={day.value}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {day.label}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {groupedTimetable[day.label] && groupedTimetable[day.label].length > 0 ? (
                <div className="space-y-3">
                  {groupedTimetable[day.label].map((period) => (
                    <div key={period.id} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex justify-between items-start">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">{period.subject?.name || 'Unknown Subject'}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <User className="h-4 w-4" />
                            <span>{period.teacher?.first_name} {period.teacher?.last_name}</span>
                          </div>
                          {period.room_number && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <MapPin className="h-4 w-4" />
                              <span>{period.room_number}</span>
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{period.start_time} - {period.end_time}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-gray-500 py-4">No periods scheduled</p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
