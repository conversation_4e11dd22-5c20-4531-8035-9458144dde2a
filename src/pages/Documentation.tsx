
import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>raduationCap, FileText, LifeBuoy, ShieldCheck, Home, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

const documentationSections = [
  {
    icon: <BookOpen className="h-6 w-6 text-green-600 mb-2" />,
    title: "Introduction",
    content: (
      <p>
        Welcome to the VertiQ school management system documentation! Here you’ll find guides and resources for using VertiQ, managing your school data, and taking advantage of all features. Whether you're an admin, teacher, or student, everything you need to get started is here.
      </p>
    ),
  },
  {
    icon: <FileText className="h-6 w-6 text-green-600 mb-2" />,
    title: "Getting Started",
    content: (
      <ul className="list-disc pl-5">
        <li>Sign up and create your VertiQ account.</li>
        <li>Set up your institution profile and invite staff.</li>
        <li>Manage students, teachers, and classes easily from the dashboard.</li>
      </ul>
    ),
  },
  {
    icon: <ShieldCheck className="h-6 w-6 text-green-600 mb-2" />,
    title: "Features Overview",
    content: (
      <ul className="list-disc pl-5">
        <li>Attendance tracking with real-time reporting</li>
        <li>Gradebook and results management</li>
        <li>Fee collection and tracking</li>
        <li>Communication tools for teachers, students, and parents</li>
        <li>Customizable timetables and scheduling</li>
      </ul>
    ),
  },
  {
    icon: <LifeBuoy className="h-6 w-6 text-green-600 mb-2" />,
    title: "Need Further Help?",
    content: (
      <p>
        Visit our <Link to="/help-center" className="text-green-700 underline">Help Center</Link> or <Link to="/contact" className="text-green-700 underline">contact us</Link> for personalized support and troubleshooting.
      </p>
    ),
  },
];

export const Documentation: React.FC = () => (
  <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex flex-col">
    {/* Header */}
    <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center">
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">VertiQ</span>
          </Link>
          <div className="hidden md:flex items-center gap-8">
            <Link to="/" className="text-gray-600 hover:text-green-600 transition-colors flex items-center gap-1">
              <Home className="w-4 h-4" /> Home
            </Link>
            <Link to="/features" className="text-gray-600 hover:text-green-600 transition-colors">Features</Link>
            <Link to="/pricing" className="text-gray-600 hover:text-green-600 transition-colors">Pricing</Link>
            <Link to="/documentation" className="text-green-600 font-medium">Documentation</Link>
            <Link to="/help-center" className="text-gray-600 hover:text-green-600 transition-colors">Help Center</Link>
            <Link to="/login">
              <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </nav>

    {/* Main Content */}
    <main className="flex-1 flex flex-col items-center justify-center py-16 px-4">
      <div className="max-w-2xl w-full text-center mb-12">
        <Badge className="mb-6 bg-green-100 text-green-800 border-green-200 inline-block">
          <FileText className="inline-block mr-2" /> Docs
        </Badge>
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">VertiQ Documentation</h1>
        <p className="text-lg text-gray-600 mb-4">
          Getting the most out of VertiQ school management system.
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-4xl w-full">
        {documentationSections.map((section, idx) => (
          <div key={section.title} className="bg-white rounded-xl shadow-lg p-6 flex flex-col items-start">
            {section.icon}
            <h2 className="text-xl font-bold text-gray-900 mb-2">{section.title}</h2>
            <div className="text-gray-700 text-left">{section.content}</div>
          </div>
        ))}
      </div>
    </main>

    {/* Footer */}
    <footer className="bg-gray-900 text-white py-12 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="h-5 w-5 text-white" />
              </div>
              <span className="text-lg font-bold">VertiQ</span>
            </div>
            <p className="text-gray-400">
              School management made seamless with VertiQ school management system.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Product</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/features" className="hover:text-white transition-colors">Features</Link></li>
              <li><Link to="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
              <li><Link to="/documentation" className="hover:text-white transition-colors">Documentation</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Company</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/about" className="hover:text-white transition-colors">About</Link></li>
              <li><Link to="/privacy" className="hover:text-white transition-colors">Privacy</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Support</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/help-center" className="hover:text-white transition-colors">Help Center</Link></li>
              <li><Link to="/contact" className="hover:text-white transition-colors">Contact Us</Link></li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 VertiQ. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
);

export default Documentation;
