import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { MetricCard } from "@/components/MetricCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  User,
  Book,
  Calendar,
  FileText,
  Bell,
  Plus,
  DollarSign,
  UserCheck,
  BookOpen,
  Clock,
  AlertTriangle,
  Building,
  MessageCircle,
  CreditCard,
  Award,
  Target,
  CheckCircle,
  TrendingUp,
  Eye,
  Download,
  CalendarDays,
  GraduationCap,
  ClipboardList
} from "lucide-react";

export const SimpleDashboard: React.FC = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const { user } = useSimpleAuth();
  const [isQuickAddOpen, setIsQuickAddOpen] = useState(false);

  // Get user role from auth metadata
  const userRole = user?.user_metadata?.role || 'admin';
  const userName = user?.user_metadata?.first_name || user?.email?.split('@')[0] || 'User';

  console.log('🏠 SimpleDashboard - User:', user);
  console.log('🏠 SimpleDashboard - User Role:', userRole);
  console.log('🏠 SimpleDashboard - User Name:', userName);
  console.log('🏠 SimpleDashboard - User Metadata:', user?.user_metadata);
  console.log('🏠 SimpleDashboard - Will show dashboard for role:', userRole);

  const handleQuickAction = (actionId: string) => {
    console.log('Quick action:', actionId);
    setIsQuickAddOpen(false);
  };

  // Student Portal - Comprehensive Dashboard
  if (userRole === 'student') {
    return (
      <div className="space-y-6 md:space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
              Welcome back, {userName}!
            </h1>
            <p className="text-gray-600 mt-1">
              Let's continue your learning journey today.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className="bg-blue-100 text-blue-800">Student</Badge>
            <Button size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View Profile
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          <MetricCard
            title="Overall Grade"
            value="A-"
            change="↑ 0.2 points this month"
            changeType="increase"
            icon={<Award className="h-6 w-6" />}
            iconBg="bg-yellow-500"
          />
          <MetricCard
            title="Attendance"
            value="96.5%"
            change="Excellent attendance!"
            changeType="increase"
            icon={<CheckCircle className="h-6 w-6" />}
            iconBg="bg-green-500"
          />
          <MetricCard
            title="Assignments Due"
            value="4"
            change="2 due this week"
            changeType="neutral"
            icon={<FileText className="h-6 w-6" />}
            iconBg="bg-blue-500"
          />
          <MetricCard
            title="Next Exam"
            value="3 days"
            change="Physics - Chapter 5"
            changeType="neutral"
            icon={<Clock className="h-6 w-6" />}
            iconBg="bg-red-500"
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Today's Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarDays className="h-5 w-5" />
                Today's Classes
              </CardTitle>
              <CardDescription>Your schedule for today</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    9:00
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Mathematics</p>
                    <p className="text-sm text-gray-600">Room 201 • Mr. Johnson</p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Completed
                  </Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    11:00
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Physics</p>
                    <p className="text-sm text-gray-600">Lab 1 • Dr. Smith</p>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    Current
                  </Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    2:00
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Chemistry</p>
                    <p className="text-sm text-gray-600">Room 105 • Ms. Davis</p>
                  </div>
                  <Badge variant="outline">Upcoming</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Assignments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Recent Assignments
              </CardTitle>
              <CardDescription>Your latest assignments and submissions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <div className="flex-1">
                    <p className="font-medium text-red-800">Math Homework #12</p>
                    <p className="text-sm text-red-600">Due: Tomorrow</p>
                  </div>
                  <Badge variant="destructive">Overdue</Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <Clock className="h-5 w-5 text-yellow-500" />
                  <div className="flex-1">
                    <p className="font-medium text-yellow-800">Physics Lab Report</p>
                    <p className="text-sm text-yellow-600">Due: Friday</p>
                  </div>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    Pending
                  </Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <div className="flex-1">
                    <p className="font-medium text-green-800">English Essay</p>
                    <p className="text-sm text-green-600">Submitted: Yesterday</p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Submitted
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Student Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Academic Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Academic Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Mathematics</span>
                    <span>92%</span>
                  </div>
                  <Progress value={92} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Physics</span>
                    <span>88%</span>
                  </div>
                  <Progress value={88} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Chemistry</span>
                    <span>85%</span>
                  </div>
                  <Progress value={85} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Fee Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Fee Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Tuition Fee</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Paid
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Library Fee</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Paid
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Lab Fee</span>
                  <Badge variant="destructive">Pending</Badge>
                </div>
                <Button size="sm" className="w-full mt-3">
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Exams */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5" />
                Upcoming Exams
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="font-medium text-blue-800">Physics Mid-term</p>
                  <p className="text-sm text-blue-600">March 15, 2024</p>
                  <p className="text-xs text-blue-500">Chapters 1-5</p>
                </div>
                <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <p className="font-medium text-purple-800">Math Quiz</p>
                  <p className="text-sm text-purple-600">March 18, 2024</p>
                  <p className="text-xs text-purple-500">Algebra & Geometry</p>
                </div>
                <Button size="sm" className="w-full">
                  <Calendar className="h-4 w-4 mr-2" />
                  View All Exams
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Teacher Portal - Comprehensive Dashboard
  if (userRole === 'teacher') {
    return (
      <div className="space-y-6 md:space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
              Welcome back, {userName}!
            </h1>
            <p className="text-gray-600 mt-1">
              Manage your classes and students effectively.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className="bg-green-100 text-green-800">Teacher</Badge>
            <Button size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View Profile
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          <MetricCard
            title="My Classes"
            value="5"
            change="3 active today"
            changeType="neutral"
            icon={<BookOpen className="h-6 w-6" />}
            iconBg="bg-blue-500"
          />
          <MetricCard
            title="My Students"
            value="120"
            change="Across all classes"
            changeType="neutral"
            icon={<Users className="h-6 w-6" />}
            iconBg="bg-green-500"
          />
          <MetricCard
            title="Pending Grades"
            value="8"
            change="Assignments to grade"
            changeType="neutral"
            icon={<FileText className="h-6 w-6" />}
            iconBg="bg-orange-500"
          />
          <MetricCard
            title="Today's Classes"
            value="4"
            change="2 completed, 2 upcoming"
            changeType="neutral"
            icon={<Clock className="h-6 w-6" />}
            iconBg="bg-purple-500"
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* My Classes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                My Classes
              </CardTitle>
              <CardDescription>Classes you are assigned to teach</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    10A
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Grade 10 - Section A</p>
                    <p className="text-sm text-gray-600">Mathematics • 30 students</p>
                  </div>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    9B
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Grade 9 - Section B</p>
                    <p className="text-sm text-gray-600">Mathematics • 28 students</p>
                  </div>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
                <div className="flex items-center gap-3 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    8A
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Grade 8 - Section A</p>
                    <p className="text-sm text-gray-600">Mathematics • 25 students</p>
                  </div>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Today's Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarDays className="h-5 w-5" />
                Today's Schedule
              </CardTitle>
              <CardDescription>Your teaching schedule for today</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    9:00
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Grade 10A - Mathematics</p>
                    <p className="text-sm text-gray-600">Room 201 • Algebra</p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Completed
                  </Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    11:00
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Grade 9B - Mathematics</p>
                    <p className="text-sm text-gray-600">Room 105 • Geometry</p>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    Current
                  </Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    2:00
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Grade 8A - Mathematics</p>
                    <p className="text-sm text-gray-600">Room 203 • Fractions</p>
                  </div>
                  <Badge variant="outline">Upcoming</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Teacher Management Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Assignments Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Assignment Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Pending Reviews</span>
                  <Badge variant="destructive">8</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Active Assignments</span>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    12
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Completed</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    45
                  </Badge>
                </div>
                <Button size="sm" className="w-full mt-3">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Assignment
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Examination Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5" />
                Examination Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="font-medium text-blue-800">Mid-term Exams</p>
                  <p className="text-sm text-blue-600">March 15-20, 2024</p>
                  <p className="text-xs text-blue-500">3 classes scheduled</p>
                </div>
                <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <p className="font-medium text-purple-800">Unit Test</p>
                  <p className="text-sm text-purple-600">March 25, 2024</p>
                  <p className="text-xs text-purple-500">Grade 10A only</p>
                </div>
                <Button size="sm" className="w-full">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Exam
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button size="sm" className="w-full justify-start" variant="outline">
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Take Attendance
                </Button>
                <Button size="sm" className="w-full justify-start" variant="outline">
                  <FileText className="h-4 w-4 mr-2" />
                  Grade Assignments
                </Button>
                <Button size="sm" className="w-full justify-start" variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  View Timetable
                </Button>
                <Button size="sm" className="w-full justify-start" variant="outline">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Send Message
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Admin Dashboard (default)
  return (
    <div className="space-y-6 md:space-y-8">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
            Welcome back, {userName}!
          </h1>
          <p className="text-gray-600 mt-1 text-sm md:text-base">
            Here's what's happening at your school today.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
            Admin
          </span>
        </div>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
        <MetricCard
          title="Total Students"
          value="1,247"
          change="↑ 8% from last month"
          changeType="increase"
          icon={<User className="h-6 w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Total Teachers"
          value="68"
          change="2 new this month"
          changeType="increase"
          icon={<Users className="h-6 w-6" />}
          iconBg="bg-green-500"
        />
      </div>
    </div>
  );
};
