import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { supabase } from '@/integrations/supabase/client';
import { getCurrentSchoolId } from "@/services/supabaseService";

export const DatabaseTest = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const runTests = async () => {
    setIsLoading(true);
    const results: any[] = [];

    try {
      // Test 1: Check Supabase connection
      results.push({
        test: "Supabase Connection",
        status: "running",
        message: "Testing connection..."
      });

      const { data: connectionTest, error: connectionError } = await supabase
        .from('schools')
        .select('count')
        .limit(1);

      if (connectionError) {
        results[0] = {
          test: "Supabase Connection",
          status: "failed",
          message: `Connection failed: ${connectionError.message}`
        };
      } else {
        results[0] = {
          test: "Supabase Connection",
          status: "passed",
          message: "Successfully connected to Supabase"
        };
      }

      // Test 2: Check if school2 exists
      results.push({
        test: "School2 Exists",
        status: "running",
        message: "Checking for school2..."
      });

      const schoolId = await getCurrentSchoolId('school2');
      if (schoolId) {
        results[1] = {
          test: "School2 Exists",
          status: "passed",
          message: `Found school2 with ID: ${schoolId}`
        };
      } else {
        results[1] = {
          test: "School2 Exists",
          status: "failed",
          message: "School2 not found in database"
        };
      }

      // Test 3: Check RLS policies
      results.push({
        test: "RLS Policies",
        status: "running",
        message: "Testing RLS policies..."
      });

      if (schoolId) {
        try {
          const { data: classTest, error: classError } = await supabase
            .from('classes')
            .insert({
              school_id: schoolId,
              name: 'Test Class',
              grade_level: '1',
              section: 'TEST',
              academic_year: '2024-25'
            })
            .select()
            .single();

          if (classError) {
            if (classError.code === '42501') {
              results[2] = {
                test: "RLS Policies",
                status: "failed",
                message: "RLS policies are blocking data creation"
              };
            } else {
              results[2] = {
                test: "RLS Policies",
                status: "failed",
                message: `Database error: ${classError.message}`
              };
            }
          } else {
            // Clean up test data
            await supabase.from('classes').delete().eq('id', classTest.id);
            results[2] = {
              test: "RLS Policies",
              status: "passed",
              message: "RLS policies allow data creation"
            };
          }
        } catch (error: any) {
          results[2] = {
            test: "RLS Policies",
            status: "failed",
            message: `Error testing RLS: ${error.message}`
          };
        }
      } else {
        results[2] = {
          test: "RLS Policies",
          status: "skipped",
          message: "Skipped - school2 not found"
        };
      }

      // Test 4: Check all services
      results.push({
        test: "Service Integration",
        status: "running",
        message: "Testing all services..."
      });

      if (schoolId) {
        try {
          // Import services
          const {
            classesService,
            studentsService,
            subjectsService,
            examinationsService,
            resultsService,
            teachersService
          } = await import('@/services/supabaseService');

          // Test each service
          const serviceTests = await Promise.allSettled([
            classesService.getAll(schoolId),
            studentsService.getAll(schoolId),
            subjectsService.getAll(schoolId),
            examinationsService.getAll(schoolId),
            resultsService.getAll(schoolId),
            teachersService.getAll(schoolId)
          ]);

          const serviceNames = ['Classes', 'Students', 'Subjects', 'Examinations', 'Results', 'Teachers'];
          const failedServices = serviceTests
            .map((result, index) => ({ name: serviceNames[index], result }))
            .filter(({ result }) => result.status === 'rejected');

          if (failedServices.length === 0) {
            results[3] = {
              test: "Service Integration",
              status: "passed",
              message: `All ${serviceNames.length} services working correctly`
            };
          } else {
            results[3] = {
              test: "Service Integration",
              status: "failed",
              message: `Failed services: ${failedServices.map(s => s.name).join(', ')}`
            };
          }
        } catch (error: any) {
          results[3] = {
            test: "Service Integration",
            status: "failed",
            message: `Service test error: ${error.message}`
          };
        }
      } else {
        results[3] = {
          test: "Service Integration",
          status: "skipped",
          message: "Skipped - school2 not found"
        };
      }

    } catch (error: any) {
      results.push({
        test: "General Error",
        status: "failed",
        message: `Unexpected error: ${error.message}`
      });
    }

    setTestResults(results);
    setIsLoading(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'running': return 'bg-yellow-100 text-yellow-800';
      case 'skipped': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Database Connection Test</CardTitle>
          <CardDescription>
            Test the Supabase database connection and configuration for school2
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button onClick={runTests} disabled={isLoading}>
              {isLoading ? 'Running Tests...' : 'Run Database Tests'}
            </Button>

            {testResults.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-lg font-medium">Test Results:</h3>
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{result.test}</div>
                      <div className="text-sm text-gray-600">{result.message}</div>
                    </div>
                    <Badge className={getStatusColor(result.status)}>
                      {result.status}
                    </Badge>
                  </div>
                ))}
              </div>
            )}

            {testResults.length > 0 && (
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Next Steps:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• If school2 doesn't exist, run the setup-school2.sql script in Supabase</li>
                  <li>• If RLS policies are blocking, temporarily disable them for development</li>
                  <li>• Check the Supabase dashboard for any configuration issues</li>
                  <li>• Ensure your Supabase project URL and anon key are correct</li>
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
