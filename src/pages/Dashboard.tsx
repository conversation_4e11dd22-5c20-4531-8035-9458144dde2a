
import React from 'react';
import { useMultiTenantAuth } from '@/contexts/MultiTenantAuthContext';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { AdminDashboard } from './dashboards/AdminDashboard';
import { TeacherDashboard } from './dashboards/TeacherDashboard';
import { StudentDashboard } from './dashboards/StudentDashboard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  GraduationCap,
  User,
  Heart,

  BookOpen,
  Calendar,
  FileText,
  DollarSign,
  BarChart3,
  Clock
} from 'lucide-react';

export const Dashboard: React.FC = () => {
  // Try to use MultiTenantAuth first, fallback to SimpleAuth
  let user: any, profile: any;
  try {
    const multiTenantAuth = useMultiTenantAuth();
    user = multiTenantAuth.user;
    profile = multiTenantAuth.profile;
  } catch {
    // Fallback to SimpleAuth
    const simpleAuth = useSimpleAuth();
    user = simpleAuth.user;
    // Create a profile-like object from SimpleAuth user
    profile = user ? {
      id: user.id,
      email: user.email,
      role: user.user_metadata?.role || 'admin'
    } : null;
  }

  console.log('🏠 Dashboard component rendered');
  console.log('👤 Profile in Dashboard:', profile);

  // Role-specific dashboard content
  const renderRoleSpecificDashboard = () => {
    switch (profile?.role) {
      case 'admin':
        return <AdminDashboard />;

      case 'teacher':
        return <TeacherDashboard />;

      case 'student':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Teacher Dashboard</h1>
                <p className="text-gray-600 mt-1">Welcome back, {profile?.first_name}!</p>
              </div>
              <Badge className="bg-blue-100 text-blue-800">
                <GraduationCap className="h-4 w-4 mr-1" />
                Teacher
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">My Classes</CardTitle>
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Ready</div>
                  <p className="text-xs text-muted-foreground">Classes management</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Students</CardTitle>
                  <User className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Active</div>
                  <p className="text-xs text-muted-foreground">Student management</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Teaching</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Online</div>
                  <p className="text-xs text-muted-foreground">System ready</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Today's Classes</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Ready</div>
                  <p className="text-xs text-muted-foreground">Schedule management</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Teacher Tools</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm text-gray-600">• Access student management system</p>
                  <p className="text-sm text-gray-600">• View class assignments and schedules</p>
                  <p className="text-sm text-gray-600">• Manage teaching resources</p>
                  <p className="text-sm text-gray-600">• Track student progress</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm text-gray-600">• Teacher dashboard is active</p>
                  <p className="text-sm text-gray-600">• Student data synchronized</p>
                  <p className="text-sm text-gray-600">• All systems operational</p>
                </CardContent>
              </Card>
            </div>
          </div>
        );



      case 'parent':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Parent Dashboard</h1>
                <p className="text-gray-600 mt-1">Welcome back, {profile?.first_name}!</p>
              </div>
              <Badge className="bg-purple-100 text-purple-800">
                <Heart className="h-4 w-4 mr-1" />
                Parent
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">My Children</CardTitle>
                  <User className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Active</div>
                  <p className="text-xs text-muted-foreground">Children management</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">School Updates</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Current</div>
                  <p className="text-xs text-muted-foreground">Stay informed</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Fees</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$450</div>
                  <p className="text-xs text-muted-foreground">Due next week</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Grades</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">A-</div>
                  <p className="text-xs text-muted-foreground">Current semester</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Children's Upcoming Events</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm text-gray-600">• Parent-teacher meeting - Friday 3:00 PM</p>
                  <p className="text-sm text-gray-600">• Alice's Math exam - Next Tuesday</p>
                  <p className="text-sm text-gray-600">• John's Science project due - Thursday</p>
                  <p className="text-sm text-gray-600">• School sports day - Next Friday</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Updates</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm text-gray-600">• Alice: Excellent performance in Science project</p>
                  <p className="text-sm text-gray-600">• John: Improved attendance this month</p>
                  <p className="text-sm text-gray-600">• Monthly fee payment reminder</p>
                  <p className="text-sm text-gray-600">• New assignment notifications</p>
                </CardContent>
              </Card>
            </div>
          </div>
        );

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Student Dashboard</h1>
                <p className="text-gray-600 mt-1">Welcome back, {profile?.first_name}!</p>
              </div>
              <Badge className="bg-green-100 text-green-800">
                <GraduationCap className="h-4 w-4 mr-1" />
                Student
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">My Classes</CardTitle>
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Active</div>
                  <p className="text-xs text-muted-foreground">Class schedule</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Assignments</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Current</div>
                  <p className="text-xs text-muted-foreground">View assignments</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Grades</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Available</div>
                  <p className="text-xs text-muted-foreground">Academic progress</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Attendance</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Tracked</div>
                  <p className="text-xs text-muted-foreground">Attendance record</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Student Resources</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm text-gray-600">• Access learning materials</p>
                  <p className="text-sm text-gray-600">• View class schedules and timetables</p>
                  <p className="text-sm text-gray-600">• Check assignment deadlines</p>
                  <p className="text-sm text-gray-600">• Review academic progress</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Academic Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm text-gray-600">• Student portal is active</p>
                  <p className="text-sm text-gray-600">• Academic records up to date</p>
                  <p className="text-sm text-gray-600">• All systems operational</p>
                </CardContent>
              </Card>
            </div>
          </div>
        );

      default:
        return <AdminDashboard />;
    }
  };

  return renderRoleSpecificDashboard();
};
