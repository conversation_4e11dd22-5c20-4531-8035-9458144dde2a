import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { User, Session } from '@supabase/supabase-js';
import { MultiTenantService, SchoolProfile } from '@/services/multiTenantService';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: SchoolProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const SimpleAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<SchoolProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    console.log('🔄 SimpleAuth: Getting initial session...');
    supabase.auth.getSession().then(async ({ data: { session } }) => {
      try {
        console.log('🔄 SimpleAuth: Initial session:', session?.user?.id || 'No user');
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          console.log('🔄 SimpleAuth: Loading profile for user:', session.user.id);
          // Load user profile from database with timeout
          try {
            const profilePromise = MultiTenantService.getCurrentUserProfile();
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Profile loading timeout')), 10000)
            );

            const userProfile = await Promise.race([profilePromise, timeoutPromise]);
            console.log('🔄 SimpleAuth: Profile loaded:', userProfile?.role || 'No profile');
            setProfile(userProfile);

            if (!userProfile) {
              console.warn('⚠️ SimpleAuth: No profile found for user:', session.user.id);
            }
          } catch (profileError) {
            console.error('❌ SimpleAuth: Profile loading failed:', profileError);
            setProfile(null);
          }
        }
      } catch (error) {
        console.error('❌ SimpleAuth: Error loading initial session:', error);
      } finally {
        console.log('✅ SimpleAuth: Initial loading complete');
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        try {
          console.log('🔄 SimpleAuth: Auth state change:', event, session?.user?.id || 'No user');
          setSession(session);
          setUser(session?.user ?? null);

          if (session?.user) {
            console.log('🔄 SimpleAuth: Loading profile for user:', session.user.id);
            // Load user profile from database with timeout
            try {
              const profilePromise = MultiTenantService.getCurrentUserProfile();
              const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Profile loading timeout')), 10000)
              );

              const userProfile = await Promise.race([profilePromise, timeoutPromise]);
              console.log('🔄 SimpleAuth: Profile loaded:', userProfile?.role || 'No profile');
              setProfile(userProfile);

              if (!userProfile) {
                console.warn('⚠️ SimpleAuth: No profile found for user:', session.user.id);
              }
            } catch (profileError) {
              console.error('❌ SimpleAuth: Profile loading failed:', profileError);
              setProfile(null);
            }
          } else {
            setProfile(null);
          }
        } catch (error) {
          console.error('❌ SimpleAuth: Error in auth state change:', error);
        } finally {
          console.log('✅ SimpleAuth: Auth state change complete');
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      console.log('🔐 SimpleAuth: Starting signIn for:', email);
      // Real Supabase authentication
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('❌ SimpleAuth: SignIn error:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ SimpleAuth: SignIn successful for user:', data.user?.id);
      return { success: true };
    } catch (error: any) {
      console.error('❌ SimpleAuth: SignIn exception:', error.message);
      return { success: false, error: error.message };
    }
  };

  const signOut = async () => {
    setLoading(true);
    await supabase.auth.signOut();
    setProfile(null);
    setLoading(false);
  };

  return (
    <AuthContext.Provider value={{
      user,
      session,
      profile,
      loading,
      signIn,
      signOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useSimpleAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider');
  }
  return context;
};
