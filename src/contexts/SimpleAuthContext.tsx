import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { User, Session } from '@supabase/supabase-js';
import { MultiTenantService, SchoolProfile } from '@/services/multiTenantService';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: SchoolProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const SimpleAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<SchoolProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(async ({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        // Load user profile from database
        const userProfile = await MultiTenantService.getCurrentUserProfile();
        setProfile(userProfile);
      }

      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Load user profile from database
          const userProfile = await MultiTenantService.getCurrentUserProfile();
          setProfile(userProfile);
        } else {
          setProfile(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);


      // TEMPORARY: Mock authentication for testing
      const useMockAuth = false;

      if (useMockAuth) {


        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Determine user role based on email pattern
        let userRole = 'student'; // default to student for testing
        let firstName = email.split('@')[0];

        if (email.includes('admin')) {
          userRole = 'admin';
          firstName = 'Admin';
        } else if (email.includes('teacher')) {
          userRole = 'teacher';
          firstName = firstName.replace('teacher', 'Teacher ');
        } else if (email.includes('student') || email.includes('parent')) {
          userRole = 'student';
          firstName = firstName.replace('student', 'Student ');
        } else {
          // Default case - treat as student for testing
          userRole = 'student';
          firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);
        }



        // Mock user object with correct role and school context
        const mockUser = {
          id: `mock-${userRole}-${Date.now()}`,
          email: email,
          user_metadata: {
            firstName: firstName,
            role: userRole,
            school_id: 'ca245138-c35d-496c-ba6f-8cdbf326b457' // Use the known school2 ID
          }
        } as any;

        setUser(mockUser);
        setSession({ user: mockUser } as any);


        return { success: true };
      }

      // Real Supabase authentication
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {

        return { success: false, error: error.message };
      }


      return { success: true };
    } catch (error: any) {

      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    setLoading(true);
    await supabase.auth.signOut();
    setProfile(null);
    setLoading(false);
  };

  return (
    <AuthContext.Provider value={{
      user,
      session,
      profile,
      loading,
      signIn,
      signOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useSimpleAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider');
  }
  return context;
};
