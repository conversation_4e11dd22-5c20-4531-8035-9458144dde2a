import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { User, Session } from '@supabase/supabase-js';
import { MultiTenantService, SchoolProfile } from '@/services/multiTenantService';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: SchoolProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const SimpleAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<SchoolProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('🔐 SimpleAuthContext: Initializing...');

    // Get initial session
    supabase.auth.getSession().then(async ({ data: { session } }) => {
      console.log('🔐 SimpleAuthContext: Initial session:', !!session);
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        // Load user profile from database
        console.log('🔐 SimpleAuthContext: Loading initial profile for user:', session.user.id);
        const userProfile = await MultiTenantService.getCurrentUserProfile();
        console.log('🔐 SimpleAuthContext: Initial profile loaded:', userProfile);
        setProfile(userProfile);
      }

      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 SimpleAuthContext: Auth event:', event, 'Session:', !!session);
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Load user profile from database
          console.log('🔐 SimpleAuthContext: Loading profile for user:', session.user.id);
          const userProfile = await MultiTenantService.getCurrentUserProfile();
          console.log('🔐 SimpleAuthContext: Profile loaded:', userProfile);
          setProfile(userProfile);
        } else {
          console.log('🔐 SimpleAuthContext: No session/user, clearing profile');
          setProfile(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      console.log('🔐 SimpleAuthContext: signIn called with:', email);

      // TEMPORARY: Mock authentication for testing
      const useMockAuth = false;

      if (useMockAuth) {
        console.log('🔐 SimpleAuthContext: Using mock authentication');

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Determine user role based on email pattern
        let userRole = 'student'; // default to student for testing
        let firstName = email.split('@')[0];

        if (email.includes('admin')) {
          userRole = 'admin';
          firstName = 'Admin';
        } else if (email.includes('teacher')) {
          userRole = 'teacher';
          firstName = firstName.replace('teacher', 'Teacher ');
        } else if (email.includes('student') || email.includes('parent')) {
          userRole = 'student';
          firstName = firstName.replace('student', 'Student ');
        } else {
          // Default case - treat as student for testing
          userRole = 'student';
          firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);
        }

        console.log('🔐 SimpleAuthContext: Detected role:', userRole, 'for email:', email);

        // Mock user object with correct role and school context
        const mockUser = {
          id: `mock-${userRole}-${Date.now()}`,
          email: email,
          user_metadata: {
            firstName: firstName,
            role: userRole,
            school_id: 'ca245138-c35d-496c-ba6f-8cdbf326b457' // Use the known school2 ID
          }
        } as any;

        setUser(mockUser);
        setSession({ user: mockUser } as any);

        console.log('🔐 SimpleAuthContext: Mock login successful');
        return { success: true };
      }

      // Real Supabase authentication
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('🔐 SimpleAuthContext: Supabase auth error:', error);
        return { success: false, error: error.message };
      }

      console.log('🔐 SimpleAuthContext: Supabase login successful');
      return { success: true };
    } catch (error: any) {
      console.error('🔐 SimpleAuthContext: signIn error:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    setLoading(true);
    await supabase.auth.signOut();
    setProfile(null);
    setLoading(false);
  };

  return (
    <AuthContext.Provider value={{
      user,
      session,
      profile,
      loading,
      signIn,
      signOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useSimpleAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider');
  }
  return context;
};
