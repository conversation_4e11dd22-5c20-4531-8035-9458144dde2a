
import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useSchool } from './SchoolContext';
import { MultiTenantService, SchoolProfile } from '@/services/multiTenantService';
import { RoleAuthService } from '@/services/roleAuthService';

interface LoginData {
  email: string;
  password: string;
  role: 'admin' | 'teacher' | 'student';
  schoolSlug: string;
}

interface MultiTenantAuthContextType {
  user: User | null;
  session: Session | null;
  profile: SchoolProfile | null;
  login: (loginData: LoginData) => Promise<{ success: boolean; error?: string }>;
  register: (email: string, password: string, profileData: Partial<SchoolProfile>) => Promise<boolean>;
  logout: () => Promise<void>;
  loading: boolean;
  isAuthenticated: boolean;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
}

const MultiTenantAuthContext = createContext<MultiTenantAuthContextType | undefined>(undefined);

export const MultiTenantAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<SchoolProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const { currentSchool } = useSchool();
  
  // Temporarily allow authentication with just user (profile loading is failing)
  const isAuthenticated = !!user;

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state change event:', event, 'User ID:', session?.user?.id);
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Load user profile
          console.log('Loading profile for user:', session.user.id);
          const userProfile = await MultiTenantService.getCurrentUserProfile();
          console.log('Profile loaded:', userProfile);
          setProfile(userProfile);
        } else {
          console.log('No session/user, clearing profile');
          setProfile(null);
        }
        console.log('🏁 Setting auth loading to false');
        setLoading(false);
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        MultiTenantService.getCurrentUserProfile().then(setProfile);
      }
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const login = async (loginData: LoginData): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('Starting role-based login process:', loginData);

      // Use the role-based authentication service
      const authResult = await RoleAuthService.authenticateUser({
        username: loginData.email,
        password: loginData.password,
        role: loginData.role,
        schoolSlug: loginData.schoolSlug
      });

      if (authResult.success) {
        console.log('✅ Authentication successful for role:', loginData.role);

        // Set user and profile data
        if (authResult.user) {
          setUser(authResult.user);
        }

        if (authResult.profile) {
          setProfile(authResult.profile);
        }

        // For non-admin roles, we don't use Supabase auth session
        // Just set the user state directly
        if (loginData.role !== 'admin') {
          setSession({
            access_token: 'mock-token',
            refresh_token: 'mock-refresh',
            expires_in: 3600,
            token_type: 'bearer',
            user: authResult.user
          } as any);
        } else {
          // For admin, the session should already be set by Supabase auth
          console.log('Admin login - Supabase session should be active');
        }

        return { success: true };
      } else {
        console.log('❌ Authentication failed:', authResult.error);
        return { success: false, error: authResult.error };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Authentication failed. Please try again.' };
    }
  };

  const register = async (email: string, password: string, profileData: Partial<SchoolProfile>): Promise<boolean> => {
    if (!currentSchool) return false;

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/${currentSchool.slug}/dashboard`
        }
      });

      if (error || !data.user) return false;

      // Create user profile
      const newProfile = await MultiTenantService.createUserProfile(
        data.user.id,
        currentSchool.id,
        profileData.role || 'student',
        profileData
      );

      if (newProfile) {
        setProfile(newProfile);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Registration error:', error);
      return false;
    }
  };

  const logout = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      setSession(null);
      setProfile(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const hasRole = (role: string): boolean => {
    return profile?.role === role;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return profile ? roles.includes(profile.role) : false;
  };

  return (
    <MultiTenantAuthContext.Provider value={{
      user,
      session,
      profile,
      login,
      register,
      logout,
      loading,
      isAuthenticated,
      hasRole,
      hasAnyRole
    }}>
      {children}
    </MultiTenantAuthContext.Provider>
  );
};

export const useMultiTenantAuth = () => {
  const context = useContext(MultiTenantAuthContext);
  if (context === undefined) {
    throw new Error('useMultiTenantAuth must be used within a MultiTenantAuthProvider');
  }
  return context;
};
