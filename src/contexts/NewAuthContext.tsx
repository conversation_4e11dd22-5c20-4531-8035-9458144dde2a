import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface UserProfile {
  id: string;
  email: string;
  role: 'admin' | 'teacher' | 'student' | 'parent';
  first_name: string;
  last_name: string;
  school_id: string;
}

interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  // Load user profile from database
  const loadProfile = async (userId: string): Promise<UserProfile | null> => {
    console.log('🔍 NewAuth - Loading profile for user:', userId);

    try {
      // Try direct REST API call first
      console.log('📡 NewAuth - Trying direct REST API...');
      const response = await fetch(
        `https://rkqndobgwwkzlleidbrp.supabase.co/rest/v1/profiles?id=eq.${userId}&select=*`,
        {
          headers: {
            'Content-Type': 'application/json',
            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.OgH2Gw5NOjQCnkfgpf66w9LK5rKzZGKN2F2sL4OoGYU'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        console.log('✅ NewAuth - REST API response:', data);
        if (data && data.length > 0) {
          return data[0] as UserProfile;
        }
      }

      console.log('⚠️ NewAuth - REST API failed, trying Supabase client...');

      // Fallback to Supabase client with timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Supabase client timeout')), 2000);
      });

      const queryPromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]);

      if (error) {
        console.error('❌ NewAuth - Supabase client error:', error);
        throw error;
      }

      console.log('✅ NewAuth - Supabase client success:', data);
      return data as UserProfile;

    } catch (error) {
      console.error('❌ NewAuth - All profile loading methods failed:', error);

      // Create a mock profile as last resort
      console.log('🔄 NewAuth - Creating mock profile for user:', userId);
      const mockProfile: UserProfile = {
        id: userId,
        email: userId === '885a23c9-f555-446c-b443-5a36b52ec5c6' ? '<EMAIL>' : '<EMAIL>',
        role: userId === '885a23c9-f555-446c-b443-5a36b52ec5c6' ? 'student' : 'admin',
        first_name: 'Test',
        last_name: 'User',
        school_id: 'school2'
      };

      console.log('🎭 NewAuth - Using mock profile:', mockProfile);
      return mockProfile;
    }
  };

  // Initialize auth state
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        // Get initial session
        const { data: { session: initialSession }, error } = await supabase.auth.getSession();
        
        if (!mounted) return;

        if (error) {
          console.error('Session error:', error);
          setLoading(false);
          return;
        }

        if (initialSession) {
          setSession(initialSession);
          setUser(initialSession.user);
          
          // Load profile
          const userProfile = await loadProfile(initialSession.user.id);
          if (mounted) {
            setProfile(userProfile);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        if (!mounted) return;

        console.log('🔄 NewAuth - Auth state change:', event, 'User:', !!newSession?.user);

        setSession(newSession);
        setUser(newSession?.user ?? null);

        if (newSession?.user) {
          console.log('👤 NewAuth - Loading profile for authenticated user...');
          const userProfile = await loadProfile(newSession.user.id);
          if (mounted) {
            console.log('📝 NewAuth - Setting profile:', !!userProfile);
            setProfile(userProfile);
          }
        } else {
          console.log('🚫 NewAuth - No user, clearing profile');
          setProfile(null);
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    setUser(null);
    setSession(null);
    setProfile(null);
  };

  const value = {
    user,
    profile,
    session,
    loading,
    signIn,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
