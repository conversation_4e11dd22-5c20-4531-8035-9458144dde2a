import React, { createContext, useContext } from 'react';
import { useCleanAuth } from './CleanAuthContext';
import { User, UserRole } from '@/types';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  loading: boolean;
  isAuthenticated: boolean;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const MockAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user: simpleUser, signIn, signOut, loading } = useSimpleAuth();

  // Convert SimpleAuth user to AuthContext user format
  const user: User | null = simpleUser ? {
    id: simpleUser.id,
    email: simpleUser.email || '',
    username: simpleUser.email?.split('@')[0] || '',
    firstName: simpleUser.email?.split('@')[0] || 'User',
    lastName: '',
    role: 'admin' as UserRole,
    phone: '',
    address: '',
    isActive: true,
    avatar: '',
    permissions: ['read', 'write', 'delete']
  } : null;

  const login = async (email: string, password: string): Promise<boolean> => {
    const result = await signIn(email, password);
    return result.success;
  };

  const logout = async (): Promise<void> => {
    await signOut();
  };

  const hasRole = (role: UserRole): boolean => {
    return user?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return user ? roles.includes(user.role) : false;
  };

  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  };

  const value: AuthContextType = {
    user,
    login,
    logout,
    loading,
    isAuthenticated: !!user,
    hasRole,
    hasAnyRole,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
