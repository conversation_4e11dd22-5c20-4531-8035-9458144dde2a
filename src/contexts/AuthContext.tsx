
import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, UserRole } from '@/types';
import { supabase } from '@/integrations/supabase/client';
import { useSimpleAuth } from './SimpleAuthContext';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  loading: boolean;
  isAuthenticated: boolean;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Demo users with their credentials (for backwards compatibility)
const DEMO_USERS = {
  '<EMAIL>': {
    id: 'admin-1',
    email: '<EMAIL>',
    username: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as UserRole,
    phone: '+1234567890',
    address: '123 Main St',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  '<EMAIL>': {
    id: 'teacher-1',
    email: '<EMAIL>',
    username: 'teacher1',
    firstName: 'John',
    lastName: 'Smith',
    role: 'teacher' as UserRole,
    phone: '+1234567891',
    address: '456 Oak Ave',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  '<EMAIL>': {
    id: 'parent-1',
    email: '<EMAIL>',
    username: 'parent1',
    firstName: 'Robert',
    lastName: 'Johnson',
    role: 'parent' as UserRole,
    phone: '+**********',
    address: '789 Pine St',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
};

const DEMO_PASSWORDS = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'teacher123',
  '<EMAIL>': 'parent123'
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user: simpleUser, signIn, signOut, loading: simpleLoading } = useSimpleAuth();
  const [user, setUser] = useState<User | null>(null);
  const loading = simpleLoading;
  const isAuthenticated = !!user;

  // Convert SimpleAuth user to AuthContext user format
  useEffect(() => {
    if (simpleUser) {
      const compatUser: User = {
        id: simpleUser.id,
        email: simpleUser.email || '',
        username: simpleUser.email?.split('@')[0] || '',
        firstName: simpleUser.email?.split('@')[0] || 'User',
        lastName: '',
        role: 'admin' as UserRole,
        phone: '',
        address: '',
        isActive: true,
        avatar: '',
        permissions: ['read', 'write', 'delete']
      };
      setUser(compatUser);
    } else {
      setUser(null);
    }
  }, [simpleUser]);

  const login = async (email: string, password: string): Promise<boolean> => {
    setLoading(true);

    try {
      // Check if it's a demo user
      if (email in DEMO_PASSWORDS && DEMO_PASSWORDS[email as keyof typeof DEMO_PASSWORDS] === password) {
        const demoUser = DEMO_USERS[email as keyof typeof DEMO_USERS];
        setUser(demoUser);
        localStorage.setItem('currentUser', JSON.stringify(demoUser));
        setLoading(false);
        return true;
      }

      // Try Supabase authentication for real users
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Supabase login error:', error.message);
        setLoading(false);
        return false;
      }

      if (data.user) {
        // Try to get user profile from database
        try {
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', data.user.id)
            .single();

          if (!profileError && profile) {
            const userProfile: User = {
              id: profile.id,
              email: profile.email || data.user.email || email,
              username: profile.email?.split('@')[0] || 'user',
              firstName: profile.first_name || 'User',
              lastName: profile.last_name || 'Name',
              role: profile.role as UserRole,
              phone: profile.phone || undefined,
              address: profile.address || undefined,
              isActive: profile.is_active,
              createdAt: profile.created_at,
              updatedAt: profile.updated_at
            };
            setUser(userProfile);
            localStorage.setItem('currentUser', JSON.stringify(userProfile));
            setLoading(false);
            return true;
          }
        } catch (profileError) {
          console.error('Profile fetch error:', profileError);
        }

        // Fallback: create user from auth data
        const fallbackUser: User = {
          id: data.user.id,
          email: data.user.email || email,
          username: data.user.email?.split('@')[0] || 'user',
          firstName: 'User',
          lastName: 'Name',
          role: 'admin',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        setUser(fallbackUser);
        localStorage.setItem('currentUser', JSON.stringify(fallbackUser));
        setLoading(false);
        return true;
      }

      setLoading(false);
      return false;
    } catch (error) {
      console.error('Login error:', error);
      setLoading(false);
      return false;
    }
  };

  const logout = async () => {
    await signOut();
  };

  const hasRole = (role: UserRole): boolean => {
    return user?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return user ? roles.includes(user.role) : false;
  };

  const hasPermission = (permission: string): boolean => {
    return user?.role === 'admin' || true; // Simplified for now
  };

  const value = {
    user,
    login,
    logout,
    loading,
    isAuthenticated,
    hasRole,
    hasAnyRole,
    hasPermission
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Export the mock auth hook that works with SimpleAuth
export { useAuth } from './MockAuthContext';
