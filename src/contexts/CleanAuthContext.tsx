import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { SchoolProfile } from '@/types/auth';

interface CleanAuthContextType {
  user: User | null;
  session: Session | null;
  profile: SchoolProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
}

const CleanAuthContext = createContext<CleanAuthContextType | undefined>(undefined);

export const CleanAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<SchoolProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Function to load user profile
  const loadUserProfile = async (userId: string): Promise<SchoolProfile | null> => {
    try {
      console.log('🔍 Loading profile for user:', userId);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Profile loading error:', error.message);
        return null;
      }

      console.log('✅ Profile loaded:', data);
      return data as SchoolProfile;
    } catch (error) {
      console.error('❌ Profile loading exception:', error);
      return null;
    }
  };

  useEffect(() => {
    let mounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('🔄 Starting getInitialSession...');
        const { data: { session }, error } = await supabase.auth.getSession();
        console.log('📋 Got session response:', { session: !!session, error: !!error });

        if (!mounted) {
          console.log('⚠️ Component unmounted, skipping session setup');
          return;
        }

        if (error) {
          console.error('❌ Session error:', error);
          setLoading(false);
          return;
        }

        console.log('✅ Setting session and user state');
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          console.log('👤 Loading user profile...');
          const userProfile = await loadUserProfile(session.user.id);
          if (mounted) {
            setProfile(userProfile);
            console.log('✅ Profile set successfully');
          }
        } else {
          console.log('👤 No session user found');
        }
      } catch (error) {
        console.error('❌ Initial session error:', error);
      } finally {
        if (mounted) {
          console.log('🏁 Setting loading to false');
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // Safety timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (mounted) {
        console.log('⏰ Timeout reached, forcing loading to false');
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state change:', event, !!session);
        if (!mounted) return;

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          const userProfile = await loadUserProfile(session.user.id);
          if (mounted) {
            setProfile(userProfile);
          }
        } else {
          setProfile(null);
        }
      }
    );

    return () => {
      mounted = false;
      clearTimeout(timeoutId);
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    setUser(null);
    setSession(null);
    setProfile(null);
  };

  return (
    <CleanAuthContext.Provider value={{
      user,
      session,
      profile,
      loading,
      signIn,
      signOut,
    }}>
      {children}
    </CleanAuthContext.Provider>
  );
};

export const useCleanAuth = () => {
  const context = useContext(CleanAuthContext);
  if (context === undefined) {
    throw new Error('useCleanAuth must be used within a CleanAuthProvider');
  }
  return context;
};
