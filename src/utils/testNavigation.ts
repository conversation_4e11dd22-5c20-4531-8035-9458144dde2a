// Test utility to verify role-based navigation
export const testNavigationForRole = (role: string) => {
  console.log(`🧪 Testing navigation for role: ${role}`);
  
  // Mock navigation items (from Sidebar.tsx)
  const allNavigationItems = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: "Home",
      roles: ["admin", "teacher", "student", "parent"]
    },
    {
      name: "Students",
      href: "/students",
      icon: "Users",
      roles: ["admin", "teacher", "parent"]
    },
    {
      name: "Teachers",
      href: "/teachers", 
      icon: "GraduationCap",
      roles: ["admin"]
    },
    {
      name: "Classes",
      href: "/classes",
      icon: "BookOpen", 
      roles: ["admin", "teacher", "student", "parent"]
    },
    {
      name: "Subjects",
      href: "/subjects",
      icon: "Book",
      roles: ["admin", "teacher", "student", "parent"]
    },
    {
      name: "Assignments",
      href: "/assignments",
      icon: "FileText",
      roles: ["admin", "teacher", "student", "parent"]
    },
    {
      name: "Student Timetable",
      href: "/student-timetable",
      icon: "Calendar",
      roles: ["student"]
    },
    {
      name: "Student Assignments", 
      href: "/student-assignments",
      icon: "FileText",
      roles: ["student"]
    },
    {
      name: "Student Examinations",
      href: "/student-examinations", 
      icon: "ClipboardList",
      roles: ["student"]
    },
    {
      name: "Student Results",
      href: "/student-results",
      icon: "Award",
      roles: ["student"]
    },
    {
      name: "Teacher Timetable",
      href: "/teacher-timetable",
      icon: "Calendar", 
      roles: ["teacher"]
    },
    {
      name: "User Management",
      href: "/users",
      icon: "Settings",
      roles: ["admin"]
    }
  ];

  // Filter items for the role
  const accessibleItems = allNavigationItems.filter(item => 
    item.roles.includes(role)
  );

  console.log(`✅ ${role} can access ${accessibleItems.length} navigation items:`);
  accessibleItems.forEach(item => {
    console.log(`  - ${item.name} (${item.href})`);
  });

  // Test specific role expectations
  switch (role) {
    case 'admin':
      console.log(`🔍 Admin should have access to all management features`);
      const adminExpected = ['Dashboard', 'Students', 'Teachers', 'Classes', 'Subjects', 'Assignments', 'User Management'];
      const adminActual = accessibleItems.map(item => item.name);
      const adminMissing = adminExpected.filter(item => !adminActual.includes(item));
      if (adminMissing.length > 0) {
        console.error(`❌ Admin missing access to: ${adminMissing.join(', ')}`);
      } else {
        console.log(`✅ Admin has all expected access`);
      }
      break;

    case 'teacher':
      console.log(`🔍 Teacher should have access to teaching features`);
      const teacherExpected = ['Dashboard', 'Students', 'Classes', 'Subjects', 'Assignments', 'Teacher Timetable'];
      const teacherActual = accessibleItems.map(item => item.name);
      const teacherMissing = teacherExpected.filter(item => !teacherActual.includes(item));
      if (teacherMissing.length > 0) {
        console.error(`❌ Teacher missing access to: ${teacherMissing.join(', ')}`);
      } else {
        console.log(`✅ Teacher has all expected access`);
      }
      break;

    case 'student':
      console.log(`🔍 Student should have access to student-specific features`);
      const studentExpected = ['Dashboard', 'Classes', 'Subjects', 'Assignments', 'Student Timetable', 'Student Assignments', 'Student Examinations', 'Student Results'];
      const studentActual = accessibleItems.map(item => item.name);
      const studentMissing = studentExpected.filter(item => !studentActual.includes(item));
      if (studentMissing.length > 0) {
        console.error(`❌ Student missing access to: ${studentMissing.join(', ')}`);
      } else {
        console.log(`✅ Student has all expected access`);
      }
      break;

    case 'parent':
      console.log(`🔍 Parent should have access to child monitoring features`);
      const parentExpected = ['Dashboard', 'Students', 'Classes', 'Subjects', 'Assignments'];
      const parentActual = accessibleItems.map(item => item.name);
      const parentMissing = parentExpected.filter(item => !parentActual.includes(item));
      if (parentMissing.length > 0) {
        console.error(`❌ Parent missing access to: ${parentMissing.join(', ')}`);
      } else {
        console.log(`✅ Parent has all expected access`);
      }
      break;
  }

  return accessibleItems;
};

// Test all roles
export const testAllRoles = () => {
  console.log('🧪 Starting comprehensive role-based navigation test...');
  
  const roles = ['admin', 'teacher', 'student', 'parent'];
  
  roles.forEach(role => {
    console.log('\n' + '='.repeat(50));
    testNavigationForRole(role);
  });
  
  console.log('\n' + '='.repeat(50));
  console.log('🧪 Navigation test completed!');
};
