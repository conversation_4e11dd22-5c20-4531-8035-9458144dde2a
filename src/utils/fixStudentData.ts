import { supabase } from '@/integrations/supabase/client';

export async function fixStudentData() {
  try {
    console.log('Fixing student data...');
    
    // Update students with proper parent emails
    const updates = [
      {
        student_id: '416541',
        parent_email: '<EMAIL>'
      },
      {
        student_id: '416542', 
        parent_email: '<EMAIL>'
      },
      {
        student_id: '416543',
        parent_email: '<EMAIL>'
      }
    ];

    for (const update of updates) {
      const { data, error } = await supabase
        .from('students')
        .update({ parent_email: update.parent_email })
        .eq('student_id', update.student_id)
        .eq('school_id', 'ca245138-c35d-496c-ba6f-8cdbf326b457');

      if (error) {
        console.error(`Error updating student ${update.student_id}:`, error);
      } else {
        console.log(`Updated student ${update.student_id} with parent email ${update.parent_email}`);
      }
    }

    console.log('Student data fix completed');
  } catch (error) {
    console.error('Error fixing student data:', error);
  }
}

// Call this function to fix the data
// fixStudentData();
