import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';

type Class = Database['public']['Tables']['classes']['Row'];
type ClassInsert = Database['public']['Tables']['classes']['Insert'];
type ClassUpdate = Database['public']['Tables']['classes']['Update'];

export const classService = {
  // Get all classes
  async getAll(): Promise<Class[]> {
    const { data, error } = await supabase
      .from('classes')
      .select(`
        *,
        class_teacher:class_teacher_id (
          id,
          first_name,
          last_name
        )
      `)
      .eq('is_active', true)
      .order('grade_level', { ascending: true })
      .order('section', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Get class by ID
  async getById(id: string): Promise<Class | null> {
    const { data, error } = await supabase
      .from('classes')
      .select(`
        *,
        class_teacher:class_teacher_id (
          id,
          first_name,
          last_name
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Get classes assigned to a teacher
  async getByTeacherId(teacherId: string): Promise<Class[]> {
    const { data, error } = await supabase
      .from('classes')
      .select(`
        *,
        teacher_classes!inner (
          teacher_id
        )
      `)
      .eq('teacher_classes.teacher_id', teacherId)
      .eq('teacher_classes.is_active', true)
      .eq('is_active', true)
      .order('grade_level', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Get classes where parent's children are enrolled
  async getByParentId(parentId: string): Promise<Class[]> {
    const { data, error } = await supabase
      .from('classes')
      .select(`
        *,
        students!inner (
          id,
          parent_students!inner (
            parent_id
          )
        )
      `)
      .eq('students.parent_students.parent_id', parentId)
      .eq('is_active', true)
      .order('grade_level', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Create new class
  async create(classData: ClassInsert): Promise<Class> {
    const { data, error } = await supabase
      .from('classes')
      .insert(classData)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update class
  async update(id: string, updates: ClassUpdate): Promise<Class> {
    const { data, error } = await supabase
      .from('classes')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete class (soft delete)
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('classes')
      .update({ is_active: false })
      .eq('id', id);

    if (error) throw error;
  },

  // Get class statistics
  async getStats(classId: string) {
    const [studentsResult, attendanceResult] = await Promise.all([
      // Get student count
      supabase
        .from('students')
        .select('id', { count: 'exact' })
        .eq('class_id', classId)
        .eq('is_active', true),
      
      // Get average attendance for the class
      supabase
        .from('attendance')
        .select('status')
        .eq('class_id', classId)
        .gte('date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
    ]);

    const studentCount = studentsResult.count || 0;
    const attendanceData = attendanceResult.data || [];
    
    const totalRecords = attendanceData.length;
    const presentRecords = attendanceData.filter(a => a.status === 'present').length;
    const attendanceRate = totalRecords > 0 ? (presentRecords / totalRecords) * 100 : 0;

    return {
      studentCount,
      attendanceRate: Math.round(attendanceRate)
    };
  }
};
