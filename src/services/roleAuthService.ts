import { studentsService, teachersService, getCurrentSchoolId } from './supabaseService';
import { supabase } from '@/integrations/supabase/client';

export interface RoleLoginData {
  username: string;
  password: string;
  role: 'admin' | 'teacher' | 'student';
  schoolSlug: string;
}

export interface AuthResult {
  success: boolean;
  user?: any;
  profile?: any;
  error?: string;
}

export class RoleAuthService {
  
  static async authenticateUser(loginData: RoleLoginData): Promise<AuthResult> {
    try {
      const schoolId = await getCurrentSchoolId(loginData.schoolSlug);
      if (!schoolId) {
        return { success: false, error: 'School not found' };
      }

      switch (loginData.role) {
        case 'admin':
          return await this.authenticateAdmin(loginData, schoolId);
        case 'teacher':
          return await this.authenticateTeacher(loginData, schoolId);
        case 'student':
          return await this.authenticateStudent(loginData, schoolId);
        default:
          return { success: false, error: 'Invalid role' };
      }
    } catch (error) {
      console.error('Authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  private static async authenticateAdmin(loginData: RoleLoginData, schoolId: string): Promise<AuthResult> {
    try {
      // Admin authentication using Supabase Auth with real credentials
      const { data, error } = await supabase.auth.signInWithPassword({
        email: loginData.username,
        password: loginData.password,
      });

      if (error) {
        console.error('Admin auth error:', error);
        return { success: false, error: 'Invalid admin credentials. Please check your email and password.' };
      }

      if (data.user) {
        // Verify the user has admin access to this school
        // For now, we'll assume any authenticated user can be admin
        // In production, you'd check admin permissions in the database

        return {
          success: true,
          user: data.user,
          profile: {
            id: data.user.id,
            first_name: data.user.user_metadata?.first_name || 'Admin',
            last_name: data.user.user_metadata?.last_name || 'User',
            email: data.user.email,
            role: 'admin',
            school_id: schoolId,
            created_at: data.user.created_at,
            updated_at: data.user.updated_at || new Date().toISOString()
          }
        };
      }

      return { success: false, error: 'Authentication failed' };
    } catch (error) {
      console.error('Admin authentication error:', error);
      return { success: false, error: 'Admin authentication failed. Please try again.' };
    }
  }

  private static async authenticateTeacher(loginData: RoleLoginData, schoolId: string): Promise<AuthResult> {
    try {
      // Teacher authentication: Email as username, Employee ID as password
      const email = loginData.username;
      const employeeId = loginData.password;

      // Try Supabase Auth first (for registered teachers)
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: email,
        password: employeeId,
      });

      if (authData?.user) {
        // Fetch teacher data from database to get full profile
        const teachersData = await teachersService.getAll(schoolId);
        const teacher = teachersData?.find(t => t.email === email && t.employee_id === employeeId);

        if (!teacher) {
          return { success: false, error: 'Teacher record not found in database.' };
        }

        if (!teacher.is_active) {
          return { success: false, error: 'Teacher account is inactive. Please contact administration.' };
        }

        return {
          success: true,
          user: authData.user,
          profile: {
            id: teacher.id,
            first_name: teacher.first_name || 'Teacher',
            last_name: teacher.last_name || '',
            email: teacher.email,
            role: 'teacher',
            school_id: schoolId,
            employee_id: employeeId,
            created_at: teacher.created_at,
            updated_at: teacher.updated_at
          }
        };
      }

      // Fallback: Check if teacher exists in database (for legacy accounts)
      const teachersData = await teachersService.getAll(schoolId);
      const teacher = teachersData?.find(t => t.employee_id === employeeId);

      if (!teacher) {
        return { success: false, error: 'Teacher not found. Please check your credentials or sign up first.' };
      }

      if (!teacher.is_active) {
        return { success: false, error: 'Teacher account is inactive. Please contact administration.' };
      }

      // For legacy teachers without Supabase auth, create a mock session
      return {
        success: true,
        user: {
          id: `teacher-${teacher.id}`,
          email: teacher.email || `${employeeId}@school.com`,
        },
        profile: {
          id: teacher.id,
          first_name: teacher.first_name || 'Teacher',
          last_name: teacher.last_name || employeeId,
          email: teacher.email || `${employeeId}@school.com`,
          role: 'teacher',
          school_id: schoolId,
          employee_id: employeeId,
          created_at: teacher.created_at,
          updated_at: teacher.updated_at
        }
      };
    } catch (error) {
      return { success: false, error: 'Teacher authentication error' };
    }
  }

  private static async authenticateStudent(loginData: RoleLoginData, schoolId: string): Promise<AuthResult> {
    try {
      // Student authentication: Parent email as username, Student ID as password
      const parentEmail = loginData.username;
      const studentId = loginData.password;

      // Basic validation
      if (!parentEmail.includes('@')) {
        return { success: false, error: 'Please enter a valid parent email address.' };
      }

      if (!studentId) {
        return { success: false, error: 'Please enter your Student ID.' };
      }

      // Fetch student data from database
      const studentsData = await studentsService.getAll(schoolId);

      // Auto-update students without parent_email for easier testing
      // In production, this should be done through proper data entry
      if (studentsData) {
        for (const student of studentsData) {
          if (!student.parent_email && student.student_id) {
            const generatedEmail = `parent.${student.student_id.toLowerCase()}@example.com`;
            try {
              await studentsService.update(student.id, {
                parent_email: generatedEmail
              });
              student.parent_email = generatedEmail; // Update local copy
              console.log(`Auto-generated parent email for student ${student.student_id}: ${generatedEmail}`);
            } catch (error) {
              console.error(`Failed to update parent email for student ${student.student_id}:`, error);
            }
          }
        }
      }

      console.log('Student authentication debug:', {
        parentEmail,
        studentId,
        studentsCount: studentsData?.length,
        studentsData: studentsData?.map(s => ({
          id: s.id,
          student_id: s.student_id,
          parent_email: s.parent_email,
          first_name: s.first_name,
          last_name: s.last_name,
          is_active: s.is_active
        }))
      });

      // Try Supabase Auth first (for registered students)
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: parentEmail,
        password: studentId,
      });

      if (authData?.user) {
        // Fetch student data from database to get full profile
        const student = studentsData?.find(s =>
          s.parent_email?.toLowerCase() === parentEmail.toLowerCase() &&
          s.student_id === studentId
        );

        if (!student) {
          return { success: false, error: 'Student record not found in database.' };
        }

        if (!student.is_active) {
          return { success: false, error: 'Student account is inactive. Please contact administration.' };
        }

        return {
          success: true,
          user: authData.user,
          profile: {
            id: student.id,
            first_name: student.first_name || 'Student',
            last_name: student.last_name || '',
            email: parentEmail,
            role: 'student',
            school_id: schoolId,
            student_id: studentId,
            created_at: student.created_at,
            updated_at: student.updated_at
          }
        };
      }

      // Fallback: Find student by exact match (for legacy accounts)
      const student = studentsData?.find(s =>
        s.student_id === studentId &&
        s.parent_email?.toLowerCase() === parentEmail.toLowerCase()
      );

      if (!student) {
        console.log('No student found with matching credentials');
        console.log('Available students:', studentsData?.map(s => ({
          student_id: s.student_id,
          parent_email: s.parent_email,
          name: `${s.first_name} ${s.last_name}`
        })));

        return {
          success: false,
          error: 'Invalid credentials. Please check your parent email and Student ID or sign up first.'
        };
      }

      console.log('Found matching student:', {
        id: student.id,
        student_id: student.student_id,
        parent_email: student.parent_email,
        first_name: student.first_name,
        last_name: student.last_name
      });

      console.log('Student found:', student);

      if (!student) {
        return { success: false, error: 'Student not found. Please check your parent email and Student ID.' };
      }

      if (!student.is_active) {
        return { success: false, error: 'Student account is inactive. Please contact administration.' };
      }

      return {
        success: true,
        user: {
          id: `student-${student.id}`,
          email: parentEmail,
        },
        profile: {
          id: student.id,
          first_name: student.first_name || 'Student',
          last_name: student.last_name || studentId,
          email: parentEmail,
          role: 'student',
          school_id: schoolId,
          student_id: studentId,
          created_at: student.created_at,
          updated_at: student.updated_at
        }
      };
    } catch (error) {
      return { success: false, error: 'Student authentication error' };
    }
  }

  static async logout(): Promise<void> {
    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
}
