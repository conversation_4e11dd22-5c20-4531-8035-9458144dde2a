import { supabase } from '@/integrations/supabase/client';
import { MultiTenantService } from './multiTenantService';

// Attendance Service
export class AttendanceService extends MultiTenantService {
  // Mark attendance for a class
  static async markClassAttendance(attendanceData: {
    class_id: string;
    date: string;
    attendance_records: Array<{
      student_id: string;
      status: 'present' | 'absent' | 'late' | 'excused';
      remarks?: string;
    }>;
  }) {
    return this.withSchoolContext(async (schoolId, userId) => {
      const records = attendanceData.attendance_records.map(record => ({
        ...record,
        school_id: schoolId,
        class_id: attendanceData.class_id,
        date: attendanceData.date,
        marked_by: userId,
      }));

      const { data, error } = await supabaseAdmin
        .from('attendance')
        .upsert(records, { 
          onConflict: 'student_id,date',
          ignoreDuplicates: false 
        })
        .select();

      if (error) throw error;
      return data;
    });
  }

  // Get attendance for a specific date and class
  static async getClassAttendance(classId: string, date: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('attendance')
        .select(`
          *,
          student:students(
            id,
            student_id,
            first_name,
            last_name,
            roll_number
          ),
          marked_by_user:users!marked_by(first_name, last_name)
        `)
        .eq('school_id', schoolId)
        .eq('class_id', classId)
        .eq('date', date)
        .order('student.roll_number');

      if (error) throw error;
      return data;
    });
  }

  // Get attendance summary for a student
  static async getStudentAttendanceSummary(studentId: string, startDate?: string, endDate?: string) {
    return this.withSchoolContext(async (schoolId) => {
      let query = supabase
        .from('attendance')
        .select('*')
        .eq('school_id', schoolId)
        .eq('student_id', studentId);

      if (startDate) query = query.gte('date', startDate);
      if (endDate) query = query.lte('date', endDate);

      const { data, error } = await query.order('date', { ascending: false });

      if (error) throw error;

      // Calculate summary statistics
      const total = data.length;
      const present = data.filter(record => record.status === 'present').length;
      const absent = data.filter(record => record.status === 'absent').length;
      const late = data.filter(record => record.status === 'late').length;
      const excused = data.filter(record => record.status === 'excused').length;
      const percentage = total > 0 ? ((present + late) / total) * 100 : 0;

      return {
        records: data,
        summary: {
          total,
          present,
          absent,
          late,
          excused,
          percentage: Math.round(percentage * 100) / 100
        }
      };
    });
  }

  // Get attendance summary for a class
  static async getClassAttendanceSummary(classId: string, startDate?: string, endDate?: string) {
    return this.withSchoolContext(async (schoolId) => {
      let query = supabase
        .from('attendance')
        .select(`
          *,
          student:students(
            id,
            student_id,
            first_name,
            last_name,
            roll_number
          )
        `)
        .eq('school_id', schoolId)
        .eq('class_id', classId);

      if (startDate) query = query.gte('date', startDate);
      if (endDate) query = query.lte('date', endDate);

      const { data, error } = await query.order('date', { ascending: false });

      if (error) throw error;

      // Group by student and calculate individual summaries
      const studentSummaries = data.reduce((acc, record) => {
        const studentId = record.student_id;
        if (!acc[studentId]) {
          acc[studentId] = {
            student: record.student,
            records: [],
            summary: { total: 0, present: 0, absent: 0, late: 0, excused: 0, percentage: 0 }
          };
        }
        
        acc[studentId].records.push(record);
        acc[studentId].summary.total++;
        acc[studentId].summary[record.status]++;
        
        return acc;
      }, {} as any);

      // Calculate percentages
      Object.values(studentSummaries).forEach((student: any) => {
        const { present, late, total } = student.summary;
        student.summary.percentage = total > 0 ? Math.round(((present + late) / total) * 100 * 100) / 100 : 0;
      });

      return Object.values(studentSummaries);
    });
  }

  // Get attendance statistics for dashboard
  static async getAttendanceStats(dateRange?: { start: string; end: string }) {
    return this.withSchoolContext(async (schoolId) => {
      const today = new Date().toISOString().split('T')[0];
      const startDate = dateRange?.start || today;
      const endDate = dateRange?.end || today;

      const { data, error } = await supabase
        .from('attendance')
        .select('status')
        .eq('school_id', schoolId)
        .gte('date', startDate)
        .lte('date', endDate);

      if (error) throw error;

      const stats = data.reduce((acc, record) => {
        acc[record.status] = (acc[record.status] || 0) + 1;
        acc.total++;
        return acc;
      }, { total: 0, present: 0, absent: 0, late: 0, excused: 0 });

      const attendanceRate = stats.total > 0 ? 
        Math.round(((stats.present + stats.late) / stats.total) * 100 * 100) / 100 : 0;

      return {
        ...stats,
        attendanceRate
      };
    });
  }
}
