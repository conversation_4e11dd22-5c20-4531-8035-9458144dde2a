import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';

type Subject = Database['public']['Tables']['subjects']['Row'];
type SubjectInsert = Database['public']['Tables']['subjects']['Insert'];
type SubjectUpdate = Database['public']['Tables']['subjects']['Update'];

export const subjectService = {
  // Get all subjects
  async getAll(): Promise<Subject[]> {
    const { data, error } = await supabase
      .from('subjects')
      .select(`
        *,
        departments:department_id (
          id,
          name
        )
      `)
      .eq('is_active', true)
      .order('name');

    if (error) throw error;
    return data || [];
  },

  // Get subject by ID
  async getById(id: string): Promise<Subject | null> {
    const { data, error } = await supabase
      .from('subjects')
      .select(`
        *,
        departments:department_id (
          id,
          name
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Get subjects by department
  async getByDepartmentId(departmentId: string): Promise<Subject[]> {
    const { data, error } = await supabase
      .from('subjects')
      .select('*')
      .eq('department_id', departmentId)
      .eq('is_active', true)
      .order('name');

    if (error) throw error;
    return data || [];
  },

  // Get subjects taught by a teacher
  async getByTeacherId(teacherId: string): Promise<Subject[]> {
    const { data, error } = await supabase
      .from('subjects')
      .select(`
        *,
        teacher_classes!inner (
          teacher_id
        )
      `)
      .eq('teacher_classes.teacher_id', teacherId)
      .eq('teacher_classes.is_active', true)
      .eq('is_active', true)
      .order('name');

    if (error) throw error;
    return data || [];
  },

  // Create new subject
  async create(subject: SubjectInsert): Promise<Subject> {
    const { data, error } = await supabase
      .from('subjects')
      .insert(subject)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update subject
  async update(id: string, updates: SubjectUpdate): Promise<Subject> {
    const { data, error } = await supabase
      .from('subjects')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete subject (soft delete)
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('subjects')
      .update({ is_active: false })
      .eq('id', id);

    if (error) throw error;
  }
};
