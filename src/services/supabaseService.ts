import { supabase } from '@/integrations/supabase/client';

// Get current school ID from URL or context
export const getCurrentSchoolId = async (schoolSlug: string): Promise<string | null> => {
  try {
    console.log('🏫 getCurrentSchoolId called with slug:', schoolSlug);

    // Handle undefined or empty school slug
    if (!schoolSlug || schoolSlug === 'undefined') {
      console.warn('🏫 getCurrentSchoolId: Invalid school slug provided:', schoolSlug);
      return null;
    }

    // Query Supabase for the school
    console.log('🏫 Querying Supabase for school with slug:', schoolSlug);

    const { data, error } = await supabase
      .from('schools')
      .select('id, name, slug')
      .eq('slug', schoolSlug)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('🏫 Error fetching school:', error);
      console.error('🏫 Error details:', error.message, error.code);
      return null;
    }

    console.log('🏫 School found:', data);
    const schoolId = data?.id || null;
    console.log('🏫 Returning school ID:', schoolId);
    return schoolId;
  } catch (error) {
    console.error('🏫 Error in getCurrentSchoolId:', error);
    return null;
  }
};

// Students Service
export const studentsService = {
  async getAll(schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('students')
        .select('*')
        .eq('school_id', schoolId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase students error:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in studentsService.getAll:', error);
      throw error;
    }
  },



  async getById(id: string) {
    const { data, error } = await supabase
      .from('students')
      .select(`
        *,
        class:classes(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async create(studentData: any, schoolId: string) {
    // Create the student record directly with all information
    const studentRecord = {
      school_id: schoolId,
      student_id: studentData.studentId,
      class_id: studentData.classId,
      admission_date: studentData.admissionDate,
      parent_contact: studentData.parentContact,
      emergency_contact: studentData.emergencyContact,
      medical_info: studentData.medicalInfo,
      // Store additional student info directly
      first_name: studentData.firstName,
      last_name: studentData.lastName,
      parent_email: studentData.email, // Parent's email for login
      phone: studentData.phone,
      address: studentData.address,
      date_of_birth: studentData.dateOfBirth,
      gender: studentData.gender,
    };

    const { data, error } = await supabase
      .from('students')
      .insert(studentRecord)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async update(id: string, studentData: any) {
    try {
      const studentRecord: any = {};
      if (studentData.studentId) studentRecord.student_id = studentData.studentId;
      if (studentData.firstName) studentRecord.first_name = studentData.firstName;
      if (studentData.lastName) studentRecord.last_name = studentData.lastName;
      if (studentData.email) studentRecord.parent_email = studentData.email;
      if (studentData.phone) studentRecord.phone = studentData.phone;
      if (studentData.address) studentRecord.address = studentData.address;
      if (studentData.dateOfBirth) studentRecord.date_of_birth = studentData.dateOfBirth;
      if (studentData.gender) studentRecord.gender = studentData.gender;
      if (studentData.classId) studentRecord.class_id = studentData.classId;
      if (studentData.admissionDate) studentRecord.admission_date = studentData.admissionDate;
      if (studentData.parentContact) studentRecord.parent_contact = studentData.parentContact;
      if (studentData.emergencyContact) studentRecord.emergency_contact = studentData.emergencyContact;
      if (studentData.medicalInfo) studentRecord.medical_info = studentData.medicalInfo;

      const { data, error } = await supabase
        .from('students')
        .update(studentRecord)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Supabase students update error:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in studentsService.update:', error);
      throw error;
    }
  },

  async delete(id: string) {
    try {
      const { data, error } = await supabase
        .from('students')
        .update({ is_active: false })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Supabase students delete error:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in studentsService.delete:', error);
      throw error;
    }
  },

  async checkRollNumberExists(schoolId: string, rollNumber: string, excludeId?: string) {
    let query = supabase
      .from('students')
      .select('id')
      .eq('school_id', schoolId)
      .eq('roll_number', rollNumber)
      .eq('is_active', true);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data && data.length > 0;
  },

  async checkStudentIdExists(schoolId: string, studentId: string, excludeId?: string) {
    let query = supabase
      .from('students')
      .select('id')
      .eq('school_id', schoolId)
      .eq('student_id', studentId)
      .eq('is_active', true);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data && data.length > 0;
  }
};

// Teachers Service
export const teachersService = {
  async getAll(schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('teachers')
        .select(`
          *,
          profile:profiles(*),
          department:departments(name)
        `)
        .eq('school_id', schoolId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase teachers error:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in teachersService.getAll:', error);
      throw error;
    }
  },



  async getById(id: string) {
    const { data, error } = await supabase
      .from('teachers')
      .select(`
        *,
        profile:profiles(*),
        department:departments(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async create(teacherData: any, profileData: any, schoolId: string, password: string) {
    // Create a teacher record with both teacher and profile data
    const teacherRecord = {
      school_id: schoolId,
      employee_id: teacherData.employee_id,
      hire_date: teacherData.hire_date,
      experience_years: teacherData.experience_years || 0,
      qualification: teacherData.qualification || '',
      department_id: teacherData.department_id || null,
      salary: teacherData.salary || null,
      // Include profile data directly
      first_name: profileData.first_name,
      last_name: profileData.last_name,
      email: profileData.email,
      phone: profileData.phone,
      is_active: true
    };

    const { data, error } = await supabase
      .from('teachers')
      .insert(teacherRecord)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async update(id: string, teacherData: any, profileData?: any) {
    // Combine teacher data and profile data into a single update
    const updateData = {
      ...teacherData,
      ...(profileData && {
        first_name: profileData.first_name,
        last_name: profileData.last_name,
        email: profileData.email,
        phone: profileData.phone,
      })
    };

    const { data, error } = await supabase
      .from('teachers')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { data, error } = await supabase
      .from('teachers')
      .update({ is_active: false })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Classes Service
export const classesService = {
  async getAll(schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('classes')
        .select(`
          *,
          student_count:students(count)
        `)
        .eq('school_id', schoolId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase classes error:', error);
        throw error;
      }

      // Transform the data to include student count as a number
      const transformedData = data?.map(cls => ({
        ...cls,
        current_students: cls.student_count?.[0]?.count || 0
      })) || [];

      return transformedData;
    } catch (error) {
      console.error('Error in classesService.getAll:', error);
      throw error;
    }
  },



  async getById(id: string) {
    const { data, error } = await supabase
      .from('classes')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async create(classData: any, schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('classes')
        .insert({ ...classData, school_id: schoolId })
        .select()
        .single();

      if (error) {
        console.error('Supabase create class error:', error);

        // Provide specific error messages for common issues
        if (error.code === '42501') {
          throw new Error('Permission denied: Row Level Security policy prevents class creation. Please check your database permissions.');
        } else if (error.code === '23505') {
          throw new Error('Class already exists with this name and section.');
        } else {
          throw new Error(`Database error: ${error.message}`);
        }
      }

      return data;
    } catch (error) {
      console.error('Error in classesService.create:', error);
      throw error;
    }
  },

  async update(id: string, classData: any) {
    const { data, error } = await supabase
      .from('classes')
      .update(classData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { data, error } = await supabase
      .from('classes')
      .update({ is_active: false })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};



// Subjects Service
export const subjectsService = {
  async getAll(schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('subjects')
        .select('*')
        .eq('school_id', schoolId)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        console.error('Supabase subjects error:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in subjectsService.getAll:', error);
      throw error;
    }
  },



  async getById(id: string) {
    const { data, error } = await supabase
      .from('subjects')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async create(subjectData: any, schoolId: string) {
    try {
      // Prepare the insert data with only the fields that exist in the database
      const insertData: any = {
        name: subjectData.name,
        school_id: schoolId,
        is_active: true
      };

      // Add department if provided (this will work once the column is added)
      if (subjectData.department) {
        insertData.department = subjectData.department;
      }

      const { data, error } = await supabase
        .from('subjects')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Supabase create subject error:', error);
        throw new Error(`Failed to create subject: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('Error in subjectsService.create:', error);
      throw error;
    }
  },

  async update(id: string, subjectData: any) {
    // Prepare the update data with only the fields that should be updated
    const updateData: any = {};

    if (subjectData.name) {
      updateData.name = subjectData.name;
    }

    if (subjectData.department !== undefined) {
      updateData.department = subjectData.department;
    }

    const { data, error } = await supabase
      .from('subjects')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { data, error } = await supabase
      .from('subjects')
      .update({ is_active: false })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Attendance Service
export const attendanceService = {
  async getByDateAndClass(schoolId: string, date: string, classId?: string) {
    let query = supabase
      .from('attendance')
      .select(`
        *,
        student:students(
          student_id,
          profile:profiles(first_name, last_name)
        ),
        class:classes(name, grade_level, section)
      `)
      .eq('school_id', schoolId)
      .eq('date', date);

    if (classId) {
      query = query.eq('class_id', classId);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async markAttendance(attendanceData: any, schoolId: string) {
    const { data, error } = await supabase
      .from('attendance')
      .upsert({ ...attendanceData, school_id: schoolId })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getStudentAttendance(studentId: string, startDate: string, endDate: string) {
    const { data, error } = await supabase
      .from('attendance')
      .select('*')
      .eq('student_id', studentId)
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date', { ascending: false });

    if (error) throw error;
    return data;
  }
};

// Assignments Service
export const assignmentsService = {
  async getAll(schoolId: string) {
    const { data, error } = await supabase
      .from('assignments')
      .select(`
        *,
        class:classes(name, grade_level, section),
        subject:subjects(name, code),
        teacher:teachers(
          profile:profiles(first_name, last_name)
        )
      `)
      .eq('school_id', schoolId)
      .eq('is_active', true)
      .order('due_date', { ascending: false });

    if (error) throw error;
    return data;
  },

  async create(assignmentData: any, schoolId: string) {
    const { data, error } = await supabase
      .from('assignments')
      .insert({ ...assignmentData, school_id: schoolId })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async update(id: string, assignmentData: any) {
    const { data, error } = await supabase
      .from('assignments')
      .update(assignmentData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Fees Service
export const feesService = {
  async getFeeStructures(schoolId: string) {
    const { data, error } = await supabase
      .from('fee_structures')
      .select(`
        *,
        class:classes(name, grade_level, section)
      `)
      .eq('school_id', schoolId)
      .eq('is_active', true)
      .order('academic_year', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getFeePayments(schoolId: string) {
    const { data, error } = await supabase
      .from('fee_payments')
      .select(`
        *,
        student:students(
          student_id,
          profile:profiles(first_name, last_name)
        ),
        fee_structure:fee_structures(fee_type, amount)
      `)
      .eq('school_id', schoolId)
      .order('payment_date', { ascending: false });

    if (error) throw error;
    return data;
  },

  async createPayment(paymentData: any, schoolId: string) {
    const { data, error } = await supabase
      .from('fee_payments')
      .insert({ ...paymentData, school_id: schoolId })
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Communications Service
export const communicationsService = {
  async getAll(schoolId: string) {
    const { data, error } = await supabase
      .from('communications')
      .select(`
        *,
        sender:profiles!sender_id(first_name, last_name),
        recipient:profiles!recipient_id(first_name, last_name),
        recipient_class:classes!recipient_class_id(name, grade_level, section)
      `)
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async create(messageData: any, schoolId: string) {
    const { data, error } = await supabase
      .from('communications')
      .insert({ ...messageData, school_id: schoolId })
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Notifications Service
export const notificationsService = {
  async getAll(schoolId: string) {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async create(notificationData: any, schoolId: string) {
    const { data, error } = await supabase
      .from('notifications')
      .insert({ ...notificationData, school_id: schoolId })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async markAsRead(id: string) {
    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Departments Service
export const departmentsService = {
  async getAll(schoolId: string) {
    const { data, error } = await supabase
      .from('departments')
      .select('*')
      .eq('school_id', schoolId)
      .eq('is_active', true)
      .order('name', { ascending: true });

    if (error) throw error;
    return data;
  },

  async create(departmentData: any, schoolId: string) {
    const { data, error } = await supabase
      .from('departments')
      .insert({ ...departmentData, school_id: schoolId })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async update(id: string, departmentData: any) {
    const { data, error } = await supabase
      .from('departments')
      .update(departmentData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { data, error } = await supabase
      .from('departments')
      .update({ is_active: false })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Parents Service
export const parentsService = {
  async getAll(schoolId: string) {
    const { data, error } = await supabase
      .from('parents')
      .select(`
        *,
        profile:profiles(*),
        children:students(
          id,
          student_id,
          profile:profiles(first_name, last_name),
          class:classes(name, grade_level, section)
        )
      `)
      .eq('school_id', schoolId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('parents')
      .select(`
        *,
        profile:profiles(*),
        children:students(
          id,
          student_id,
          profile:profiles(*),
          class:classes(*)
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async create(parentData: any, schoolId: string) {
    const { data, error } = await supabase
      .from('parents')
      .insert({ ...parentData, school_id: schoolId })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async update(id: string, parentData: any) {
    const { data, error } = await supabase
      .from('parents')
      .update(parentData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { data, error } = await supabase
      .from('parents')
      .update({ is_active: false })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Examinations Service
export const examinationsService = {
  async getAll(schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('examinations')
        .select('*')
        .eq('school_id', schoolId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase examinations getAll error:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in examinationsService.getAll:', error);
      throw error;
    }
  },



  async getById(id: string) {
    try {
      const { data, error } = await supabase
        .from('examinations')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Supabase examinations getById error:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in examinationsService.getById:', error);
      throw error;
    }
  },

  async create(examData: any, schoolId: string) {
    try {
      const dbData = {
        name: examData.name,
        description: examData.description || '',
        subject_id: examData.subjectId || null,
        class_id: examData.classId || null,
        exam_type: examData.examType || 'Regular',
        academic_year: examData.academicYear || '2024-25',
        start_date: examData.startDate,
        end_date: examData.endDate || examData.startDate,
        start_time: examData.startTime || null,
        end_time: examData.endTime || null,
        total_marks: examData.totalMarks || null,
        passing_marks: examData.passingMarks || null,
        room: examData.room || null,
        instructions: examData.instructions || null,
        school_id: schoolId,
        is_active: true
      };

      console.log('📋 Creating examination with data:', dbData);

      const { data, error } = await supabase
        .from('examinations')
        .insert(dbData)
        .select()
        .single();

      if (error) {
        console.error('Supabase examinations create error:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error creating examination:', error);
      throw error;
    }
  },

  async update(id: string, examData: any) {
    try {
      const dbData = {
        name: examData.name,
        description: examData.description,
        subject_id: examData.subjectId,
        class_id: examData.classId,
        exam_type: examData.examType,
        academic_year: examData.academicYear || '2024-25',
        start_date: examData.startDate,
        end_date: examData.endDate,
        start_time: examData.startTime,
        end_time: examData.endTime,
        total_marks: examData.totalMarks,
        passing_marks: examData.passingMarks,
        room: examData.room,
        instructions: examData.instructions
      };

      const { data, error } = await supabase
        .from('examinations')
        .update(dbData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Supabase examinations update error:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in examinationsService.update:', error);
      throw error;
    }
  },

  async delete(id: string) {
    try {
      const { data, error } = await supabase
        .from('examinations')
        .update({ is_active: false })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Supabase examinations delete error:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in examinationsService.delete:', error);
      throw error;
    }
  }
};

// Results Service
export const resultsService = {
  async getAll(schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('results')
        .select(`
          *,
          student:students(id, student_id, first_name, last_name),
          exam_subject:exam_subjects(
            id,
            examination:examinations(id, name),
            subject:subjects(id, name),
            class:classes(id, name, section)
          )
        `)
        .eq('school_id', schoolId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase results error:', error);
        throw error;
      }
      return data || [];
    } catch (error) {
      console.error('Error in resultsService.getAll:', error);
      throw error;
    }
  },

  async getByExamination(schoolId: string, examinationId: string) {
    try {
      const { data, error } = await supabase
        .from('results')
        .select(`
          *,
          student:students(id, student_id, first_name, last_name),
          exam_subject:exam_subjects(
            id,
            examination:examinations(id, name),
            subject:subjects(id, name)
          )
        `)
        .eq('school_id', schoolId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error getting results by examination:', error);
        throw error;
      }
      return data || [];
    } catch (error) {
      console.error('Error in resultsService.getByExamination:', error);
      throw error;
    }
  },

  async create(resultData: any, schoolId: string) {
    try {
      // The form sends: studentId, examId, subject, marksObtained, etc.
      // But we need: student_id, exam_subject_id, marks_obtained, etc.

      // First, we need to find or create the exam_subject record
      const { data: examSubject, error: examSubjectError } = await supabase
        .from('exam_subjects')
        .select('id')
        .eq('examination_id', resultData.examId)
        .eq('subject_id', resultData.subject)
        .single();

      let examSubjectId = examSubject?.id;

      if (!examSubjectId) {
        // Create exam_subject if it doesn't exist
        // We need to provide all required fields
        const examSubjectData = {
          school_id: schoolId,
          examination_id: resultData.examId,
          subject_id: resultData.subject,
          class_id: resultData.classId || resultData.studentClassId, // Get from student's class
          exam_date: resultData.examDate || new Date().toISOString().split('T')[0], // Default to today
          start_time: '09:00:00', // Default start time
          end_time: '11:00:00',   // Default end time
          max_marks: resultData.totalMarks || 100
        };

        console.log('📋 Creating exam_subject with data:', examSubjectData);

        const { data: newExamSubject, error: createError } = await supabase
          .from('exam_subjects')
          .insert(examSubjectData)
          .select('id')
          .single();

        if (createError) {
          console.error('Error creating exam_subject:', createError);
          throw createError;
        }
        examSubjectId = newExamSubject.id;
      }

      // Now create the result
      const dbData = {
        school_id: schoolId,
        student_id: resultData.studentId,
        exam_subject_id: examSubjectId,
        marks_obtained: resultData.marksObtained,
        grade: resultData.grade || null,
        remarks: resultData.remarks || null
      };

      const { data, error } = await supabase
        .from('results')
        .insert(dbData)
        .select()
        .single();

      if (error) {
        console.error('Error creating result:', error);
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Error in resultsService.create:', error);
      throw error;
    }
  },

  async update(id: string, resultData: any) {
    try {
      const dbData = {
        marks_obtained: resultData.marksObtained,
        grade: resultData.grade,
        remarks: resultData.remarks
      };

      const { data, error } = await supabase
        .from('results')
        .update(dbData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating result:', error);
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Error in resultsService.update:', error);
      throw error;
    }
  },

  async delete(id: string) {
    try {
      const { data, error } = await supabase
        .from('results')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting result:', error);
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Error in resultsService.delete:', error);
      throw error;
    }
  }
};

// Teacher Classes Service
export const teacherClassesService = {
  async getByTeacher(schoolId: string, teacherId: string) {
    try {
      const { data, error } = await supabase
        .from('teacher_classes')
        .select(`
          *,
          class:classes(id, name, section, grade_level)
        `)
        .eq('school_id', schoolId)
        .eq('teacher_id', teacherId)
        .eq('is_active', true);

      if (error) {
        console.error('Error getting teacher classes:', error);
        throw error;
      }
      return data || [];
    } catch (error) {
      console.error('Error in teacherClassesService.getByTeacher:', error);
      throw error;
    }
  },

  async assignClasses(schoolId: string, teacherId: string, classIds: string[]) {
    try {
      // First, remove existing assignments for this teacher
      await supabase
        .from('teacher_classes')
        .delete()
        .eq('school_id', schoolId)
        .eq('teacher_id', teacherId);

      // Then add new assignments
      if (classIds.length > 0) {
        const assignments = classIds.map(classId => ({
          school_id: schoolId,
          teacher_id: teacherId,
          class_id: classId,
          academic_year: '2024-25',
          is_active: true
        }));

        const { data, error } = await supabase
          .from('teacher_classes')
          .insert(assignments);

        if (error) {
          console.error('Error assigning classes:', error);
          throw error;
        }
      }

      return true;
    } catch (error) {
      console.error('Error in teacherClassesService.assignClasses:', error);
      throw error;
    }
  },

  async getAllAssignments(schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('teacher_classes')
        .select(`
          *,
          teacher:teachers(id, first_name, last_name),
          class:classes(id, name, section, grade_level)
        `)
        .eq('school_id', schoolId)
        .eq('is_active', true);

      if (error) {
        console.error('Error getting all teacher class assignments:', error);
        throw error;
      }
      return data || [];
    } catch (error) {
      console.error('Error in teacherClassesService.getAllAssignments:', error);
      throw error;
    }
  }
};

// Timetable Service
export const timetableService = {
  async getAll(schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('timetable')
        .select('*')
        .eq('school_id', schoolId)
        .order('day_of_week', { ascending: true })
        .order('period_number', { ascending: true });

      if (error) {
        console.warn('Timetable table not available:', error);
        return []; // Return empty array if table doesn't exist
      }
      return data || [];
    } catch (error) {
      console.warn('Timetable service error:', error);
      return []; // Return empty array on any error
    }
  },

  async getByClass(schoolId: string, classId: string) {
    try {
      const { data, error } = await supabase
        .from('timetable')
        .select(`
          *,
          subject:subjects(id, name, code),
          teacher:teachers(id, first_name, last_name),
          class:classes(id, name, section)
        `)
        .eq('school_id', schoolId)
        .eq('class_id', classId)
        .eq('is_active', true)
        .order('day_of_week', { ascending: true })
        .order('start_time', { ascending: true });

      if (error) {
        console.warn('Timetable getByClass error:', error);
        return []; // Return empty array if table doesn't exist
      }
      return data || [];
    } catch (error) {
      console.warn('Timetable getByClass service error:', error);
      return []; // Return empty array on any error
    }
  },

  async create(timetableData: any, schoolId: string) {
    try {
      const { data, error } = await supabase
        .from('timetable')
        .insert({ ...timetableData, school_id: schoolId })
        .select()
        .single();

      if (error) {
        console.error('Supabase create timetable error:', error);
        throw new Error(`Failed to create timetable: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('Error in timetableService.create:', error);
      throw error;
    }
  },

  async update(id: string, timetableData: any) {
    const { data, error } = await supabase
      .from('timetable')
      .update(timetableData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { data, error } = await supabase
      .from('timetable')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return data;
  }
};

// Teacher Class Assignment Service
export const teacherClassAssignmentService = {
  // Get classes assigned to a teacher
  getTeacherClasses(teacherId: string): string[] {
    try {
      const assignments = JSON.parse(localStorage.getItem('teacherClassAssignments') || '{}');
      return assignments[teacherId] || [];
    } catch (error) {
      console.error('Error getting teacher classes:', error);
      return [];
    }
  },

  // Get teachers assigned to a class
  getClassTeachers(classId: string): string[] {
    try {
      const assignments = JSON.parse(localStorage.getItem('teacherClassAssignments') || '{}');
      const teacherIds: string[] = [];

      Object.entries(assignments).forEach(([teacherId, classIds]) => {
        if (Array.isArray(classIds) && classIds.includes(classId)) {
          teacherIds.push(teacherId);
        }
      });

      return teacherIds;
    } catch (error) {
      console.error('Error getting class teachers:', error);
      return [];
    }
  },

  // Check if a teacher is assigned to a class
  isTeacherAssignedToClass(teacherId: string, classId: string): boolean {
    const teacherClasses = this.getTeacherClasses(teacherId);
    return teacherClasses.includes(classId);
  },

  // Assign classes to a teacher
  assignClassesToTeacher(teacherId: string, classIds: string[]): boolean {
    try {
      const assignments = JSON.parse(localStorage.getItem('teacherClassAssignments') || '{}');
      assignments[teacherId] = classIds;
      localStorage.setItem('teacherClassAssignments', JSON.stringify(assignments));
      return true;
    } catch (error) {
      console.error('Error assigning classes to teacher:', error);
      return false;
    }
  },

  // Remove a teacher from a class
  removeTeacherFromClass(teacherId: string, classId: string): boolean {
    try {
      const assignments = JSON.parse(localStorage.getItem('teacherClassAssignments') || '{}');
      if (assignments[teacherId]) {
        assignments[teacherId] = assignments[teacherId].filter((id: string) => id !== classId);
        localStorage.setItem('teacherClassAssignments', JSON.stringify(assignments));
      }
      return true;
    } catch (error) {
      console.error('Error removing teacher from class:', error);
      return false;
    }
  }
};
