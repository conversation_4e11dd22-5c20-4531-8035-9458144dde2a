import { supabase, supabaseAdmin } from '@/lib/supabase';
import { MultiTenantService } from './multiTenantService';

// Fee Service
export class FeeService extends MultiTenantService {
  // Create fee structure
  static async createFee(feeData: {
    class_id?: string;
    student_id?: string;
    fee_type: string;
    amount: number;
    due_date: string;
    description?: string;
    is_recurring?: boolean;
    recurrence_period?: string;
  }) {
    return this.withSchoolContext(async (schoolId, userId) => {
      const currentYear = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', schoolId)
        .eq('is_current', true)
        .single();

      const { data, error } = await supabaseAdmin
        .from('fees')
        .insert({
          ...feeData,
          school_id: schoolId,
          academic_year_id: currentYear.data?.id,
          created_by: userId,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }

  // Get all fees
  static async getAllFees() {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('fees')
        .select(`
          *,
          academic_year:academic_years(name),
          class:classes(name, grade_level),
          student:students(student_id, first_name, last_name),
          created_by_user:users!created_by(first_name, last_name),
          fee_payments(
            amount_paid,
            payment_date
          )
        `)
        .eq('school_id', schoolId)
        .order('due_date', { ascending: true });

      if (error) throw error;

      // Calculate payment status for each fee
      return data.map(fee => {
        const totalPaid = fee.fee_payments.reduce((sum, payment) => sum + payment.amount_paid, 0);
        const balance = fee.amount - totalPaid;
        const status = balance <= 0 ? 'paid' : new Date(fee.due_date) < new Date() ? 'overdue' : 'pending';
        
        return {
          ...fee,
          total_paid: totalPaid,
          balance,
          status
        };
      });
    });
  }

  // Get fees for a specific student
  static async getStudentFees(studentId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('fees')
        .select(`
          *,
          academic_year:academic_years(name),
          fee_payments(
            id,
            amount_paid,
            payment_date,
            payment_method,
            receipt_number,
            collected_by_user:users!collected_by(first_name, last_name)
          )
        `)
        .eq('school_id', schoolId)
        .eq('student_id', studentId)
        .order('due_date', { ascending: true });

      if (error) throw error;

      // Calculate payment status for each fee
      return data.map(fee => {
        const totalPaid = fee.fee_payments.reduce((sum, payment) => sum + payment.amount_paid, 0);
        const balance = fee.amount - totalPaid;
        const status = balance <= 0 ? 'paid' : new Date(fee.due_date) < new Date() ? 'overdue' : 'pending';
        
        return {
          ...fee,
          total_paid: totalPaid,
          balance,
          status
        };
      });
    });
  }

  // Get fees for parent's children
  static async getParentChildrenFees() {
    return this.withSchoolContext(async (schoolId, userId) => {
      // First get parent's children
      const { data: parentStudents, error: psError } = await supabase
        .from('parent_students')
        .select(`
          student:students(
            id,
            student_id,
            first_name,
            last_name,
            class:classes(name)
          )
        `)
        .eq('parent_id', userId);

      if (psError || !parentStudents) throw psError;

      const studentIds = parentStudents.map(ps => ps.student.id);
      if (studentIds.length === 0) return [];

      // Get fees for all children
      const { data, error } = await supabase
        .from('fees')
        .select(`
          *,
          academic_year:academic_years(name),
          student:students(student_id, first_name, last_name, class:classes(name)),
          fee_payments(
            amount_paid,
            payment_date,
            payment_method,
            receipt_number
          )
        `)
        .eq('school_id', schoolId)
        .in('student_id', studentIds)
        .order('due_date', { ascending: true });

      if (error) throw error;

      // Calculate payment status for each fee
      return data.map(fee => {
        const totalPaid = fee.fee_payments.reduce((sum, payment) => sum + payment.amount_paid, 0);
        const balance = fee.amount - totalPaid;
        const status = balance <= 0 ? 'paid' : new Date(fee.due_date) < new Date() ? 'overdue' : 'pending';
        
        return {
          ...fee,
          total_paid: totalPaid,
          balance,
          status
        };
      });
    });
  }

  // Record fee payment
  static async recordPayment(paymentData: {
    fee_id: string;
    student_id: string;
    amount_paid: number;
    payment_date: string;
    payment_method?: string;
    transaction_id?: string;
    receipt_number?: string;
    remarks?: string;
  }) {
    return this.withSchoolContext(async (schoolId, userId) => {
      const { data, error } = await supabaseAdmin
        .from('fee_payments')
        .insert({
          ...paymentData,
          school_id: schoolId,
          collected_by: userId,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }

  // Get fee collection summary
  static async getFeeCollectionSummary(dateRange?: { start: string; end: string }) {
    return this.withSchoolContext(async (schoolId) => {
      let query = supabase
        .from('fee_payments')
        .select(`
          amount_paid,
          payment_date,
          payment_method,
          fee:fees(fee_type, amount)
        `)
        .eq('school_id', schoolId);

      if (dateRange) {
        query = query
          .gte('payment_date', dateRange.start)
          .lte('payment_date', dateRange.end);
      }

      const { data, error } = await query.order('payment_date', { ascending: false });

      if (error) throw error;

      // Calculate summary statistics
      const totalCollected = data.reduce((sum, payment) => sum + payment.amount_paid, 0);
      const paymentsByMethod = data.reduce((acc, payment) => {
        const method = payment.payment_method || 'cash';
        acc[method] = (acc[method] || 0) + payment.amount_paid;
        return acc;
      }, {} as Record<string, number>);

      const paymentsByType = data.reduce((acc, payment) => {
        const type = payment.fee.fee_type;
        acc[type] = (acc[type] || 0) + payment.amount_paid;
        return acc;
      }, {} as Record<string, number>);

      return {
        payments: data,
        summary: {
          totalCollected,
          totalPayments: data.length,
          paymentsByMethod,
          paymentsByType
        }
      };
    });
  }

  // Get outstanding fees summary
  static async getOutstandingFeesSummary() {
    return this.withSchoolContext(async (schoolId) => {
      const { data: fees, error } = await supabase
        .from('fees')
        .select(`
          *,
          student:students(student_id, first_name, last_name, class:classes(name)),
          fee_payments(amount_paid)
        `)
        .eq('school_id', schoolId);

      if (error) throw error;

      const outstandingFees = fees
        .map(fee => {
          const totalPaid = fee.fee_payments.reduce((sum, payment) => sum + payment.amount_paid, 0);
          const balance = fee.amount - totalPaid;
          const isOverdue = new Date(fee.due_date) < new Date();
          
          return {
            ...fee,
            total_paid: totalPaid,
            balance,
            is_overdue: isOverdue && balance > 0
          };
        })
        .filter(fee => fee.balance > 0);

      const totalOutstanding = outstandingFees.reduce((sum, fee) => sum + fee.balance, 0);
      const overdueAmount = outstandingFees
        .filter(fee => fee.is_overdue)
        .reduce((sum, fee) => sum + fee.balance, 0);

      return {
        outstandingFees,
        summary: {
          totalOutstanding,
          overdueAmount,
          totalStudentsWithDues: new Set(outstandingFees.map(fee => fee.student_id)).size,
          overdueCount: outstandingFees.filter(fee => fee.is_overdue).length
        }
      };
    });
  }
}
