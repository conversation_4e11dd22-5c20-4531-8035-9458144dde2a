import { supabase } from '@/integrations/supabase/client';
import { MultiTenantService } from './multiTenantService';

// Timetable Service
export class TimetableService extends MultiTenantService {
  // Create timetable entry
  static async createTimetableEntry(timetableData: {
    class_id: string;
    subject_id: string;
    teacher_id: string;
    day_of_week: number; // 1-7 (Monday-Sunday)
    period_number: number;
    start_time: string;
    end_time: string;
    room_number?: string;
  }) {
    return this.withSchoolContext(async (schoolId) => {
      const currentYear = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', schoolId)
        .eq('is_current', true)
        .single();

      const { data, error } = await supabaseAdmin
        .from('timetable')
        .insert({
          ...timetableData,
          school_id: schoolId,
          academic_year_id: currentYear.data?.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }

  // Get timetable for a class
  static async getClassTimetable(classId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('timetable')
        .select(`
          *,
          subject:subjects(name, code),
          teacher:teachers(
            users(first_name, last_name)
          ),
          class:classes(name, grade_level, section)
        `)
        .eq('school_id', schoolId)
        .eq('class_id', classId)
        .order('day_of_week')
        .order('period_number');

      if (error) throw error;

      // Group by day of week
      const timetableByDay = data.reduce((acc, entry) => {
        const day = entry.day_of_week;
        if (!acc[day]) acc[day] = [];
        acc[day].push(entry);
        return acc;
      }, {} as Record<number, any[]>);

      return {
        entries: data,
        byDay: timetableByDay
      };
    });
  }

  // Get teacher's timetable
  static async getTeacherTimetable(teacherId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('timetable')
        .select(`
          *,
          subject:subjects(name, code),
          class:classes(name, grade_level, section),
          academic_year:academic_years(name)
        `)
        .eq('school_id', schoolId)
        .eq('teacher_id', teacherId)
        .order('day_of_week')
        .order('period_number');

      if (error) throw error;

      // Group by day of week
      const timetableByDay = data.reduce((acc, entry) => {
        const day = entry.day_of_week;
        if (!acc[day]) acc[day] = [];
        acc[day].push(entry);
        return acc;
      }, {} as Record<number, any[]>);

      return {
        entries: data,
        byDay: timetableByDay
      };
    });
  }

  // Get current user's timetable (for teachers)
  static async getMyTimetable() {
    return this.withSchoolContext(async (schoolId, userId, role) => {
      if (role !== 'teacher') {
        throw new Error('Only teachers can access their timetable');
      }

      return this.getTeacherTimetable(userId);
    });
  }

  // Get timetable for parent's children
  static async getChildrenTimetables() {
    return this.withSchoolContext(async (schoolId, userId, role) => {
      if (role !== 'parent') {
        throw new Error('Only parents can access children timetables');
      }

      // Get parent's children
      const { data: parentStudents, error: psError } = await supabase
        .from('parent_students')
        .select(`
          student:students(
            id,
            student_id,
            first_name,
            last_name,
            class_id,
            class:classes(name, grade_level, section)
          )
        `)
        .eq('parent_id', userId);

      if (psError || !parentStudents) throw psError;

      // Get timetables for all children's classes
      const timetables = await Promise.all(
        parentStudents.map(async (ps) => {
          const timetable = await this.getClassTimetable(ps.student.class_id);
          return {
            student: ps.student,
            timetable
          };
        })
      );

      return timetables;
    });
  }

  // Update timetable entry
  static async updateTimetableEntry(entryId: string, updates: {
    subject_id?: string;
    teacher_id?: string;
    start_time?: string;
    end_time?: string;
    room_number?: string;
  }) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabaseAdmin
        .from('timetable')
        .update(updates)
        .eq('id', entryId)
        .eq('school_id', schoolId)
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }

  // Delete timetable entry
  static async deleteTimetableEntry(entryId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabaseAdmin
        .from('timetable')
        .delete()
        .eq('id', entryId)
        .eq('school_id', schoolId)
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }

  // Bulk create timetable for a class
  static async createClassTimetable(classId: string, timetableEntries: Array<{
    subject_id: string;
    teacher_id: string;
    day_of_week: number;
    period_number: number;
    start_time: string;
    end_time: string;
    room_number?: string;
  }>) {
    return this.withSchoolContext(async (schoolId) => {
      const currentYear = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', schoolId)
        .eq('is_current', true)
        .single();

      const entries = timetableEntries.map(entry => ({
        ...entry,
        class_id: classId,
        school_id: schoolId,
        academic_year_id: currentYear.data?.id,
      }));

      const { data, error } = await supabaseAdmin
        .from('timetable')
        .insert(entries)
        .select();

      if (error) throw error;
      return data;
    });
  }

  // Get today's schedule for a class
  static async getTodaySchedule(classId: string) {
    const today = new Date().getDay(); // 0 = Sunday, 1 = Monday, etc.
    const dayOfWeek = today === 0 ? 7 : today; // Convert to 1-7 format

    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('timetable')
        .select(`
          *,
          subject:subjects(name, code),
          teacher:teachers(
            users(first_name, last_name)
          )
        `)
        .eq('school_id', schoolId)
        .eq('class_id', classId)
        .eq('day_of_week', dayOfWeek)
        .order('period_number');

      if (error) throw error;
      return data;
    });
  }

  // Get teacher's today schedule
  static async getTeacherTodaySchedule(teacherId: string) {
    const today = new Date().getDay();
    const dayOfWeek = today === 0 ? 7 : today;

    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('timetable')
        .select(`
          *,
          subject:subjects(name, code),
          class:classes(name, grade_level, section)
        `)
        .eq('school_id', schoolId)
        .eq('teacher_id', teacherId)
        .eq('day_of_week', dayOfWeek)
        .order('period_number');

      if (error) throw error;
      return data;
    });
  }

  // Helper function to get day name
  static getDayName(dayOfWeek: number): string {
    const days = ['', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[dayOfWeek] || '';
  }

  // Helper function to format time
  static formatTime(time: string): string {
    return new Date(`1970-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }
}
