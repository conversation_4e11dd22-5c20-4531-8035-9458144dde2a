
import { User, Student, Teacher, Assignment, Class, Result } from '@/types';

// API Response type
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

// Utility function to simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Generate unique IDs
const generateId = () => Math.random().toString(36).substr(2, 9);

// Mock data storage
let usersData: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    username: 'teacher',
    email: '<EMAIL>',
    role: 'teacher',
    firstName: 'John',
    lastName: 'Teacher',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

let studentsData: Student[] = [
  {
    id: '1',
    username: 'student1',
    email: '<EMAIL>',
    role: 'student',
    firstName: 'John',
    lastName: 'Smith',
    isActive: true,
    studentId: '8A001',
    class: '8-A',
    section: 'A',
    rollNumber: '001',
    dateOfBirth: '2010-05-15',
    phone: '************',
    address: '123 Main St, City',
    parentId: '4',
    admissionDate: '2024-01-01',
    emergencyContact: 'Jane Smith - ************',
    bloodGroup: 'O+',
    medicalInfo: 'No known allergies',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

let teachersData: Teacher[] = [
  {
    id: '2',
    username: 'teacher1',
    email: '<EMAIL>',
    role: 'teacher',
    firstName: 'Sarah',
    lastName: 'Williams',
    isActive: true,
    employeeId: 'T001',
    department: 'Science',
    subjects: ['Mathematics', 'Physics'],
    classes: ['10-A', '11-B'],
    qualifications: ['PhD Mathematics', 'M.Ed'],
    experience: 8,
    salary: 75000,
    joiningDate: '2020-08-15',
    phone: '************',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

let assignmentsData: Assignment[] = [
  {
    id: '1',
    title: 'Quadratic Equations Worksheet',
    description: 'Solve various quadratic equation problems using different methods.',
    subject: 'Mathematics',
    subjectId: 'math-1',
    class: '10-A',
    classId: 'class-1',
    teacherId: '2',
    dueDate: '2024-12-15',
    maxScore: 100,
    status: 'published',
    submissions: 28,
    totalStudents: 30,
    instructions: 'Show all working steps clearly.',
    attachments: [],
    createdAt: '2024-12-01T00:00:00Z'
  }
];

let classesData: Class[] = [
  {
    id: '1',
    name: 'Grade 10 Section A',
    section: 'A',
    grade: 10,
    teacherId: '2',
    subjects: ['Mathematics', 'Physics', 'Chemistry', 'English'],
    students: ['1'],
    capacity: 30,
    currentStrength: 28,
    classTeacherId: '2',
    room: 'Room 101',
    academicYear: '2024-2025',
    description: 'Advanced mathematics and science class',
    schedule: {
      startTime: '08:00',
      endTime: '15:00'
    },
    createdAt: '2024-09-01T00:00:00Z'
  }
];

let resultsData: Result[] = [
  {
    id: '1',
    studentId: '1',
    examId: '1',
    subject: 'Mathematics',
    marksObtained: 85,
    totalMarks: 100,
    grade: 'A',
    percentage: 85,
    examDate: '2024-12-01',
    createdAt: '2024-12-01T00:00:00Z'
  }
];

let periodsData: any[] = [
  {
    id: '1',
    subject: 'Mathematics',
    teacher: 'Dr. Sarah Williams',
    class: '10-A',
    room: 'Room 101',
    day: 'monday',
    startTime: '09:00',
    endTime: '09:45',
    type: 'regular'
  }
];

// Generic CRUD service creator
function createService<T extends { id: string }>(data: T[], entityName: string) {
  return {
    async getAll(): Promise<ApiResponse<T[]>> {
      await delay(500);
      return { success: true, data };
    },

    async getAllClasses(): Promise<ApiResponse<T[]>> {
      await delay(500);
      return { success: true, data };
    },

    async getById(id: string): Promise<ApiResponse<T>> {
      await delay(300);
      const item = data.find(item => item.id === id);
      if (item) {
        return { success: true, data: item };
      }
      return { success: false, data: {} as T, error: `${entityName} not found` };
    },

    async create(itemData: Omit<T, 'id' | 'createdAt'>): Promise<ApiResponse<T>> {
      await delay(800);
      const newItem = {
        ...itemData,
        id: generateId(),
        createdAt: new Date().toISOString()
      } as T;
      data.push(newItem);
      return { success: true, data: newItem };
    },

    async update(id: string, itemData: Partial<T>): Promise<ApiResponse<T>> {
      await delay(600);
      const index = data.findIndex(item => item.id === id);
      if (index !== -1) {
        data[index] = { ...data[index], ...itemData };
        return { success: true, data: data[index] };
      }
      return { success: false, data: {} as T, error: `${entityName} not found` };
    },

    async delete(id: string): Promise<ApiResponse<boolean>> {
      await delay(400);
      const index = data.findIndex(item => item.id === id);
      if (index !== -1) {
        data.splice(index, 1);
        return { success: true, data: true };
      }
      return { success: false, data: false, error: `${entityName} not found` };
    }
  };
}

// Authentication service
export const authService = {
  async login(email: string, password: string): Promise<ApiResponse<User>> {
    await delay(1000);
    const user = usersData.find(u => u.email === email);
    if (user && user.isActive) {
      return { success: true, data: user };
    }
    return { success: false, data: {} as User, error: 'Invalid credentials' };
  },

  async logout(): Promise<ApiResponse<boolean>> {
    await delay(500);
    return { success: true, data: true };
  }
};

// Export all services
export const userService = createService(usersData, 'user');
export const studentService = createService(studentsData, 'student');
export const teacherService = createService(teachersData, 'teacher');
export const assignmentService = createService(assignmentsData, 'assignment');
export const classService = createService(classesData, 'class');
export const resultService = createService(resultsData, 'result');
export const periodService = createService(periodsData, 'period');
