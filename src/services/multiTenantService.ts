
import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';

// Type aliases for better readability
type Tables = Database['public']['Tables'];
type UserRole = Database['public']['Enums']['user_role'];

export interface SchoolProfile {
  id: string;
  school_id: string;
  role: UserRole;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  date_of_birth?: string;
  gender?: string;
  avatar_url?: string;
  is_active: boolean;
  metadata?: any;
}

export interface School {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  logo?: string;
  address: string;
  phone?: string;
  email: string;
  website?: string;
  established_year?: number;
  principal_name?: string;
  total_students: number;
  total_teachers: number;
  settings: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SchoolRegistrationData {
  name: string;
  slug: string;
  address: string;
  email: string;
  phone?: string;
  website?: string;
  principal_name?: string;
  admin_first_name: string;
  admin_last_name: string;
  admin_email: string;
  admin_phone?: string;
}

export class MultiTenantService {
  // User Profile Management
  static async getCurrentUserProfile(): Promise<SchoolProfile | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return null;
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        return null;
      }

      return data as SchoolProfile;
    } catch (error) {
      return null;
    }
  }

  static async createUserProfile(
    userId: string,
    schoolId: string,
    role: UserRole,
    profileData: Partial<SchoolProfile>
  ): Promise<SchoolProfile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          school_id: schoolId,
          role,
          ...profileData
        })
        .select()
        .single();

      if (error) throw error;
      return data as SchoolProfile;
    } catch (error) {
      console.error('Error creating user profile:', error);
      return null;
    }
  }

  static async updateUserProfile(userId: string, updates: Partial<SchoolProfile>): Promise<SchoolProfile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data as SchoolProfile;
    } catch (error) {
      console.error('Error updating user profile:', error);
      return null;
    }
  }

  static async getUsersBySchool(schoolId: string, role?: UserRole): Promise<SchoolProfile[]> {
    try {
      let query = supabase
        .from('profiles')
        .select('*')
        .eq('school_id', schoolId)
        .eq('is_active', true);

      if (role) {
        query = query.eq('role', role);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as SchoolProfile[];
    } catch (error) {
      console.error('Error fetching users by school:', error);
      return [];
    }
  }

  // School Management
  static async getSchoolBySlug(slug: string): Promise<School | null> {
    try {
      const { data, error } = await supabase
        .from('schools')
        .select('*')
        .eq('slug', slug)
        .eq('is_active', true)
        .single();

      if (error) return null;
      return data as School;
    } catch {
      return null;
    }
  }

  static async getSchoolById(id: string): Promise<School | null> {
    try {
      const { data, error } = await supabase
        .from('schools')
        .select('*')
        .eq('id', id)
        .single();

      if (error) return null;
      return data as School;
    } catch {
      return null;
    }
  }

  static async getAllSchools(): Promise<School[]> {
    try {
      const { data, error } = await supabase
        .from('schools')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data as School[];
    } catch (error) {
      console.error('Error fetching schools:', error);
      return [];
    }
  }

  static async updateSchool(schoolId: string, updates: Partial<School>): Promise<School | null> {
    try {
      const { data, error } = await supabase
        .from('schools')
        .update(updates)
        .eq('id', schoolId)
        .select()
        .single();

      if (error) throw error;
      return data as School;
    } catch (error) {
      console.error('Error updating school:', error);
      return null;
    }
  }

  // School Registration
  static async registerSchool(registrationData: SchoolRegistrationData): Promise<{ school: School } | null> {
    try {
      // Check if slug is available
      const existingSchool = await this.getSchoolBySlug(registrationData.slug);
      if (existingSchool) {
        throw new Error('School slug already exists');
      }

      // Create school
      const { data: schoolData, error: schoolError } = await supabase
        .from('schools')
        .insert({
          name: registrationData.name,
          slug: registrationData.slug,
          address: registrationData.address,
          email: registrationData.email,
          phone: registrationData.phone,
          website: registrationData.website,
          principal_name: registrationData.principal_name,
          settings: {
            theme: {
              primaryColor: '#3b82f6',
              secondaryColor: '#1e40af'
            },
            features: ['attendance', 'grades', 'assignments', 'communications'],
            admin_info: {
              first_name: registrationData.admin_first_name,
              last_name: registrationData.admin_last_name,
              email: registrationData.admin_email,
              phone: registrationData.admin_phone
            }
          }
        })
        .select()
        .single();

      if (schoolError) throw schoolError;

      const school = schoolData as School;

      return { school };
    } catch (error: any) {
      console.error('Error registering school:', error);

      // Log more details about the error
      if (error.message) {
        console.error('Error message:', error.message);
      }
      if (error.details) {
        console.error('Error details:', error.details);
      }
      if (error.hint) {
        console.error('Error hint:', error.hint);
      }

      return null;
    }
  }

  // Register school with admin account automatically
  static async registerSchoolWithAdmin(registrationData: SchoolRegistrationData): Promise<{ school: School; adminCredentials: { email: string; password: string } } | null> {
    try {
      console.log('Starting school registration with admin account...');

      // First create the school
      const schoolResult = await this.registerSchool(registrationData);
      if (!schoolResult) {
        throw new Error('Failed to create school');
      }

      const { school } = schoolResult;

      // Generate admin password
      const adminPassword = this.generateSecurePassword();

      // Create admin account
      const adminResult = await this.registerSchoolAdmin(school.slug, {
        email: registrationData.admin_email,
        password: adminPassword,
        first_name: registrationData.admin_first_name,
        last_name: registrationData.admin_last_name,
        phone: registrationData.admin_phone
      });

      if (!adminResult) {
        throw new Error('Failed to create admin account');
      }

      // Create demo users for the school
      await this.createDemoUsers(school.id, school.slug);

      return {
        school,
        adminCredentials: {
          email: registrationData.admin_email,
          password: adminPassword
        }
      };
    } catch (error: any) {
      console.error('Error registering school with admin:', error);
      return null;
    }
  }

  // Generate a secure password
  static generateSecurePassword(): string {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }

  // Create demo users for a school
  static async createDemoUsers(schoolId: string, schoolSlug: string): Promise<void> {
    try {
      const demoUsers = [
        {
          email: `teacher@${schoolSlug}.demo`,
          password: 'demo123',
          role: 'teacher',
          first_name: 'Demo',
          last_name: 'Teacher'
        },
        {
          email: `student@${schoolSlug}.demo`,
          password: 'demo123',
          role: 'student',
          first_name: 'Demo',
          last_name: 'Student'
        },
        {
          email: `parent@${schoolSlug}.demo`,
          password: 'demo123',
          role: 'parent',
          first_name: 'Demo',
          last_name: 'Parent'
        }
      ];

      for (const demoUser of demoUsers) {
        try {
          // Create auth user
          const { data: authData, error: authError } = await supabase.auth.signUp({
            email: demoUser.email,
            password: demoUser.password,
            options: {
              emailRedirectTo: `${window.location.origin}/${schoolSlug}/dashboard`
            }
          });

          if (authError) {
            console.warn(`Failed to create demo user ${demoUser.email}:`, authError.message);
            continue;
          }

          if (authData.user) {
            // Create profile
            await this.createUserProfile(authData.user.id, schoolId, demoUser.role as UserRole, {
              first_name: demoUser.first_name,
              last_name: demoUser.last_name,
              email: demoUser.email
            });
          }
        } catch (error) {
          console.warn(`Error creating demo user ${demoUser.email}:`, error);
        }
      }
    } catch (error) {
      console.error('Error creating demo users:', error);
    }
  }

  // Register admin for a school
  static async registerSchoolAdmin(
    schoolSlug: string,
    adminData: {
      email: string;
      password: string;
      first_name: string;
      last_name: string;
      phone?: string;
    }
  ): Promise<{ user: any; profile: SchoolProfile } | null> {
    try {
      // Get school by slug
      const school = await this.getSchoolBySlug(schoolSlug);
      if (!school) {
        throw new Error('School not found');
      }

      // Check if admin already exists (simplified check)
      try {
        const { data: existingAdmins, error } = await supabase
          .from('profiles')
          .select('id')
          .eq('school_id', school.id)
          .eq('role', 'admin')
          .limit(1);

        if (!error && existingAdmins && existingAdmins.length > 0) {
          throw new Error('School already has an admin');
        }
      } catch (error) {
        // If we can't check, proceed anyway (better than blocking registration)
        console.warn('Could not check for existing admin:', error);
      }

      // Create admin user account
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: adminData.email,
        password: adminData.password,
        options: {
          emailRedirectTo: `${window.location.origin}/${schoolSlug}/dashboard`
        }
      });

      if (authError) {
        console.error('Auth signup error:', authError);

        // If user already exists, that's actually okay - they might be trying to complete registration
        if (authError.message.includes('User already registered')) {
          throw new Error('An account with this email already exists. Please use the login page instead.');
        }

        throw new Error(`Failed to create user account: ${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('Failed to create admin user');
      }

      // Check if user was actually created and confirmed
      if (!authData.user.email_confirmed_at && !authData.user.confirmed_at) {
        console.warn('User created but not confirmed, this might cause issues');
      }

      // Wait a moment for the user to be properly created
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Use database function to create profile with proper permissions
      console.log('Creating profile for user:', authData.user.id);

      const { data: functionResult, error: functionError } = await supabase
        .rpc('create_admin_profile', {
          user_id: authData.user.id,
          school_id: school.id,
          first_name: adminData.first_name,
          last_name: adminData.last_name,
          email: adminData.email,
          phone: adminData.phone || null
        });

      if (functionError) {
        console.error('Profile creation function error:', functionError);
        throw new Error(`Failed to create admin profile: ${functionError.message}`);
      }

      if (functionResult?.error) {
        console.error('Profile creation error from function:', functionResult.error);
        throw new Error(`Failed to create admin profile: ${functionResult.error}`);
      }

      const profileData = functionResult?.profile;

      if (!profileData) {
        throw new Error('Failed to create admin profile: No profile data returned');
      }

      return { user: authData.user, profile: profileData as SchoolProfile };
    } catch (error) {
      console.error('Error registering school admin:', error);
      return null;
    }
  }

  // Utility Methods
  static async getCurrentUserSchoolId(): Promise<string | null> {
    const profile = await this.getCurrentUserProfile();
    return profile?.school_id || null;
  }

  static async isUserSuperAdmin(): Promise<boolean> {
    const profile = await this.getCurrentUserProfile();
    return profile?.role === 'super_admin';
  }

  static async validateSchoolAccess(schoolId: string): Promise<boolean> {
    const currentSchoolId = await this.getCurrentUserSchoolId();
    const isSuperAdmin = await this.isUserSuperAdmin();

    return isSuperAdmin || currentSchoolId === schoolId;
  }

  static async checkSlugAvailability(slug: string): Promise<boolean> {
    try {
      const school = await this.getSchoolBySlug(slug);
      return school === null;
    } catch (error) {
      console.error('Error checking slug availability:', error);
      // If there's an error, assume slug is not available to be safe
      return false;
    }
  }

  static generateSlug(schoolName: string): string {
    return schoolName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  // Check if admin exists for a school
  static async checkAdminExists(schoolId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('school_id', schoolId)
        .eq('role', 'admin')
        .limit(1);

      if (error) {
        console.error('Error checking admin existence:', error);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      console.error('Error in checkAdminExists:', error);
      return false;
    }
  }

  // Create a new user for a school
  static async createSchoolUser(
    schoolId: string,
    userData: {
      email: string;
      password: string;
      role: UserRole;
      first_name: string;
      last_name: string;
      phone?: string;
    }
  ): Promise<{ user: any; profile: SchoolProfile } | null> {
    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          emailRedirectTo: `${window.location.origin}/login`
        }
      });

      if (authError) {
        console.error('Auth signup error:', authError);
        throw new Error(`Failed to create user account: ${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('Failed to create user');
      }

      // Create profile
      const profile = await this.createUserProfile(authData.user.id, schoolId, userData.role, {
        first_name: userData.first_name,
        last_name: userData.last_name,
        email: userData.email,
        phone: userData.phone
      });

      if (!profile) {
        throw new Error('Failed to create user profile');
      }

      return {
        user: authData.user,
        profile
      };
    } catch (error: any) {
      console.error('Error creating school user:', error);
      return null;
    }
  }

  // Tenant-aware query helpers
  static createTenantQuery<T extends keyof Tables>(
    tableName: T,
    schoolId?: string
  ) {
    let query = supabase.from(tableName).select('*');

    if (schoolId) {
      query = query.eq('school_id', schoolId);
    }

    return query;
  }

  static async withTenantContext<T>(
    operation: (schoolId: string) => Promise<T>
  ): Promise<T | null> {
    try {
      const schoolId = await this.getCurrentUserSchoolId();
      if (!schoolId) {
        throw new Error('No school context available');
      }

      return await operation(schoolId);
    } catch (error) {
      console.error('Error in tenant context operation:', error);
      return null;
    }
  }

  // School Statistics
  static async getSchoolStats(schoolId: string) {
    try {
      const [studentsCount, teachersCount, classesCount, parentsCount] = await Promise.all([
        supabase.from('students').select('id', { count: 'exact' }).eq('school_id', schoolId).eq('is_active', true),
        supabase.from('teachers').select('id', { count: 'exact' }).eq('school_id', schoolId).eq('is_active', true),
        supabase.from('classes').select('id', { count: 'exact' }).eq('school_id', schoolId).eq('is_active', true),
        supabase.from('parents').select('id', { count: 'exact' }).eq('school_id', schoolId).eq('is_active', true)
      ]);

      return {
        students: studentsCount.count || 0,
        teachers: teachersCount.count || 0,
        classes: classesCount.count || 0,
        parents: parentsCount.count || 0
      };
    } catch (error) {
      console.error('Error fetching school stats:', error);
      return {
        students: 0,
        teachers: 0,
        classes: 0,
        parents: 0
      };
    }
  }

  // Bulk operations for school setup
  static async setupSchoolDefaults(schoolId: string) {
    try {
      // Create default departments
      const defaultDepartments = [
        { name: 'Mathematics', description: 'Mathematics Department' },
        { name: 'Science', description: 'Science Department' },
        { name: 'English', description: 'English Department' },
        { name: 'Social Studies', description: 'Social Studies Department' },
        { name: 'Physical Education', description: 'Physical Education Department' }
      ];

      const { data: departments } = await supabase
        .from('departments')
        .insert(
          defaultDepartments.map(dept => ({
            ...dept,
            school_id: schoolId
          }))
        )
        .select();

      // Create default subjects
      if (departments && departments.length > 0) {
        const defaultSubjects = [
          { name: 'Algebra', code: 'MATH101', department_id: departments[0].id },
          { name: 'Geometry', code: 'MATH102', department_id: departments[0].id },
          { name: 'Biology', code: 'SCI101', department_id: departments[1].id },
          { name: 'Chemistry', code: 'SCI102', department_id: departments[1].id },
          { name: 'English Literature', code: 'ENG101', department_id: departments[2].id },
          { name: 'Grammar', code: 'ENG102', department_id: departments[2].id }
        ];

        await supabase
          .from('subjects')
          .insert(
            defaultSubjects.map(subject => ({
              ...subject,
              school_id: schoolId
            }))
          );
      }

      return true;
    } catch (error) {
      console.error('Error setting up school defaults:', error);
      return false;
    }
  }
}
