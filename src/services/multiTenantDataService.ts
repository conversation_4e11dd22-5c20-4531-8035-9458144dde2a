
import { supabase } from '@/integrations/supabase/client';

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

export class MultiTenantDataService {
  static async query<T>(
    table: string,
    columns: string = '*',
    filters: Record<string, any> = {}
  ): Promise<ApiResponse<T[]>> {
    try {
      let query = supabase.from(table).select(columns);
      
      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      const { data, error } = await query;
      
      if (error) {
        return { success: false, data: [], error: error.message };
      }

      return { success: true, data: data as T[] };
    } catch (error: any) {
      return { success: false, data: [], error: error.message };
    }
  }

  static async create<T>(
    table: string,
    data: Partial<T>
  ): Promise<ApiResponse<T>> {
    try {
      const { data: result, error } = await supabase
        .from(table)
        .insert(data)
        .select()
        .single();

      if (error) {
        return { success: false, data: {} as T, error: error.message };
      }

      return { success: true, data: result as T };
    } catch (error: any) {
      return { success: false, data: {} as T, error: error.message };
    }
  }

  static async update<T>(
    table: string,
    id: string,
    data: Partial<T>
  ): Promise<ApiResponse<T>> {
    try {
      const { data: result, error } = await supabase
        .from(table)
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { success: false, data: {} as T, error: error.message };
      }

      return { success: true, data: result as T };
    } catch (error: any) {
      return { success: false, data: {} as T, error: error.message };
    }
  }

  static async delete(table: string, id: string): Promise<ApiResponse<boolean>> {
    try {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id);

      if (error) {
        return { success: false, data: false, error: error.message };
      }

      return { success: true, data: true };
    } catch (error: any) {
      return { success: false, data: false, error: error.message };
    }
  }
}
