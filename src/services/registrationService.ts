import { supabase } from '@/integrations/supabase/client';

export interface StudentRegistrationData {
  studentId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  dateOfBirth: string;
  gender: string;
  classId?: string | null;
  admissionDate: string;
  parentContact: string;
  emergencyContact: string;
  medicalInfo: string;
}

export interface TeacherRegistrationData {
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  hire_date: string;
  experience_years?: number;
  qualification?: string;
  department_id?: string | null;
  salary?: number | null;
}

export const registrationService = {
  /**
   * Register a new student using database function that bypasses RLS
   */
  async registerStudent(studentData: StudentRegistrationData, schoolId: string): Promise<string> {
    try {
      console.log('Registering student using database function:', studentData);
      
      const { data, error } = await supabase.rpc('register_student', {
        student_data: studentData,
        school_id: schoolId
      });

      if (error) {
        console.error('Database function error:', error);
        throw error;
      }

      if (!data) {
        throw new Error('No student ID returned from registration function');
      }

      console.log('Student registered successfully with ID:', data);
      return data;
    } catch (error) {
      console.error('Error in registerStudent:', error);
      throw error;
    }
  },

  /**
   * Register a new teacher using database function that bypasses RLS
   */
  async registerTeacher(teacherData: TeacherRegistrationData, schoolId: string): Promise<string> {
    try {
      console.log('Registering teacher using database function:', teacherData);
      
      const { data, error } = await supabase.rpc('register_teacher', {
        teacher_data: teacherData,
        school_id: schoolId
      });

      if (error) {
        console.error('Database function error:', error);
        throw error;
      }

      if (!data) {
        throw new Error('No teacher ID returned from registration function');
      }

      console.log('Teacher registered successfully with ID:', data);
      return data;
    } catch (error) {
      console.error('Error in registerTeacher:', error);
      throw error;
    }
  },

  /**
   * Check if a student ID already exists in the school
   */
  async checkStudentIdExists(studentId: string, schoolId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('students')
        .select('id')
        .eq('student_id', studentId)
        .eq('school_id', schoolId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error checking student ID:', error);
        return false; // Assume it doesn't exist if we can't check
      }

      return !!data; // Returns true if student exists
    } catch (error) {
      console.error('Error in checkStudentIdExists:', error);
      return false;
    }
  },

  /**
   * Check if a teacher employee ID already exists in the school
   */
  async checkTeacherIdExists(employeeId: string, schoolId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('teachers')
        .select('id')
        .eq('employee_id', employeeId)
        .eq('school_id', schoolId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error checking teacher ID:', error);
        return false; // Assume it doesn't exist if we can't check
      }

      return !!data; // Returns true if teacher exists
    } catch (error) {
      console.error('Error in checkTeacherIdExists:', error);
      return false;
    }
  }
};
