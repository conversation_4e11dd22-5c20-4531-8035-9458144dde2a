import { supabase } from '@/integrations/supabase/client';
import { MultiTenantService } from './multiTenantService';

// Examination Service
export class ExaminationService extends MultiTenantService {
  // Create new examination
  static async createExamination(examData: {
    name: string;
    type: string;
    start_date: string;
    end_date: string;
    total_marks?: number;
    passing_marks?: number;
    description?: string;
  }) {
    return this.withSchoolContext(async (schoolId, userId) => {
      const currentYear = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', schoolId)
        .eq('is_current', true)
        .single();

      const { data, error } = await supabaseAdmin
        .from('examinations')
        .insert({
          ...examData,
          school_id: schoolId,
          academic_year_id: currentYear.data?.id,
          created_by: userId,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }

  // Get all examinations
  static async getAllExaminations() {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('examinations')
        .select(`
          *,
          academic_year:academic_years(name),
          created_by_user:users!created_by(first_name, last_name),
          grades(count)
        `)
        .eq('school_id', schoolId)
        .order('start_date', { ascending: false });

      if (error) throw error;
      return data;
    });
  }

  // Get examination by ID
  static async getExaminationById(examId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('examinations')
        .select(`
          *,
          academic_year:academic_years(name),
          created_by_user:users!created_by(first_name, last_name)
        `)
        .eq('id', examId)
        .eq('school_id', schoolId)
        .single();

      if (error) throw error;
      return data;
    });
  }

  // Publish examination results
  static async publishExamination(examId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabaseAdmin
        .from('examinations')
        .update({ is_published: true })
        .eq('id', examId)
        .eq('school_id', schoolId)
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }
}

// Grades Service
export class GradesService extends MultiTenantService {
  // Add/Update grades for students
  static async upsertGrades(gradesData: Array<{
    examination_id: string;
    student_id: string;
    subject_id: string;
    marks_obtained: number;
    total_marks?: number;
    grade?: string;
    remarks?: string;
  }>) {
    return this.withSchoolContext(async (schoolId, userId) => {
      const records = gradesData.map(grade => ({
        ...grade,
        school_id: schoolId,
        teacher_id: userId,
      }));

      const { data, error } = await supabaseAdmin
        .from('grades')
        .upsert(records, { 
          onConflict: 'examination_id,student_id,subject_id',
          ignoreDuplicates: false 
        })
        .select();

      if (error) throw error;
      return data;
    });
  }

  // Get grades for an examination
  static async getExaminationGrades(examId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('grades')
        .select(`
          *,
          student:students(
            id,
            student_id,
            first_name,
            last_name,
            roll_number,
            class:classes(name)
          ),
          subject:subjects(name, code),
          teacher:teachers(
            users(first_name, last_name)
          )
        `)
        .eq('school_id', schoolId)
        .eq('examination_id', examId)
        .order('student.roll_number');

      if (error) throw error;
      return data;
    });
  }

  // Get student's grades for all examinations
  static async getStudentGrades(studentId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('grades')
        .select(`
          *,
          examination:examinations(name, type, start_date, is_published),
          subject:subjects(name, code),
          teacher:teachers(
            users(first_name, last_name)
          )
        `)
        .eq('school_id', schoolId)
        .eq('student_id', studentId)
        .eq('examination.is_published', true)
        .order('examination.start_date', { ascending: false });

      if (error) throw error;
      return data;
    });
  }

  // Get class performance for an examination
  static async getClassPerformance(examId: string, classId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('grades')
        .select(`
          *,
          student:students!inner(
            id,
            student_id,
            first_name,
            last_name,
            roll_number,
            class_id
          ),
          subject:subjects(name, code)
        `)
        .eq('school_id', schoolId)
        .eq('examination_id', examId)
        .eq('student.class_id', classId)
        .order('student.roll_number');

      if (error) throw error;

      // Calculate performance statistics
      const subjectStats = data.reduce((acc, grade) => {
        const subjectId = grade.subject_id;
        if (!acc[subjectId]) {
          acc[subjectId] = {
            subject: grade.subject,
            grades: [],
            average: 0,
            highest: 0,
            lowest: 100,
            passCount: 0,
            totalStudents: 0
          };
        }
        
        acc[subjectId].grades.push(grade);
        acc[subjectId].totalStudents++;
        
        const percentage = (grade.marks_obtained / grade.total_marks) * 100;
        acc[subjectId].highest = Math.max(acc[subjectId].highest, percentage);
        acc[subjectId].lowest = Math.min(acc[subjectId].lowest, percentage);
        
        if (percentage >= 40) acc[subjectId].passCount++;
        
        return acc;
      }, {} as any);

      // Calculate averages and pass rates
      Object.values(subjectStats).forEach((stat: any) => {
        const totalMarks = stat.grades.reduce((sum: number, grade: any) => 
          sum + (grade.marks_obtained / grade.total_marks) * 100, 0);
        stat.average = Math.round((totalMarks / stat.totalStudents) * 100) / 100;
        stat.passRate = Math.round((stat.passCount / stat.totalStudents) * 100 * 100) / 100;
      });

      return {
        grades: data,
        subjectStats: Object.values(subjectStats)
      };
    });
  }

  // Calculate grade based on marks
  static calculateGrade(marksObtained: number, totalMarks: number): string {
    const percentage = (marksObtained / totalMarks) * 100;
    
    if (percentage >= 90) return 'A+';
    if (percentage >= 80) return 'A';
    if (percentage >= 70) return 'B+';
    if (percentage >= 60) return 'B';
    if (percentage >= 50) return 'C+';
    if (percentage >= 40) return 'C';
    return 'F';
  }
}
