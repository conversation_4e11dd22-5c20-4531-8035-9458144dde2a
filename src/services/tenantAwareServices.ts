import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';
import { MultiTenantService } from './multiTenantService';

// Type aliases
type Tables = Database['public']['Tables'];
type Student = Tables['students']['Row'];
type Teacher = Tables['teachers']['Row'];
type Class = Tables['classes']['Row'];
type Subject = Tables['subjects']['Row'];
type Department = Tables['departments']['Row'];
type Assignment = Tables['assignments']['Row'];
type Attendance = Tables['attendance']['Row'];

// Base tenant-aware service class
abstract class BaseTenantService {
  protected static async getCurrentSchoolId(): Promise<string | null> {
    return await MultiTenantService.getCurrentUserSchoolId();
  }

  protected static async validateAccess(schoolId: string): Promise<boolean> {
    return await MultiTenantService.validateSchoolAccess(schoolId);
  }
}

// Student Service
export class StudentService extends BaseTenantService {
  static async getAll(schoolId?: string): Promise<Student[]> {
    try {
      const targetSchoolId = schoolId || await this.getCurrentSchoolId();
      if (!targetSchoolId) return [];

      const { data, error } = await supabase
        .from('students')
        .select(`
          *,
          profile:profiles(*),
          class:classes(*)
        `)
        .eq('school_id', targetSchoolId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Student[];
    } catch (error) {
      console.error('Error fetching students:', error);
      return [];
    }
  }

  static async getById(id: string): Promise<Student | null> {
    try {
      const { data, error } = await supabase
        .from('students')
        .select(`
          *,
          profile:profiles(*),
          class:classes(*),
          parents:student_parents(
            *,
            parent:parents(
              *,
              profile:profiles(*)
            )
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Student;
    } catch (error) {
      console.error('Error fetching student:', error);
      return null;
    }
  }

  static async create(studentData: Omit<Student, 'id' | 'created_at' | 'updated_at'>): Promise<Student | null> {
    try {
      const schoolId = await this.getCurrentSchoolId();
      if (!schoolId) throw new Error('No school context');

      const { data, error } = await supabase
        .from('students')
        .insert({
          ...studentData,
          school_id: schoolId
        })
        .select()
        .single();

      if (error) throw error;
      return data as Student;
    } catch (error) {
      console.error('Error creating student:', error);
      return null;
    }
  }

  static async update(id: string, updates: Partial<Student>): Promise<Student | null> {
    try {
      const { data, error } = await supabase
        .from('students')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Student;
    } catch (error) {
      console.error('Error updating student:', error);
      return null;
    }
  }

  static async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('students')
        .update({ is_active: false })
        .eq('id', id);

      return !error;
    } catch (error) {
      console.error('Error deleting student:', error);
      return false;
    }
  }

  static async getByClass(classId: string): Promise<Student[]> {
    try {
      const { data, error } = await supabase
        .from('students')
        .select(`
          *,
          profile:profiles(*)
        `)
        .eq('class_id', classId)
        .eq('is_active', true)
        .order('roll_number');

      if (error) throw error;
      return data as Student[];
    } catch (error) {
      console.error('Error fetching students by class:', error);
      return [];
    }
  }
}

// Teacher Service
export class TeacherService extends BaseTenantService {
  static async getAll(schoolId?: string): Promise<Teacher[]> {
    try {
      const targetSchoolId = schoolId || await this.getCurrentSchoolId();
      if (!targetSchoolId) return [];

      const { data, error } = await supabase
        .from('teachers')
        .select(`
          *,
          profile:profiles(*),
          department:departments(*)
        `)
        .eq('school_id', targetSchoolId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Teacher[];
    } catch (error) {
      console.error('Error fetching teachers:', error);
      return [];
    }
  }

  static async getById(id: string): Promise<Teacher | null> {
    try {
      const { data, error } = await supabase
        .from('teachers')
        .select(`
          *,
          profile:profiles(*),
          department:departments(*)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Teacher;
    } catch (error) {
      console.error('Error fetching teacher:', error);
      return null;
    }
  }

  static async create(teacherData: Omit<Teacher, 'id' | 'created_at' | 'updated_at'>): Promise<Teacher | null> {
    try {
      const schoolId = await this.getCurrentSchoolId();
      if (!schoolId) throw new Error('No school context');

      const { data, error } = await supabase
        .from('teachers')
        .insert({
          ...teacherData,
          school_id: schoolId
        })
        .select()
        .single();

      if (error) throw error;
      return data as Teacher;
    } catch (error) {
      console.error('Error creating teacher:', error);
      return null;
    }
  }

  static async update(id: string, updates: Partial<Teacher>): Promise<Teacher | null> {
    try {
      const { data, error } = await supabase
        .from('teachers')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Teacher;
    } catch (error) {
      console.error('Error updating teacher:', error);
      return null;
    }
  }

  static async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('teachers')
        .update({ is_active: false })
        .eq('id', id);

      return !error;
    } catch (error) {
      console.error('Error deleting teacher:', error);
      return false;
    }
  }

  static async getByDepartment(departmentId: string): Promise<Teacher[]> {
    try {
      const { data, error } = await supabase
        .from('teachers')
        .select(`
          *,
          profile:profiles(*)
        `)
        .eq('department_id', departmentId)
        .eq('is_active', true)
        .order('hire_date');

      if (error) throw error;
      return data as Teacher[];
    } catch (error) {
      console.error('Error fetching teachers by department:', error);
      return [];
    }
  }
}

// Class Service
export class ClassService extends BaseTenantService {
  static async getAll(schoolId?: string): Promise<Class[]> {
    try {
      const targetSchoolId = schoolId || await this.getCurrentSchoolId();
      if (!targetSchoolId) return [];

      const { data, error } = await supabase
        .from('classes')
        .select(`
          *,
          class_teacher:profiles(*)
        `)
        .eq('school_id', targetSchoolId)
        .eq('is_active', true)
        .order('grade_level', { ascending: true });

      if (error) throw error;
      return data as Class[];
    } catch (error) {
      console.error('Error fetching classes:', error);
      return [];
    }
  }

  static async getById(id: string): Promise<Class | null> {
    try {
      const { data, error } = await supabase
        .from('classes')
        .select(`
          *,
          class_teacher:profiles(*),
          students:students(count)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Class;
    } catch (error) {
      console.error('Error fetching class:', error);
      return null;
    }
  }

  static async create(classData: Omit<Class, 'id' | 'created_at' | 'updated_at'>): Promise<Class | null> {
    try {
      const schoolId = await this.getCurrentSchoolId();
      if (!schoolId) throw new Error('No school context');

      const { data, error } = await supabase
        .from('classes')
        .insert({
          ...classData,
          school_id: schoolId
        })
        .select()
        .single();

      if (error) throw error;
      return data as Class;
    } catch (error) {
      console.error('Error creating class:', error);
      return null;
    }
  }

  static async update(id: string, updates: Partial<Class>): Promise<Class | null> {
    try {
      const { data, error } = await supabase
        .from('classes')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Class;
    } catch (error) {
      console.error('Error updating class:', error);
      return null;
    }
  }

  static async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('classes')
        .update({ is_active: false })
        .eq('id', id);

      return !error;
    } catch (error) {
      console.error('Error deleting class:', error);
      return false;
    }
  }
}

// Subject Service
export class SubjectService extends BaseTenantService {
  static async getAll(schoolId?: string): Promise<Subject[]> {
    try {
      const targetSchoolId = schoolId || await this.getCurrentSchoolId();
      if (!targetSchoolId) return [];

      const { data, error } = await supabase
        .from('subjects')
        .select(`
          *,
          department:departments(*)
        `)
        .eq('school_id', targetSchoolId)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data as Subject[];
    } catch (error) {
      console.error('Error fetching subjects:', error);
      return [];
    }
  }

  static async getById(id: string): Promise<Subject | null> {
    try {
      const { data, error } = await supabase
        .from('subjects')
        .select(`
          *,
          department:departments(*)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Subject;
    } catch (error) {
      console.error('Error fetching subject:', error);
      return null;
    }
  }

  static async create(subjectData: Omit<Subject, 'id' | 'created_at' | 'updated_at'>): Promise<Subject | null> {
    try {
      const schoolId = await this.getCurrentSchoolId();
      if (!schoolId) throw new Error('No school context');

      const { data, error } = await supabase
        .from('subjects')
        .insert({
          ...subjectData,
          school_id: schoolId
        })
        .select()
        .single();

      if (error) throw error;
      return data as Subject;
    } catch (error) {
      console.error('Error creating subject:', error);
      return null;
    }
  }

  static async update(id: string, updates: Partial<Subject>): Promise<Subject | null> {
    try {
      const { data, error } = await supabase
        .from('subjects')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Subject;
    } catch (error) {
      console.error('Error updating subject:', error);
      return null;
    }
  }

  static async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('subjects')
        .update({ is_active: false })
        .eq('id', id);

      return !error;
    } catch (error) {
      console.error('Error deleting subject:', error);
      return false;
    }
  }

  static async getByDepartment(departmentId: string): Promise<Subject[]> {
    try {
      const { data, error } = await supabase
        .from('subjects')
        .select('*')
        .eq('department_id', departmentId)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data as Subject[];
    } catch (error) {
      console.error('Error fetching subjects by department:', error);
      return [];
    }
  }
}

// Department Service
export class DepartmentService extends BaseTenantService {
  static async getAll(schoolId?: string): Promise<Department[]> {
    try {
      const targetSchoolId = schoolId || await this.getCurrentSchoolId();
      if (!targetSchoolId) return [];

      const { data, error } = await supabase
        .from('departments')
        .select(`
          *,
          head_teacher:profiles(*)
        `)
        .eq('school_id', targetSchoolId)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data as Department[];
    } catch (error) {
      console.error('Error fetching departments:', error);
      return [];
    }
  }

  static async getById(id: string): Promise<Department | null> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select(`
          *,
          head_teacher:profiles(*),
          subjects:subjects(count),
          teachers:teachers(count)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Department;
    } catch (error) {
      console.error('Error fetching department:', error);
      return null;
    }
  }

  static async create(departmentData: Omit<Department, 'id' | 'created_at' | 'updated_at'>): Promise<Department | null> {
    try {
      const schoolId = await this.getCurrentSchoolId();
      if (!schoolId) throw new Error('No school context');

      const { data, error } = await supabase
        .from('departments')
        .insert({
          ...departmentData,
          school_id: schoolId
        })
        .select()
        .single();

      if (error) throw error;
      return data as Department;
    } catch (error) {
      console.error('Error creating department:', error);
      return null;
    }
  }

  static async update(id: string, updates: Partial<Department>): Promise<Department | null> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Department;
    } catch (error) {
      console.error('Error updating department:', error);
      return null;
    }
  }

  static async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('departments')
        .update({ is_active: false })
        .eq('id', id);

      return !error;
    } catch (error) {
      console.error('Error deleting department:', error);
      return false;
    }
  }
}
