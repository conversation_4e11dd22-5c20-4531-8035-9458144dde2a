import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { SchoolProvider } from "@/contexts/SchoolContext";
import { MultiTenantAuthProvider } from "@/contexts/MultiTenantAuthContext";
import { MultiTenantRoute } from "@/components/MultiTenantRoute";
import { Layout } from "@/components/Layout";
import { ProtectedRoute } from "@/components/ProtectedRoute";

// Public Pages
import { LandingPage } from "@/pages/LandingPage";
import { SchoolRegistration } from "@/pages/SchoolRegistration";
import { SchoolRegistrationSuccess } from "@/pages/SchoolRegistrationSuccess";
import { SimpleSchoolLogin } from "@/pages/SimpleSchoolLogin";
import { SchoolPortal } from "@/pages/SchoolPortal";
import { StudentSignup } from "@/pages/StudentSignup";
import { TeacherSignup } from "@/pages/TeacherSignup";
import { SuperAdminDashboard } from "@/pages/SuperAdminDashboard";
import { AdminRegistration } from "@/pages/AdminRegistration";

// Existing Pages (wrapped in multi-tenant context)
import Index from "./pages/Index";
import { NewLogin } from "./pages/NewLogin";
import { Dashboard } from "./pages/Dashboard";
import { Students } from "./pages/Students";
import { Teachers } from "./pages/Teachers";
import { Classes } from "./pages/Classes";
import { Subjects } from "./pages/Subjects";
import { Assignments } from "./pages/Assignments";
import { Results } from "./pages/Results";

// Student Pages
import { StudentTimetable } from "./pages/student/StudentTimetable";
import { StudentExaminations } from "./pages/student/StudentExaminations";
import { StudentAssignments } from "./pages/student/StudentAssignments";
import { StudentResults } from "./pages/student/StudentResults";

// Teacher Pages
import { TeacherTimetable } from "./pages/teacher/TeacherTimetable";
import { Attendance } from "./pages/Attendance";
import { Fees } from "./pages/Fees";
import { Communications } from "./pages/Communications";
import { Settings } from "./pages/Settings";
import { Parents } from "./pages/Parents";
import { Departments } from "./pages/Departments";
import { Examinations } from "./pages/Examinations";
import { Grades } from "./pages/Grades";
import { Notifications } from "./pages/Notifications";
import { Reports } from "./pages/Reports";
import { Timetable } from "./pages/Timetable";
import { UserManagement } from "./pages/UserManagement";

// Other pages
import NotFound from "./pages/NotFound";
import { ContactUs } from "./pages/ContactUs";
import { AboutUs } from "./pages/AboutUs";
import { Features } from "./pages/Features";
import { Privacy } from "./pages/Privacy";
import { Documentation } from "./pages/Documentation";
import { HelpCenter } from "./pages/HelpCenter";
import { RoleDemo } from "./pages/RoleDemo";
import { Unauthorized } from "./pages/Unauthorized";
import { DatabaseTest } from "./pages/DatabaseTest";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <SchoolProvider>
        <BrowserRouter>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<LandingPage />} />
            <Route path="/register-school" element={<SchoolRegistration />} />
            <Route path="/school-registered" element={<SchoolRegistrationSuccess />} />
            <Route path="/contact" element={<ContactUs />} />
            <Route path="/about" element={<AboutUs />} />
            <Route path="/features" element={<Features />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/docs" element={<Documentation />} />
            <Route path="/help" element={<HelpCenter />} />
            <Route path="/demo" element={<RoleDemo />} />
            <Route path="/unauthorized" element={<Unauthorized />} />
            <Route path="/db-test" element={<DatabaseTest />} />

            {/* Super Admin Routes */}
            <Route path="/super-admin" element={
              <MultiTenantAuthProvider>
                <SuperAdminDashboard />
              </MultiTenantAuthProvider>
            } />

            {/* Legacy Routes (for backward compatibility) */}
            <Route path="/login" element={
              <AuthProvider>
                <NewLogin />
              </AuthProvider>
            } />

            {/* School-specific Routes */}
            <Route path="/:schoolSlug/login" element={
              <SimpleSchoolLogin />
            } />

            {/* Signup Routes */}
            <Route path="/:schoolSlug/signup/student" element={
              <MultiTenantAuthProvider>
                <StudentSignup />
              </MultiTenantAuthProvider>
            } />

            <Route path="/:schoolSlug/signup/teacher" element={
              <MultiTenantAuthProvider>
                <TeacherSignup />
              </MultiTenantAuthProvider>
            } />

            <Route path="/:schoolSlug/register-admin" element={<AdminRegistration />} />

            <Route path="/:schoolSlug/*" element={
              <MultiTenantAuthProvider>
                <MultiTenantRoute>
                  <Layout>
                    <Routes>
                      <Route path="dashboard" element={<Dashboard />} />
                      <Route path="students" element={<Students />} />
                      <Route path="teachers" element={
                        <ProtectedRoute requiredRoles={['admin']}>
                          <Teachers />
                        </ProtectedRoute>
                      } />
                      <Route path="classes" element={<Classes />} />
                      <Route path="subjects" element={<Subjects />} />
                      <Route path="assignments" element={<Assignments />} />
                      <Route path="results" element={<Results />} />
                      <Route path="attendance" element={<Attendance />} />

                    {/* Student Pages */}
                    <Route path="student-timetable" element={<StudentTimetable />} />
                    <Route path="student-examinations" element={<StudentExaminations />} />
                    <Route path="student-assignments" element={<StudentAssignments />} />
                    <Route path="student-results" element={<StudentResults />} />

                    {/* Teacher Pages */}
                    <Route path="teacher-timetable" element={<TeacherTimetable />} />
                    <Route path="fees" element={<Fees />} />
                    <Route path="communications" element={<Communications />} />
                    <Route path="settings" element={<Settings />} />
                    <Route path="parents" element={<Parents />} />
                    <Route path="departments" element={<Departments />} />
                    <Route path="examinations" element={<Examinations />} />
                    <Route path="grades" element={<Grades />} />
                    <Route path="notifications" element={<Notifications />} />
                    <Route path="reports" element={<Reports />} />
                      <Route path="timetable" element={<Timetable />} />
                      <Route path="users" element={<UserManagement />} />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </Layout>
                </MultiTenantRoute>
              </MultiTenantAuthProvider>
            } />

            {/* Fallback */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </SchoolProvider>
      <Toaster />
      <Sonner />
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
