import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { SimpleAuthProvider } from "@/contexts/SimpleAuthContext";
import { MockAuthProvider } from "@/contexts/MockAuthContext";
import { SimpleProtectedRoute } from "@/components/SimpleProtectedRoute";
import { SimpleLayout } from "@/components/SimpleLayout";
import { LandingPage } from "@/pages/LandingPage";
import { SimpleLogin } from "@/pages/SimpleLogin";
import { SimpleDashboard } from "@/pages/SimpleDashboard";
import { StudentSignup } from "@/pages/StudentSignup";
import { TeacherSignup } from "@/pages/TeacherSignup";

// Import all existing pages
import { Students } from "@/pages/Students";
import { Teachers } from "@/pages/Teachers";
import { Classes } from "@/pages/Classes";
import { Subjects } from "@/pages/Subjects";
import { Departments } from "@/pages/Departments";
import { Attendance } from "@/pages/Attendance";
import { Timetable } from "@/pages/Timetable";
import { Assignments } from "@/pages/Assignments";
import { Examinations } from "@/pages/Examinations";
import { Grades } from "@/pages/Grades";
import { Results } from "@/pages/Results";
import { Fees } from "@/pages/Fees";
import { Communications } from "@/pages/Communications";
import { Reports } from "@/pages/Reports";
import { Notifications } from "@/pages/Notifications";
import { Settings } from "@/pages/Settings";
import { UserManagement } from "@/pages/UserManagement";
import { Parents } from "@/pages/Parents";

// Import student and teacher specific pages
import { StudentTimetable } from "@/pages/student/StudentTimetable";
import { StudentAssignments } from "@/pages/student/StudentAssignments";
import { StudentExaminations } from "@/pages/student/StudentExaminations";
import { StudentResults } from "@/pages/student/StudentResults";
import { TeacherTimetable } from "@/pages/teacher/TeacherTimetable";

const queryClient = new QueryClient();

const SimpleApp = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <SimpleAuthProvider>
          <MockAuthProvider>
          <Routes>
            {/* Landing page */}
            <Route path="/" element={<LandingPage />} />
            
            {/* School-specific login */}
            <Route path="/:schoolSlug/login" element={<SimpleLogin />} />

            {/* Signup Routes */}
            <Route path="/:schoolSlug/signup/student" element={<StudentSignup />} />
            <Route path="/:schoolSlug/signup/teacher" element={<TeacherSignup />} />

            {/* Protected school routes */}
            <Route
              path="/:schoolSlug/dashboard"
              element={
                <SimpleProtectedRoute>
                  <SimpleLayout>
                    <SimpleDashboard />
                  </SimpleLayout>
                </SimpleProtectedRoute>
              }
            />
            {/* All school management pages using existing components */}
            <Route path="/:schoolSlug/students" element={<SimpleProtectedRoute><SimpleLayout><Students /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/teachers" element={<SimpleProtectedRoute><SimpleLayout><Teachers /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/parents" element={<SimpleProtectedRoute><SimpleLayout><Parents /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/classes" element={<SimpleProtectedRoute><SimpleLayout><Classes /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/subjects" element={<SimpleProtectedRoute><SimpleLayout><Subjects /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/departments" element={<SimpleProtectedRoute><SimpleLayout><Departments /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/attendance" element={<SimpleProtectedRoute><SimpleLayout><Attendance /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/timetable" element={<SimpleProtectedRoute><SimpleLayout><Timetable /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/assignments" element={<SimpleProtectedRoute><SimpleLayout><Assignments /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/examinations" element={<SimpleProtectedRoute><SimpleLayout><Examinations /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/grades" element={<SimpleProtectedRoute><SimpleLayout><Grades /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/results" element={<SimpleProtectedRoute><SimpleLayout><Results /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/fees" element={<SimpleProtectedRoute><SimpleLayout><Fees /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/communications" element={<SimpleProtectedRoute><SimpleLayout><Communications /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/reports" element={<SimpleProtectedRoute><SimpleLayout><Reports /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/notifications" element={<SimpleProtectedRoute><SimpleLayout><Notifications /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/settings" element={<SimpleProtectedRoute><SimpleLayout><Settings /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/user-management" element={<SimpleProtectedRoute><SimpleLayout><UserManagement /></SimpleLayout></SimpleProtectedRoute>} />

            {/* Student-specific routes */}
            <Route path="/:schoolSlug/student-timetable" element={<SimpleProtectedRoute><SimpleLayout><StudentTimetable /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/student-assignments" element={<SimpleProtectedRoute><SimpleLayout><StudentAssignments /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/student-examinations" element={<SimpleProtectedRoute><SimpleLayout><StudentExaminations /></SimpleLayout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/student-results" element={<SimpleProtectedRoute><SimpleLayout><StudentResults /></SimpleLayout></SimpleProtectedRoute>} />

            {/* Teacher-specific routes */}
            <Route path="/:schoolSlug/teacher-timetable" element={<SimpleProtectedRoute><SimpleLayout><TeacherTimetable /></SimpleLayout></SimpleProtectedRoute>} />

            {/* Redirect school root to dashboard */}
            <Route path="/:schoolSlug" element={<Navigate to="/:schoolSlug/dashboard" replace />} />
            
            {/* Catch all - redirect to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
          </MockAuthProvider>
        </SimpleAuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default SimpleApp;
