import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { SimpleAuthProvider } from "@/contexts/SimpleAuthContext";
import { MockAuthProvider } from "@/contexts/MockAuthContext";
import { SimpleProtectedRoute } from "@/components/SimpleProtectedRoute";
import { Layout } from "@/components/Layout";
import { LandingPage } from "@/pages/LandingPage";
import { SimpleLogin } from "@/pages/SimpleLogin";
import { SimpleDashboard } from "@/pages/SimpleDashboard";
import { StudentSignup } from "@/pages/StudentSignup";
import { TeacherSignup } from "@/pages/TeacherSignup";

// Import all existing pages
import { Students } from "@/pages/Students";
import { Teachers } from "@/pages/Teachers";
import { Classes } from "@/pages/Classes";
import { Subjects } from "@/pages/Subjects";
import { Departments } from "@/pages/Departments";
import { Attendance } from "@/pages/Attendance";
import { Timetable } from "@/pages/Timetable";
import { Assignments } from "@/pages/Assignments";
import { Examinations } from "@/pages/Examinations";
import { Grades } from "@/pages/Grades";
import { Results } from "@/pages/Results";
import { Fees } from "@/pages/Fees";
import { Communications } from "@/pages/Communications";
import { Reports } from "@/pages/Reports";
import { Notifications } from "@/pages/Notifications";
import { Settings } from "@/pages/Settings";
import { UserManagement } from "@/pages/UserManagement";
import { Parents } from "@/pages/Parents";

const queryClient = new QueryClient();

const SimpleApp = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <SimpleAuthProvider>
          <MockAuthProvider>
          <Routes>
            {/* Landing page */}
            <Route path="/" element={<LandingPage />} />
            
            {/* School-specific login */}
            <Route path="/:schoolSlug/login" element={<SimpleLogin />} />

            {/* Signup Routes */}
            <Route path="/:schoolSlug/signup/student" element={<StudentSignup />} />
            <Route path="/:schoolSlug/signup/teacher" element={<TeacherSignup />} />

            {/* Protected school routes */}
            <Route
              path="/:schoolSlug/dashboard"
              element={
                <SimpleProtectedRoute>
                  <Layout>
                    <SimpleDashboard />
                  </Layout>
                </SimpleProtectedRoute>
              }
            />
            {/* All school management pages using existing components */}
            <Route path="/:schoolSlug/students" element={<SimpleProtectedRoute><Layout><Students /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/teachers" element={<SimpleProtectedRoute><Layout><Teachers /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/parents" element={<SimpleProtectedRoute><Layout><Parents /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/classes" element={<SimpleProtectedRoute><Layout><Classes /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/subjects" element={<SimpleProtectedRoute><Layout><Subjects /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/departments" element={<SimpleProtectedRoute><Layout><Departments /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/attendance" element={<SimpleProtectedRoute><Layout><Attendance /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/timetable" element={<SimpleProtectedRoute><Layout><Timetable /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/assignments" element={<SimpleProtectedRoute><Layout><Assignments /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/examinations" element={<SimpleProtectedRoute><Layout><Examinations /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/grades" element={<SimpleProtectedRoute><Layout><Grades /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/results" element={<SimpleProtectedRoute><Layout><Results /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/fees" element={<SimpleProtectedRoute><Layout><Fees /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/communications" element={<SimpleProtectedRoute><Layout><Communications /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/reports" element={<SimpleProtectedRoute><Layout><Reports /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/notifications" element={<SimpleProtectedRoute><Layout><Notifications /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/settings" element={<SimpleProtectedRoute><Layout><Settings /></Layout></SimpleProtectedRoute>} />
            <Route path="/:schoolSlug/user-management" element={<SimpleProtectedRoute><Layout><UserManagement /></Layout></SimpleProtectedRoute>} />

            {/* Redirect school root to dashboard */}
            <Route path="/:schoolSlug" element={<Navigate to="/:schoolSlug/dashboard" replace />} />
            
            {/* Catch all - redirect to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
          </MockAuthProvider>
        </SimpleAuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default SimpleApp;
