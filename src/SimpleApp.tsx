import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useParams } from "react-router-dom";
import { CleanAuthProvider } from "@/contexts/CleanAuthContext";

import { CleanProtectedRoute } from "@/components/CleanProtectedRoute";
import { SimpleLayout } from "@/components/SimpleLayout";
import { LandingPage } from "@/pages/LandingPage";
import { CleanLogin } from "@/pages/CleanLogin";
import { SimpleDashboard } from "@/pages/SimpleDashboard";
import { StudentSignup } from "@/pages/StudentSignup";
import { TeacherSignup } from "@/pages/TeacherSignup";

// Import all existing pages
import { Students } from "@/pages/Students";
import { Teachers } from "@/pages/Teachers";
import { Classes } from "@/pages/Classes";
import { Subjects } from "@/pages/Subjects";
import { Departments } from "@/pages/Departments";
import { Attendance } from "@/pages/Attendance";
import { Timetable } from "@/pages/Timetable";
import { Assignments } from "@/pages/Assignments";
import { Examinations } from "@/pages/Examinations";
import { Grades } from "@/pages/Grades";
import { Results } from "@/pages/Results";
import { Fees } from "@/pages/Fees";
import { Communications } from "@/pages/Communications";
import { Reports } from "@/pages/Reports";
import { Notifications } from "@/pages/Notifications";
import { Settings } from "@/pages/Settings";
import { UserManagement } from "@/pages/UserManagement";
import { Parents } from "@/pages/Parents";

// Import student and teacher specific pages
import { StudentTimetable } from "@/pages/student/StudentTimetable";
import { StudentAssignments } from "@/pages/student/StudentAssignments";
import { StudentExaminations } from "@/pages/student/StudentExaminations";
import { StudentResults } from "@/pages/student/StudentResults";
import { TeacherTimetable } from "@/pages/teacher/TeacherTimetable";

const queryClient = new QueryClient();

// Component to handle school root redirect with proper parameter
const SchoolRedirect = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  return <Navigate to={`/${schoolSlug}/dashboard`} replace />;
};

const SimpleApp = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <CleanAuthProvider>
          <Routes>
            {/* Landing page */}
            <Route path="/" element={<LandingPage />} />

            {/* School-specific login */}
            <Route path="/:schoolSlug/login" element={<CleanLogin />} />

            {/* Signup Routes */}
            <Route path="/:schoolSlug/signup/student" element={<StudentSignup />} />
            <Route path="/:schoolSlug/signup/teacher" element={<TeacherSignup />} />

            {/* Protected school routes */}
            <Route
              path="/:schoolSlug/dashboard"
              element={
                <CleanProtectedRoute>
                  <SimpleLayout>
                    <SimpleDashboard />
                  </SimpleLayout>
                </CleanProtectedRoute>
              }
            />
            {/* All school management pages using existing components */}
            <Route path="/:schoolSlug/students" element={<CleanProtectedRoute><SimpleLayout><Students /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/teachers" element={<CleanProtectedRoute><SimpleLayout><Teachers /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/parents" element={<CleanProtectedRoute><SimpleLayout><Parents /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/classes" element={<CleanProtectedRoute><SimpleLayout><Classes /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/subjects" element={<CleanProtectedRoute><SimpleLayout><Subjects /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/departments" element={<CleanProtectedRoute><SimpleLayout><Departments /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/attendance" element={<CleanProtectedRoute><SimpleLayout><Attendance /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/timetable" element={<CleanProtectedRoute><SimpleLayout><Timetable /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/assignments" element={<CleanProtectedRoute><SimpleLayout><Assignments /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/examinations" element={<CleanProtectedRoute><SimpleLayout><Examinations /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/grades" element={<CleanProtectedRoute><SimpleLayout><Grades /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/results" element={<CleanProtectedRoute><SimpleLayout><Results /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/fees" element={<CleanProtectedRoute><SimpleLayout><Fees /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/communications" element={<CleanProtectedRoute><SimpleLayout><Communications /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/reports" element={<CleanProtectedRoute><SimpleLayout><Reports /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/notifications" element={<CleanProtectedRoute><SimpleLayout><Notifications /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/settings" element={<CleanProtectedRoute><SimpleLayout><Settings /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/user-management" element={<CleanProtectedRoute><SimpleLayout><UserManagement /></SimpleLayout></CleanProtectedRoute>} />

            {/* Student-specific routes */}
            <Route path="/:schoolSlug/student-timetable" element={<CleanProtectedRoute><SimpleLayout><StudentTimetable /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/student-assignments" element={<CleanProtectedRoute><SimpleLayout><StudentAssignments /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/student-examinations" element={<CleanProtectedRoute><SimpleLayout><StudentExaminations /></SimpleLayout></CleanProtectedRoute>} />
            <Route path="/:schoolSlug/student-results" element={<CleanProtectedRoute><SimpleLayout><StudentResults /></SimpleLayout></CleanProtectedRoute>} />

            {/* Teacher-specific routes */}
            <Route path="/:schoolSlug/teacher-timetable" element={<CleanProtectedRoute><SimpleLayout><TeacherTimetable /></SimpleLayout></CleanProtectedRoute>} />

            {/* Redirect school root to dashboard */}
            <Route path="/:schoolSlug" element={<SchoolRedirect />} />
            
            {/* Catch all - redirect to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </CleanAuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default SimpleApp;
