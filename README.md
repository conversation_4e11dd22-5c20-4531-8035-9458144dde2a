# School Management System

A comprehensive school management system built with React, TypeScript, and modern web technologies. This system automates and manages key school operations including student information, attendance, grades, timetables, communication, and reporting with role-based access control.

## 🚀 Features

### Multi-User Role Support
- **Admin**: Complete system management and oversight
- **Teacher**: Class management, grading, and student interaction
- **Student**: Academic progress tracking and assignment submission
- **Parent**: Child's progress monitoring and school communication

### Core Functionality

#### 🔐 Authentication & Security
- Secure login system with role-based access control (RBAC)
- Protected routes based on user roles and permissions
- Session management with remember me functionality
- Demo credentials for testing different user roles

#### 👨‍💼 Admin Module
- Comprehensive dashboard with school statistics
- User management (students, teachers, parents, admins)
- Class and subject management
- Fee management and tracking
- Reports and analytics
- System-wide notifications

#### 👩‍🏫 Teacher Module
- Teacher-specific dashboard with class overview
- Attendance management system
- Grade and assignment management
- Student progress tracking
- Parent communication tools
- Timetable viewing

#### 🎓 Student Module
- Personal dashboard with academic overview
- Assignment viewing and submission
- Grade and attendance tracking
- Timetable access
- Resource downloads
- Progress visualization

#### 👨‍👩‍👧‍👦 Parent Module
- Child's academic progress monitoring
- Attendance and grade viewing
- Fee payment interface
- Teacher communication
- Event and notification tracking

### 📊 Key Features
- **Attendance Management**: Real-time attendance tracking with multiple status options
- **Grade Management**: Comprehensive grading system with different assessment types
- **Assignment System**: Create, distribute, and track assignments
- **Timetable Management**: Class scheduling and viewing
- **Fee Management**: Payment tracking and online payment integration
- **Communication System**: Multi-user messaging and notifications
- **Reports & Analytics**: Detailed reporting with export capabilities
- **Mobile Responsive**: Optimized for desktop, tablet, and mobile devices

## 🛠️ Technology Stack

- **Frontend**: React 18 with TypeScript
- **Styling**: Tailwind CSS with ShadCN UI components
- **Routing**: React Router v6 with protected routes
- **State Management**: React Context API
- **Form Handling**: React Hook Form with Zod validation
- **Date Handling**: date-fns
- **Icons**: Lucide React
- **Build Tool**: Vite
- **Package Manager**: npm

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/godwinmwanzi/schoolmanagement.git
cd schoolmanagement
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:8080`

### Demo Credentials

The system includes demo credentials for testing different user roles:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | admin123 |
| Teacher | <EMAIL> | teacher123 |
| Student | <EMAIL> | student123 |
| Parent | <EMAIL> | parent123 |

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # ShadCN UI components
│   ├── Header.tsx      # Application header
│   ├── Layout.tsx      # Main layout wrapper
│   ├── MetricCard.tsx  # Statistics display component
│   ├── ProtectedRoute.tsx # Route protection
│   └── SchoolSidebar.tsx  # Navigation sidebar
├── contexts/           # React contexts
│   └── AuthContext.tsx # Authentication context
├── pages/              # Page components
│   ├── dashboards/     # Role-specific dashboards
│   ├── Attendance.tsx  # Attendance management
│   ├── Assignments.tsx # Assignment management
│   ├── Classes.tsx     # Class management
│   ├── Dashboard.tsx   # Main dashboard router
│   ├── Grades.tsx      # Grade management
│   ├── Login.tsx       # Login page
│   ├── Results.tsx     # Results viewing
│   ├── Students.tsx    # Student management
│   └── Teachers.tsx    # Teacher management
├── types/              # TypeScript type definitions
│   └── index.ts        # Core type definitions
├── lib/                # Utility functions
└── hooks/              # Custom React hooks
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🎨 UI Components

The system uses ShadCN UI components for a consistent and modern interface:
- Cards, Tables, Forms, Dialogs
- Navigation, Buttons, Badges
- Charts, Progress bars, Calendars
- Responsive design patterns

## 🔒 Security Features

- Role-based access control (RBAC)
- Protected routes and components
- Permission-based feature access
- Secure authentication flow
- Input validation and sanitization

## 📱 Responsive Design

The application is fully responsive and optimized for:
- Desktop computers (1024px+)
- Tablets (768px - 1023px)
- Mobile phones (320px - 767px)

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

The build artifacts will be stored in the `dist/` directory.

### Environment Variables
Create a `.env` file for environment-specific configurations:
```env
VITE_API_URL=your_api_url
VITE_APP_NAME=School Management System
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Contact the development team
- Check the documentation

## 🔮 Future Enhancements

- Real-time notifications with WebSocket
- Advanced reporting and analytics
- Mobile app development
- Integration with external systems
- Multi-language support
- Advanced security features
- Automated backup systems

---

**Note**: This is a demonstration system with mock data. For production use, integrate with a real backend API and database system.

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/8bf428d3-c44c-4011-afc6-23d22dfe815c) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
