
-- Create schools (tenants) table
CREATE TABLE public.schools (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  domain TEXT,
  settings JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create user profiles linked to schools
CREATE TABLE public.profiles (
  id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  school_id UUID NOT NULL REFERENCES public.schools(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('admin', 'teacher', 'student', 'parent', 'super_admin')),
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  phone TEXT,
  address TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add school_id to existing tables for multi-tenancy
ALTER TABLE public.students ADD COLUMN school_id UUID REFERENCES public.schools(id) ON DELETE CASCADE;
ALTER TABLE public.teachers ADD COLUMN school_id UUID REFERENCES public.schools(id) ON DELETE CASCADE;
ALTER TABLE public.classes ADD COLUMN school_id UUID REFERENCES public.schools(id) ON DELETE CASCADE;
ALTER TABLE public.subjects ADD COLUMN school_id UUID REFERENCES public.schools(id) ON DELETE CASCADE;
ALTER TABLE public.assignments ADD COLUMN school_id UUID REFERENCES public.schools(id) ON DELETE CASCADE;
ALTER TABLE public.results ADD COLUMN school_id UUID REFERENCES public.schools(id) ON DELETE CASCADE;

-- Enable RLS on all tables
ALTER TABLE public.schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.results ENABLE ROW LEVEL SECURITY;

-- Create security definer functions for RLS
CREATE OR REPLACE FUNCTION public.get_current_user_school_id()
RETURNS UUID AS $$
  SELECT school_id FROM public.profiles WHERE id = auth.uid();
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

CREATE OR REPLACE FUNCTION public.is_super_admin()
RETURNS BOOLEAN AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'super_admin'
  );
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

-- RLS Policies for schools table
CREATE POLICY "Users can view their own school" ON public.schools
  FOR SELECT USING (
    id = public.get_current_user_school_id() OR public.is_super_admin()
  );

CREATE POLICY "Super admins can manage schools" ON public.schools
  FOR ALL USING (public.is_super_admin());

-- RLS Policies for profiles table
CREATE POLICY "Users can view profiles in their school" ON public.profiles
  FOR SELECT USING (
    school_id = public.get_current_user_school_id() OR public.is_super_admin()
  );

CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Admins can manage profiles in their school" ON public.profiles
  FOR ALL USING (
    (school_id = public.get_current_user_school_id() AND 
     EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')) OR
    public.is_super_admin()
  );

-- RLS Policies for students table
CREATE POLICY "School users can access students" ON public.students
  FOR ALL USING (
    school_id = public.get_current_user_school_id() OR public.is_super_admin()
  );

-- RLS Policies for teachers table
CREATE POLICY "School users can access teachers" ON public.teachers
  FOR ALL USING (
    school_id = public.get_current_user_school_id() OR public.is_super_admin()
  );

-- RLS Policies for classes table
CREATE POLICY "School users can access classes" ON public.classes
  FOR ALL USING (
    school_id = public.get_current_user_school_id() OR public.is_super_admin()
  );

-- RLS Policies for subjects table
CREATE POLICY "School users can access subjects" ON public.subjects
  FOR ALL USING (
    school_id = public.get_current_user_school_id() OR public.is_super_admin()
  );

-- RLS Policies for assignments table
CREATE POLICY "School users can access assignments" ON public.assignments
  FOR ALL USING (
    school_id = public.get_current_user_school_id() OR public.is_super_admin()
  );

-- RLS Policies for results table
CREATE POLICY "School users can access results" ON public.results
  FOR ALL USING (
    school_id = public.get_current_user_school_id() OR public.is_super_admin()
  );

-- Create trigger to auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_schools_updated_at
  BEFORE UPDATE ON public.schools
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

CREATE TRIGGER handle_profiles_updated_at
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

-- Insert sample schools for testing
INSERT INTO public.schools (name, slug, settings) VALUES 
  ('Greenwood High School', 'greenwood', '{"theme": "green", "features": ["attendance", "grades", "assignments"]}'),
  ('Riverside Academy', 'riverside', '{"theme": "blue", "features": ["attendance", "grades", "assignments", "communications"]}'),
  ('Sunset Elementary', 'sunset', '{"theme": "orange", "features": ["attendance", "basic_grades"]}');
