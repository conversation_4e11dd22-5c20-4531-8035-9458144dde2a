-- Add department column to subjects table for simplified department management
-- This allows storing department names directly without requiring a separate departments table

ALTER TABLE subjects 
ADD COLUMN department VARCHAR(255);

-- Add index for department filtering
CREATE INDEX idx_subjects_department ON subjects(department);

-- Update existing subjects to have a default department if needed
UPDATE subjects 
SET department = 'General' 
WHERE department IS NULL AND department_id IS NULL;

-- Add comment to explain the column
COMMENT ON COLUMN subjects.department IS 'Simple department name for subject categorization (optional)';
